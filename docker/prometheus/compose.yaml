services:
  prometheus:
    image: prom/prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
    networks:
      - monitoring

  otel-collector:
    image: otel/opentelemetry-collector
    container_name: otel-collector
    ports:
      - "4317:4317" # OTLP gRPC
      - "4318:4318" # OTLP HTTP
    volumes:
      - ./otel-collector-config.yml:/etc/otel-collector-config.yml
    command:
      - "--config=/etc/otel-collector-config.yml"
    networks:
      - monitoring

networks:
  monitoring:
