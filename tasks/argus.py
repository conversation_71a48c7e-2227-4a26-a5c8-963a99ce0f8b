from typing import List, Optional

from celery import shared_task

from lib.clients.argus import ArgusClient
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.constants import (
    get_error_lark_template,
    get_lark_post_message,
)
from log import logger

argus_config = config.lark.apps['argus']
lark_client = LarkClient.from_config(argus_config)


# @shared_task(ignore_result=False)
# def p2p_chat_by_dify(content: str, message_id: str, session_id: str, user_id: str = ''):
#     # 1. 首先更新消息: "正在处理中..."
#     _, lark_msg_response = lark_client.reply(
#         message_id,
#         format_message_card(
#             config.lark.card.template_id,
#             footer='正在思考中...',
#         ),
#         message_type='interactive',
#         reply_in_thread=True,
#     )

#     # 1. 加载会话 id
#     lark_session_id = session_id or lark_msg_response.data.thread_id
#     chat_obj = ChatService.add_chat(lark_session_id, content, None)

#     # 1. Dify 处理
#     dify_response = dify_invoke(
#         content,
#         user=user_id or 'lark-user',
#         session_id=chat_obj.universal_id or '',
#     )

#     # 1.1 获取生成的结果
#     ok, answer = dify_response
#     if not ok:
#         lark_client.patch(
#             lark_msg_response.data.message_id,
#             content=get_error_lark_template('处理消息失败', answer),
#         )
#         return

#     # 2. 更新生成的结果(Patch)
#     logger.debug(f'dify_p2p_chat ai message, id = {message_id}: {answer}')

#     # 2.1 更新会话 id到数据库
#     dify_conversation_id = answer['conversation_id']
#     if not chat_obj.universal_id and dify_conversation_id:
#         chat_obj.universal_id = dify_conversation_id
#         chat_obj.save()

#     # 2.2 更新飞书消息
#     metadata = answer.get('metadata')
#     if metadata:
#         total_tokens = metadata['usage']['total_tokens']
#         total_price = metadata['usage']['total_price']
#         currency = metadata['usage']['currency']

#         lark_client.patch(
#             lark_msg_response.data.message_id,
#             content=get_lark_card_template(
#                 body=answer['answer'],
#                 footer=f'本次消耗 {total_tokens} tokens, {total_price} {currency}',
#                 as_str=True,
#             ),
#         )
#     else:
#         lark_client.patch(
#             lark_msg_response.data.message_id,
#             content=get_lark_card_template(
#                 body=answer['answer'] or '**没有回复**',
#                 footer='AI 抽风了',
#                 as_str=True,
#             ),
#         )


@shared_task(ignore_result=False)
def group_chat(
    content: str,
    message_id: str,  # 从这里获取到的消息ID
    parent_id: str = '',
    # content_type: Literal['complete', 'predict', 'rootcause'] = 'complete',
    at_list: Optional[List[str]] = None,
):
    # if content_type not in ['complete', 'predict', 'rootcause']:
    #     # reply_content = '我还是一个孩子，啥也不懂~'
    #     # lark_client.reply(message_id, json.dumps({'text': reply_content}))
    #     logger.warning(f'content_type: `{content_type}` is not supported')
    #     return

    parent_message_id = parent_id
    logger.debug(f'parent message id is: {parent_message_id}')

    # 1. 获取父消息的内容
    # try:
    #     parent_message = lark_client.query(parent_message_id)
    # except Exception as e:
    #     lark_client.reply(
    #         message_id,
    #         get_error_lark_template('处理消息失败', str(e), as_str=True),
    #         message_type='post',
    #     )
    #     return

    # 1.1 处理父消息称纯文本
    # parent_content = extract_message(parent_message.data.items[0])
    # logger.debug('extracted parent_message: {}', parent_content)

    # 2. 处理消息
    try:
        final_content = []

        # if content_type == 'complete':
        #     final_content.append([{'tag': 'text', 'text': alert_complete(parent_content)}])

        # elif content_type == 'predict':
        #     predict_result, metainfo = alert_predict(parent_content)
        #     final_content.append([{'tag': 'md', 'text': predict_result}])

        #     if metainfo:
        #         image_key = lark_client.upload_image(metainfo['image'])
        #         final_content.append([{'tag': 'img', 'image_key': image_key}])

        if not parent_message_id:
            logger.warning('No parent_message_id provided.')
            final_content.append([{'tag': 'text', 'text': '请在报错消息上@我'}])
        else:
            logger.debug(f'starting rootcause with parent_message_id: {parent_message_id}')

            # call argus api
            argus_client = ArgusClient.from_config(config.rootcause)
            argus_response = argus_client.get('/rootcause', params={'messageID': parent_message_id})
            final_content.append([{'tag': 'md', 'text': str(argus_response)}])

        if at_list:
            final_content.append([{'tag': 'at', 'user_id': str(at)} for at in at_list])

        lark_client.reply(
            message_id,
            get_lark_post_message(final_content, as_str=True),
            message_type='post',
        )
    except Exception as e:
        import traceback

        traceback.print_exc(limit=5)

        lark_client.reply(
            message_id,
            get_error_lark_template('处理异常', str(e), as_str=True),
            message_type='post',
        )
