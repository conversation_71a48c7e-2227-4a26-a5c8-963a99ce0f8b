import os
from typing import Optional

from celery import shared_task

from lib.agents import hgame_summary
from lib.check.hgame_check import <PERSON><PERSON><PERSON><PERSON><PERSON>
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.configs.lark import LarkAppConfig
from lib.utils import timerange
from lib.utils.pipe import <PERSON>pe<PERSON>ict
from log import logger


@shared_task(ignore_result=False)
def run_check(app_token: str, table_id: Optional[str] = None):
    print(f'auto create bitable, with app_token: {app_token}, table_id: {table_id}, ')

    app_config: LarkAppConfig = PipeDict(config.lark.apps) | 'aiops'
    lark_client = LarkClient.from_config(app_config)
    bitable_app = lark_client.bitable_app(app_token)

    checklist_table_id = table_id or bitable_app.get_table(config.lark.bitable_name).table_id

    new_table_id, view_id = bitable_app.copy_table(
        checklist_table_id,
        timerange.now_string('Y-MM-DD-HHmm'),
        True,
        with_conds=[
            {'field_name': '数据源', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': '任务', 'operator': 'isNotEmpty', 'value': []},
        ],
    )
    if not new_table_id:
        return {'error': 'copy table failed'}

    logger.debug(f'today_table_id : {new_table_id}')

    # ai check start
    checker = HgameChecker(lark_client, os.environ.copy())
    checker.check(bitable_app, new_table_id)

    # get new table's user column
    # user_ids = bitable_app.get_view_column(new_table_id, view_id, '通知人', 'user_id')
    link = f'https://lilithgames.feishu.cn/base/{app_token}?table={new_table_id}&view={view_id}'

    rows = bitable_app.get_view_columns(
        new_table_id,
        view_id,
        ['任务', '数据', '通知人'],
        {'任务': 'text', '数据': 'text'},
    )
    for row in rows:
        print('row:', row)
        task = row['任务']
        data = row.get('数据', '')
        users = row.get('通知人')

        if not data or not users:
            continue

        summary = hgame_summary.invoke_struct(task, data)

        user_open_ids = [u['id'] for u in users]
        response = lark_client.batch_send_message(
            receivers=user_open_ids,
            title=summary.title,
            subtitle=summary.timerange,
            tags=['Hgame'],
            content=f'{summary.content}\n\n[查看详情]({link})',
        )
        user_emails = [u['email'] for u in users]
        logger.info(f'send message to users: {user_emails}, link is {link}, response is {response}')
