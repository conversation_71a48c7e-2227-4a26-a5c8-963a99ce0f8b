import os
from typing import Optional

from celery import shared_task

from lib.check.mona_check import <PERSON><PERSON><PERSON><PERSON>
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.configs.lark import LarkAppConfig
from lib.utils import timerange
from lib.utils.pipe import PipeDict
from log import logger

VIEW_LINK_TEMPLATE = (
    'https://lilithgames.feishu.cn/base/{app_token}?table={table_id}&view={view_id}'
)


@shared_task(ignore_result=False)
def run_check(app_token: str, table_id: Optional[str] = None, notify: bool = True):
    print(f'auto create bitable, with app_token: {app_token}, table_id: {table_id}, ')

    app_config: LarkAppConfig = PipeDict(config.lark.apps) | 'aiops'
    lark_client = LarkClient.from_config(app_config)
    bitable_app = lark_client.bitable_app(app_token)

    checklist_table_id = table_id or bitable_app.get_table(config.lark.bitable_name).table_id

    new_table_id, view_id = bitable_app.copy_table(
        checklist_table_id,
        timerange.now_string('Y-MM-DD-HHmm'),
        True,
        with_conds=[
            {'field_name': '数据源', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': '巡检方法', 'operator': 'isNotEmpty', 'value': []},
        ],
    )
    if not new_table_id:
        return {'error': 'copy table failed'}

    logger.debug(f'mona new today_table_id : {new_table_id}')

    # ai check start
    checker = MonaChecker(lark_client, os.environ.copy())
    checker.check(bitable_app, new_table_id)

    # get new table's user column
    link = VIEW_LINK_TEMPLATE.format(app_token=app_token, table_id=new_table_id, view_id=view_id)

    rows = bitable_app.get_view_columns(
        new_table_id,
        view_id,
        ['数据源', '巡检结果', '通知人'],
        {'数据源': 'link_text', '巡检结果': 'singleselect'},
        conds=[
            # {'field_name': '巡检结果', 'operator': 'contains', 'value': ['不正常']},
            {'field_name': '通知人', 'operator': 'isNotEmpty', 'value': []},
        ],
    )

    if notify:
        user_open_ids = [u['id'] for u in rows[0]['通知人']]
        user_emails = [u['email'] for u in rows[0]['通知人']]

        invalid_rows = [r for r in rows if r.get('巡检结果') != '正常']

        if invalid_rows:
            summary = (
                '今日巡检完毕,'
                f'其中<font color=red>{len(invalid_rows)}</font>条异常, '
                f'<font color=green>{len(rows) - len(invalid_rows)}</font>条正常!'
            )

            response = lark_client.batch_send_message(
                receivers=user_open_ids,
                title='巡检结果通知',
                header_color='red',
                tags=['Mona'],
                content=f'{summary}\n\n[详情请查看]({link})',
            )
            logger.info(
                f'send message to users: {user_emails}, link is {link}, response is {response}'
            )
        else:
            summary = f'今日共巡检{len(rows)}条, 未发现异常'
            response = lark_client.batch_send_message(
                receivers=user_open_ids,
                header_color='green',
                title='巡检结果通知',
                tags=['Mona'],
                content=f'{summary}\n[详情请查看]({link})',
            )
            logger.info(
                f'send message to users: {user_emails}, link is {link}, response is {response}'
            )
