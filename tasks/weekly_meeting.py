from celery import Task, shared_task

from lib.agents.weely_wiki import invoke
from lib.clients.lark import LarkClient
from lib.configs import config
from log import logger


@shared_task(ignore_result=False, bind=True)
def create_weekly_meeting_wiki(
    self: 'Task',
    weekly_meeting_day: str = '本周五',
    notify: bool = True,
):
    success, result = invoke(weekly_meeting_day)
    logger.info(f'success: {success}, result: {result}')

    if not success and ('auth' in result or 'fail' in result):
        delay = 2**self.request.retries
        self.retry(exc=Exception('auth error'), countdown=delay, max_retries=3)
        return

    if notify and success:
        lark_client = LarkClient.from_config(config.lark.apps['aiops'])

        content = f'{result.link}已创建, 请大家及时填写 <at user_id="all"></at>'
        lark_client.send_text_message(
            config.lark.service_account.weekly_wiki_report_group,
            content=content,
            receive_id_type='chat_id',
        )


if __name__ == '__main__':
    create_weekly_meeting_wiki(notify=False)
