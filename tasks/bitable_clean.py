from typing import Literal

from celery import shared_task

from lib.agents.bitable_cleaner import invoke
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.configs.lark import LarkAppConfig
from lib.utils.pipe import PipeDict
from log import logger


@shared_task(ignore_result=False)
def clean_outdated_tables(
    app_token: str,
    deadline: Literal['一个月前', '一周前', '两周前'] = '一个月前',
    notify: bool = True,
):
    result = invoke(bitable_app=app_token, task=f'删除{deadline}的表格')
    logger.info(f'bitable clean result: {result}')

    if notify and config.admins:
        app_config: LarkAppConfig = PipeDict(config.lark.apps) | 'aiops'
        lark_client = LarkClient.from_config(app_config)

        user_ids = lark_client.get_user_ids(config.admins)
        if user_ids:
            lark_client.batch_send_message(
                user_ids,
                title='清理过期多维表格',
                header_color='green',
                tags=[deadline],
                content=result,
            )


if __name__ == '__main__':
    clean_outdated_tables(
        app_token=config.lark.bitables['mona'],
        deadline='一个月前',
        notify=False,
    )
