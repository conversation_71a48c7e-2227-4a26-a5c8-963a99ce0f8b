import lark_oapi as lark
import pendulum
import redis
from celery import shared_task

from lib.clients.lark import LarkClient
from lib.clients.lark.oauth import AuthorizationError, TokenRefreshError
from lib.configs import config
from log import logger

TOKEN_KEY = config.lark.service_account.redis_token_key
TTL_REFRESH_THRESHOLD = 60 * 20  # 20 minutes - minimum TTL to skip refresh


def reduce_ttl_with_gap(ttl: int, gap: int = 10) -> int:
    """对 ttl 进行偏移处理， 例如减少 10s."""
    return max(60, ttl - gap)  # 保证最小为60秒，避免过期时间为0导致的错误


@shared_task(ignore_result=True)
def refresh_user_access_token():
    """refresh user access token,
    and delay the next refresh,
    stop if token not found or refresh failed.
    """
    redis_client = redis.from_url(
        config.lark.service_account.user_token_storage, decode_responses=True
    )

    # check if token exists
    token_cached = redis_client.hgetall(TOKEN_KEY)
    if not token_cached:
        return f'{TOKEN_KEY} not found, skip refresh'

    # check if token is expired
    ttl = redis_client.ttl(TOKEN_KEY)
    if ttl and ttl > TTL_REFRESH_THRESHOLD:  # 20 minutes left - skip refresh if TTL is sufficient
        return None

    logger.debug(f'{TOKEN_KEY} last refreshed at {token_cached.get("refreshed_at")}, refresh now.')

    # refresh user access token
    lark_client = LarkClient(lark.Client().builder().build())
    try:
        new_token = lark_client.refresh_user_access_token(
            client_id=token_cached['app_id'],
            client_secret=token_cached['app_secret'],
            refresh_token=token_cached['refresh_token'],
        )
    except TokenRefreshError as e:
        return f'failed to refresh user access token, {e.message}'

    # update token in storage
    new_token.update({'refreshed_at': pendulum.now().to_rfc3339_string()})
    redis_client.hset(TOKEN_KEY, mapping=new_token)
    redis_client.expire(TOKEN_KEY, reduce_ttl_with_gap(new_token['expires_in']))
    logger.info(f'new {TOKEN_KEY} refreshed')


@shared_task(ignore_result=True)
def authorized(authorized_code: str, app_id: str, app_secret: str):
    """处理授权码并获取用户访问令牌，成功后自动启动刷新任务"""
    # get user access token
    try:
        data = LarkClient.get_user_access_token(authorized_code, app_id, app_secret)
    except AuthorizationError as e:
        logger.error(f'failed to get user access token for code {authorized_code}, {e.message}')
        return f'failed to get user access token for code {authorized_code}, {e.message}'

    # append app_id and app_secret to data
    data.update(
        {
            'app_id': app_id,
            'app_secret': app_secret,
        }
    )

    # store user access token
    redis_client = redis.from_url(config.lark.service_account.user_token_storage)
    redis_client.hset(TOKEN_KEY, mapping=data)
    redis_client.expire(TOKEN_KEY, reduce_ttl_with_gap(data['expires_in']))

    logger.info('user access token stored successfully')
    # 注意：刷新任务现在通过 link 机制自动触发，无需在此处手动调用

    return TOKEN_KEY
