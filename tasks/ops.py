import os
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List

import pendulum
from celery import shared_task
from imap_tools.message import MailMessage
from pydantic import ValidationError

from lib.agents import copilot_email
from lib.clients.github import GithubClient
from lib.clients.lark import LarkClient
from lib.clients.lark.bitable import Conjunction
from lib.clients.mail import Criteria, EmailClient
from lib.configs import config
from lib.utils import timerange
from log import logger


@dataclass
class GithubAccount:
    """Github account info"""

    login: str = field(default='')
    id: int = field(default=-1)
    record_id: int = field(default=-1)


@shared_task(ignore_result=False)
def clean_store(store_path: str, hours: int = 8):
    if not os.path.isdir(store_path):
        return

    threshold = time.time() - (hours * 3600)

    files_deleted = 0
    dirs_deleted = 0

    for root, dirs, files in os.walk(store_path):
        for f in files:
            file_path = os.path.join(root, f)
            if os.path.getmtime(file_path) < threshold:
                os.remove(file_path)
                # logger.info(f'delete file: {file_path}')
                files_deleted += 1

        for d in dirs:
            dir_path = os.path.join(root, d)
            if not os.listdir(dir_path) and os.path.getmtime(dir_path) < threshold:
                os.rmdir(dir_path)
                # logger.info(f'delete directory {dir_path}')
                dirs_deleted += 1

    if files_deleted or dirs_deleted:
        logger.info(f'deleted {files_deleted} files and {dirs_deleted} directories in {store_path}')


class CopilotLark(object):
    def __init__(self, lark: LarkClient, gh: GithubClient):
        self.lark = lark
        self.gh = gh

    def list_records(
        self,
        conditions: List[Dict[str, Any]],
        conjunction: Conjunction = 'and',
    ):
        table = self.lark.bitable_app(config.github.bitable_app)
        return table.records(
            table_id=config.github.bitable_table,
            view_id=config.github.view_id,
            conds=conditions,
            conjunction=conjunction,
        )

    def pending_records(self):
        conds = [
            {
                'field_name': '状态',
                'operator': 'is',
                'value': ['已邀请'],
            },
        ]
        return self.list_records(conds)

    def invite_pending_records(self):
        conds = [
            {
                'field_name': '状态',
                'operator': 'is',
                'value': ['待邀请'],
            },
        ]
        return self.list_records(conds)

    def malformed_records(self):
        conds = [
            {
                'field_name': 'GithubUserId',
                'operator': 'isEmpty',
                'value': [],
            },
        ]
        return self.list_records(conds)


def invite_user_to_github_org(email_content: str):
    try:
        response = copilot_email.invoke(email_content, stream=True)

        app_config = config.lark.apps['aiops']
        lark_client = LarkClient.from_config(app_config)

        # invite user to Github copilot
        github_client = GithubClient.from_config(config.github)
        bitable = lark_client.bitable_app(config.github.bitable_app)

        github_user = github_client.search_user(response.github_user)
        if not github_user:
            raise ValueError(f'github user {response.github_user} not exists')

        # user's open_id
        receivers = [response.email] + config.admins
        try:
            users_contact = lark_client.batch_get_users_contact(receivers)
            logger.debug('receivers: {}', users_contact.keys())

            user_open_ids = [c.user_id for c in users_contact.values()]
        except Exception as e:
            logger.error('Failed to get user contacts: {}', e)
            return

        is_member = github_client.org.has_in_members(github_user)
        if not is_member:
            github_client.invite_user(
                github_user,
                team_name=response.github_team,
            )

            msg_id = lark_client.batch_send_message(
                user_open_ids,
                title='Github Copilot 邀请',
                content=(
                    # f'{response.request_user}同学, 你好:\n'
                    f'<at email={response.email}></at>同学, 你好:\n'
                    f'在使用 Copilot 之前, 请以 **{github_user.login}** 账号登录Github,'
                    f'并点击[「加入{config.github.org}组织」](https://github.com/orgs/{config.github.org}/invitation?via_email=1)'
                ),
            )
            logger.debug(f'send notify message to feishu, msg_id is {msg_id}')

        # 更新 bitable表格, create/update record(状态: 已邀请)
        record_found = bitable.get_record(
            config.github.bitable_table,
            config.github.view_id,
            conds=[
                {
                    'field_name': 'GithubAccount',
                    'operator': 'is',
                    'value': [github_user.login],
                },
                {
                    'field_name': 'GithubUserId',
                    'operator': 'is',
                    'value': [github_user.id],
                },
            ],
            conjunction='or',
        )
        if record_found:
            # update record
            bitable.update_record(
                config.github.bitable_table,
                record_found.record_id,
                new_fields={
                    '状态': '已邀请',
                    '更新时间': timerange.now_ns(),
                    '备注': response.reason,
                },
            )
        else:
            # create a record
            bitable.create_record(
                config.github.bitable_table,
                {
                    'GithubAccount': github_user.login,
                    'GithubUserId': github_user.id,
                    '人员': [{'id': users_contact[response.email].user_id}],
                    '公司邮箱': response.email,
                    '添加时间': timerange.now_ns(),
                    '团队': response.github_team,
                    '备注': response.reason,
                    '状态': '已邀请',
                },
            )
    except ValidationError as e:
        user_open_ids = lark_client.get_user_ids(config.admins)
        lark_client.batch_send_message(
            user_open_ids,
            title='Copilot 邀请失败',
            content=f'email_content: {email_content}\n\nException: {e}',
        )


def list_pending_copilot_requests(
    folder: str = 'Copilot',
    criteria: Criteria = 'NOT SEEN',
    gap: int = 10,
):
    mailbox = EmailClient.from_config(config.email)

    pendings: List[MailMessage] = []

    def valid_mail_sender(m: MailMessage):
        return 'Copilot权限申请流程' in m.subject and m.from_ == '<EMAIL>'

    now = pendulum.now('Asia/Shanghai')

    def mail_handler(m: MailMessage):
        dt = pendulum.from_timestamp(m.date.timestamp(), tz='Asia/Shanghai')
        # 超过 gap 的邮件不再处理
        if dt.diff(now).in_minutes() > gap:
            return False

        if valid_mail_sender(m):
            pendings.append(m)
            logger.debug(f' {m.uid} {dt.format("YYYY-MM-DD HH:mm:ss")}  {m.subject} , {m.from_}')
        # else:
        #     print(f' {m.uid} {dt.format("YYYY-MM-DD HH:mm:ss")}  {m.subject}')
        return True

    mailbox.fetch_mails(handler=mail_handler, folder=folder, criteria=criteria)

    return pendings


@shared_task(ignore_result=False, queue='copilot')
def accept_copilot_request():
    for mail in list_pending_copilot_requests(folder=config.email.folder, criteria='ALL', gap=10):
        invite_user_to_github_org(mail.text)


@shared_task(ignore_result=False, queue='copilot')
def assign_github_copilot_seats():
    gh_config = config.github

    lark_client = LarkClient.from_config(config.lark.apps['aiops'])
    github_client = GithubClient.from_config(gh_config)
    copilot = CopilotLark(lark=lark_client, gh=github_client)

    copilot_bitable = lark_client.bitable_app(gh_config.bitable_app)

    # 1. get all pending invitations
    pending_records = copilot.pending_records()

    # 2. check the user exists and already in the org/team
    for record in pending_records:
        # 2.1. check github user
        user_login = record.fields.get('GithubAccount')[0]['text']
        user = github_client.search_user(user_login)

        if not user:
            copilot_bitable.update_record(
                table_id=gh_config.bitable_table,
                record_id=record.record_id,
                new_fields={'状态': '账户不存在'},
            )
            continue

        # 2.2. check the user membership with the org/team
        is_member = github_client.org.has_in_members(user)
        if not is_member:
            # logger.warning(f'github user {user_login} not in the org, skip')
            continue

        # 2.3. add seat for the user
        success, info = github_client.add_seats([user])
        if success:
            logger.info(f'add seats success for {user.login}')
        else:
            logger.error(f'add seats failed for {user.login}')

        # 2.4. update the record status
        if success:
            copilot_bitable.update_record(
                table_id=gh_config.bitable_table,
                record_id=record.record_id,
                new_fields={
                    'GithubAccount': user.login,
                    '状态': '启用',
                    '更新时间': timerange.now_ns(),
                },
            )
            if info.get('seats_created') > 0:
                feishu_user_id = record.fields.get('人员')[0]['id']
                admin_user_ids = lark_client.get_user_ids(config.admins)

                receivers = [feishu_user_id] + admin_user_ids

                lark_client.batch_send_message(
                    receivers=receivers,
                    title='🎉 Github Copilot 开通啦',
                    header_color='green',
                    content=(
                        f'恭喜你, <at id={feishu_user_id}></at>! Github Copilot 可以使用了!'
                        '\n<font color="red">**注意: 1个月不活跃任将会被取消资格**</font>'
                    ),
                )
        else:
            logger.error(f'failed to add seats for {user.login}', color='red')


@shared_task(ignore_result=False)
def refresh_github_user_info():
    app_config = config.lark.apps['aiops']
    gh_config = config.github

    lark_client = LarkClient.from_config(app_config)
    github_client = GithubClient.from_config(gh_config)
    copilot = CopilotLark(lark=lark_client, gh=github_client)

    copilot_bitable = lark_client.bitable_app(gh_config.bitable_app)

    # 1. get all pending invitations
    records = copilot.malformed_records()

    # 2. check the user exists and already in the org/team
    for record in records:
        # 2.1. check the user existence
        user_login = record.fields.get('GithubAccount')[0]['text']

        user = github_client.search_user(user_login)
        if not user:
            copilot_bitable.update_record(
                table_id=gh_config.bitable_table,
                record_id=record.record_id,
                new_fields={'状态': '账户不存在'},
            )
            continue

        # 2.2. check the user in the org/team
        is_member = github_client.org.has_in_members(user)
        if not is_member:
            continue

        # 2.3 figure out team name(not slug)
        # print('teams url:', user.invitation_teams_url)

        # 2.4. update the record status
        copilot_bitable.update_record(
            table_id=gh_config.bitable_table,
            record_id=record.record_id,
            new_fields={
                'GithubAccount': user.login,
                'GithubUserId': user.id,
                '更新时间': timerange.now_ns(),
            },
        )


@shared_task(ignore_result=False, queue='copilot')
def cancel_seats_inactive(month: int = 1):
    """取消最近一段时间不活跃的 Copilot 座位"""
    client = GithubClient(config.github.access_token, config.github.org)

    now = pendulum.now('Asia/Shanghai')

    # if now.last_of('month').day != now.day:
    #     print('not the last day of month, skip')
    #     return

    users_to_cancel = []

    for seat in client.list_seats():
        user_login = seat['assignee']['login']
        pending_cancellation_dt = seat['pending_cancellation_date']

        if not pending_cancellation_dt:
            last_activity_time = (
                seat['last_activity_at'] if seat['last_activity_at'] else seat['created_at']
            )
            last_activity_dt = pendulum.parse(last_activity_time)

            diff_month = last_activity_dt.diff(now).in_months()  # type: ignore
            if diff_month >= month:
                logger.info(
                    f'{seat["assignee"]["login"]}'
                    f' uid: {seat["assignee"]["id"]}, '
                    f' last_activity: {seat["last_activity_at"]},'
                    f' created_at: {seat["created_at"]}  diff month: {diff_month}'
                )
                users_to_cancel.append(user_login)

    lark_config = config.lark.apps['aiops']
    lark_client = LarkClient.new(lark_config.app_id, lark_config.app_secret)

    if users_to_cancel:
        client.cancel_seats(users_to_cancel)

        user_open_ids = lark_client.get_user_ids(config.admins)
        lark_client.batch_send_message(
            title='Copilot非活跃账号回收通知',
            content=f'本次回收 **{len(users_to_cancel)}** 个账户的Copilot坐席',
            receivers=user_open_ids,
            header_color='yellow',
        )
    # else:
    #     ok, msg = lark_client.batch_send_message(
    #         receivers=user_open_ids,
    #         title='Copilot非活跃用户回收通知',
    #         content=f'本次回收 **{len(cancel_users)}** 个账户的Copilot权限',
    #     )
    #     print(f'ok: {ok}, msg: {msg}')
    # 3. update the record status
    # 4. notify the user if needed


@shared_task(ignore_result=False, queue='copilot')
def sync_seat_status():
    """sync github copilot seat to bitable"""
    app_config = config.lark.apps['aiops']
    gh_config = config.github

    lark_client = LarkClient.from_config(app_config)
    github_client = GithubClient.from_config(gh_config)

    copilot_bitable = lark_client.bitable_app(gh_config.bitable_app)

    # 1. get all records from bitable, and delete users exclude staff
    all_users = {}

    for record in copilot_bitable.records(
        table_id=gh_config.bitable_table,
        view_id=gh_config.view_id,
        conds=[
            {'field_name': 'GithubAccount', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': 'GithubUserId', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': '人员', 'operator': 'isNotEmpty', 'value': []},
            {
                'field_name': '状态',
                'operator': 'doesNotContain',
                # 'operator': 'contains',
                'value': [
                    '已离职',
                    '已邀请',
                ],
            },
        ],
    ):
        gh_user_login = record.fields.get('GithubAccount')[0]['text']
        gh_user_id = record.fields.get('GithubUserId', '')

        feishu_email_field = record.fields.get('公司邮箱')
        if not feishu_email_field:
            continue
        feishu_email = feishu_email_field[0]['text']

        try:
            feishu_user_id = lark_client.get_user_id(feishu_email)
            if not feishu_user_id:
                # 从飞书表格中删除
                # copilot_bitable.remove_record(gh_config.bitable_table, record.record_id)

                copilot_bitable.update_record(
                    table_id=gh_config.bitable_table,
                    record_id=record.record_id,
                    new_fields={
                        '状态': '已离职',
                        '更新时间': timerange.now_ns(),
                    },
                )

                # 从 github org 中删除成员(包括正在邀请中的)
                github_client.remove_membership(gh_user_id)
                logger.info(f'remove {gh_user_id} from org')
            else:
                user = GithubAccount(gh_user_login, gh_user_id, record_id=record.record_id)
                all_users[gh_user_id] = user
        except Exception as e:
            logger.error('handle email: {}, exception: {}', feishu_email, str(e))

    # 2. sync all seats from github
    for seat in github_client.list_seats():
        gh_login = seat['assignee']['login']
        gh_user_id = int(seat['assignee']['id'])

        pending_cancellation_dt = seat['pending_cancellation_date']
        seat_status = '启用' if not pending_cancellation_dt else '回收中'

        # 计算用户最后活跃程度
        # 满分 5 分, 每个月不活跃 -1 分, 最小 0 分
        activity_score = 5
        if seat.get('last_activity_at'):
            last_activity_time = pendulum.parse(seat['last_activity_at'])
            inactive_months = last_activity_time.diff(pendulum.now('Asia/Shanghai')).in_months()
            activity_score = max(0, activity_score - inactive_months)
        else:
            activity_score = 0

        # 2.1 if not found, create a new record
        gh_account_record = all_users.pop(gh_user_id, None)
        if not gh_account_record:
            logger.warning(f'found a new github user {gh_login} with id {gh_user_id}')
            # logger.debug(
            #     f'create new record for github user {gh_user_id}, type is {type(gh_user_id)}'
            # )
            # copilot_bitable.create_record(
            #     table_id=gh_config.bitable_table,
            #     record={
            #         'GithubAccount': gh_login,
            #         'GithubUserId': gh_user_id,
            #         '添加时间': pendulum.parse(seat['created_at']).int_timestamp * 1000,
            #         '状态': seat_status,
            #         '活跃程度': activity_score,
            #         '更新时间': timerange.now_ns(),
            #     },
            # )

        # 2.2 if found, update record
        else:
            copilot_bitable.update_record(
                table_id=gh_config.bitable_table,
                record_id=gh_account_record.record_id,
                new_fields={
                    'GithubAccount': gh_login,
                    'GithubUserId': gh_user_id,
                    '添加时间': pendulum.parse(seat['created_at']).int_timestamp * 1000,
                    '状态': seat_status,
                    '活跃程度': activity_score,
                    '更新时间': timerange.now_ns(),
                },
            )

    # 3. 未处理的用户标记为禁用
    if all_users:
        logger.debug(f'mark to inactive for {len(all_users)} users: {all_users.keys()}')
        copilot_bitable.batch_update_records(
            table_id=gh_config.bitable_table,
            records=[
                {
                    'record_id': user.record_id,
                    'fields': {
                        '状态': '未启用',
                        '更新时间': timerange.now_ns(),
                    },
                }
                for user in all_users.values()
            ],
        )


def list_records():
    app_config = config.lark.apps['aiops']
    gh_config = config.github

    lark_client = LarkClient.from_config(app_config)
    bitable = lark_client.bitable_app(gh_config.bitable_app)

    records = bitable.records(
        table_id=gh_config.bitable_table,
        view_id=gh_config.view_id,
        conds=[
            {
                'field_name': '人员',
                'operator': 'isNotEmpty',
                'value': [],
            },
        ],
    )
    for record in records:
        # print(record.fields)
        user_info = record.fields.get('人员')[0]
        user_corp_email = record.fields.get('公司邮箱')
        print(user_info, user_corp_email)
        bitable.update_record(
            table_id=gh_config.bitable_table,
            record_id=record.record_id,
            new_fields={'公司邮箱': user_info['email']},
        )


if __name__ == '__main__':
    # assign_github_copilot_seats()
    # refresh_github_user_info()
    # cancel_seats_not_actively()
    # sync_seat_status()
    # list_records()
    # accept_copilot_request()
    # from lib.constants import DATA_ROOT

    # with open(DATA_ROOT / 'copilot.txt', 'r') as f:
    #     invite_user_to_github_org(f.read())
    pass
