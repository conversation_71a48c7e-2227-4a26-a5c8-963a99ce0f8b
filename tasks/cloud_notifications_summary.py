import json
from typing import List

import pendulum
from celery import shared_task

from lib.agents.cloud_notification_pre_cate import invoke
from lib.agents.cloud_notification_summary import summary
from lib.clients.lark import LarkClient
from lib.clients.lark.parsers.message import extract_message
from lib.clients.lark.templates.cloud_message_summary import (
    generate_header,
    generate_section,
    get_cloud_message_summary_card_template,
)
from lib.clients.mongo import MongoClient
from lib.configs import config
from log import logger

SECTION_STYLES = {
    'important_urgent': {'color': 'red', 'icon': 'hot_outlined'},
    'important_non_urgent': {'color': 'orange', 'icon': 'reply-cn_outlined'},
    'urgent_non_important': {'color': 'orange', 'icon': 'reply-cn_outlined'},
    'non_urgent_non_important': {'color': 'orange', 'icon': 'reply-cn_outlined'},
}


@shared_task(ignore_result=False)
def preprocess_cloud_notifications(
    start_time: int,
    end_time: int,
    chat_id: str,
):
    """预处理汇总群里的消息，分类后存入 MongoDB"""
    client = LarkClient.from_config(config.lark.apps['argus'])

    # 获取群组信息
    chat_group = client.get_group_by_id(chat_id)
    if not chat_group:
        logger.error(f'Failed to get group by id: {chat_id}')
        return

    # 获取群组类型
    group_kind = None
    for group in config.cloud_notification_groups:
        if group.chat_id == chat_id:
            group_kind = group.kind or chat_group.name
            break

    if not group_kind:
        logger.error(f'Failed to get group kind for {chat_group.name}/{chat_id} from config')
        return

    # 获取群里的消息
    message_iter = client.list_group_messages(
        chat_id,
        start_time=start_time,
        end_time=end_time,
        sender_types=['app'],
    )

    with MongoClient.from_config(config.mongo) as mgo_client:
        for message in message_iter:
            # 解析消息创建时间
            message_create_time = pendulum.from_timestamp(
                float(message.create_time) / 1000, tz='Asia/Shanghai'
            )

            # # 解析消息内容
            message_content = extract_message(message)

            # # 记录日志
            logger.debug(
                f'Cloud Message:\n'
                f'  ChatGroup: {chat_group.name}\n'
                f'  MessageID: {message.message_id}\n'
                f'  Time: {message_create_time.to_rfc3339_string()}\n'
                f'  Content: {message_content}'
            )

            # 将消息记录添加到 MongoDB
            # 1. 预处理消息
            handle_data = invoke(
                f'{message_content}\nGroup: {chat_group.name}', model_name='deepseek-v3'
            )

            # 2. 存入 MongoDB
            record = {
                'message_id': message.message_id,
                'sender_id': message.sender.id,
                'create_time': message_create_time.int_timestamp,
                'content': message_content,
                'chat_group': chat_group.name,
                'summary': handle_data.summary,
                'priority': handle_data.priority,
                'priority_reason': handle_data.priority_reason,
                'tags': {
                    'group_id': chat_id,
                    'group_kind': group_kind,
                    'message_time': message_create_time.to_rfc3339_string(),
                },
            }
            mgo_client.update_one(
                config.mongo.cloud_notification_collection,
                filter_dict={'message_id': message.message_id},
                update_dict={
                    '$set': record,
                    # 设置过期时间为7天后
                    '$setOnInsert': {'expire_at': message_create_time.add(seconds=3600 * 24 * 7)},
                },
                upsert=True,
            )


@shared_task(ignore_result=False)
def sync_last_hour_messages(*group_ids: List[str]):
    last_hour = pendulum.now('Asia/Shanghai').subtract(hours=1)
    start_time = last_hour.start_of('hour').int_timestamp
    end_time = last_hour.end_of('hour').int_timestamp

    # 如果没有传入群组ID，则默认使用配置文件中的所有群组
    if not group_ids:
        group_ids = [group.chat_id for group in config.cloud_notification_groups]

    for group_id in group_ids:
        preprocess_cloud_notifications.delay(
            start_time=start_time,
            end_time=end_time,
            chat_id=group_id,
        )


@shared_task(ignore_result=False)
def summary_cloud_notifications(days: int = 1):
    """汇总云消息"""

    that_day = pendulum.now('Asia/Shanghai').subtract(days=days)
    start_time = that_day.start_of('day').int_timestamp
    end_time = that_day.end_of('day').int_timestamp
    print(f'start_time: {start_time}, end_time: {end_time}')

    # 按时间升序排序(最老的在前)
    messages = []
    with MongoClient.from_config(config.mongo) as mgo_client:
        result = mgo_client.find(
            config.mongo.cloud_notification_collection,
            filter_dict={'create_time': {'$gte': start_time, '$lte': end_time}},
            projection={'_id': False},
            sort=[('create_time', 1)],
        )

        for message in result:
            # logger.info(f'message: {message}')
            messages.append(f'{message}')

    if not messages:
        logger.warning('No messages need summarize')
        return

    # AI总结
    summary_content = summary(''.join(messages), model_name='deepseek-v3', temperature=0.6)
    # summary_content = summary(''.join(messages), model_name='gpt-4-1')
    # summary_content = summary(''.join(messages), model_name='claude-4')
    # summary_content = summary(''.join(messages))
    logger.info(f'Summary of messages: {summary_content}')

    # 生成飞书消息卡片
    sections = []
    for section_name, section_title in [
        ('important_urgent', '重要紧急'),
        ('important_non_urgent', '重要不紧急'),
        ('urgent_non_important', '紧急不重要'),
        # ('non_urgent_non_important', '不紧急不重要'),
    ]:
        section = getattr(summary_content, section_name)
        if not section:
            continue

        # 生成消息卡片
        style = SECTION_STYLES[section_name]
        sections.append(
            generate_section(
                title=section_title,
                content='\n'.join(f'- {sec}' for sec in section),
                color=style['color'],
                icon=style['icon'],
            )
        )

    card_json = get_cloud_message_summary_card_template(
        header=generate_header(f'昨日({that_day.format("YYYY-MM-DD")})公有云站内信智能汇总'),
        sections=sections,
    )
    card_content = json.dumps(card_json, ensure_ascii=False)

    # 发送到飞书
    lark_client = LarkClient.from_config(config.lark.apps['aiops'])

    for chat_group in config.cloud_summary_notify:
        try:
            lark_client.send_message(
                chat_group.chat_id,
                card_content,
                message_type='interactive',
                receive_id_type='chat_id',
            )
        except Exception as e:
            logger.error(f'Failed to send message to {chat_group.chat_id}: {e}')


if __name__ == '__main__':
    # now = pendulum.now('Asia/Shanghai')
    # start_time = now.start_of('day').int_timestamp
    # end_time = now.int_timestamp
    # print(f'start_time: {start_time}, end_time: {end_time}')

    # that_day = pendulum.now('Asia/Shanghai').subtract(days=1)
    # start_time = that_day.start_of('day').int_timestamp
    # end_time = pendulum.now('Asia/Shanghai').subtract(days=1).end_of('day').int_timestamp

    # # 预处理汇总群里的消息，分类后存入 MongoDB
    # for group in config.cloud_notification_groups:
    #     preprocess_cloud_notifications(
    #         start_time=start_time,
    #         end_time=end_time,
    #         chat_id=group.chat_id,
    #     )
    # sync_last_hour_messages()

    summary_cloud_notifications(1)
