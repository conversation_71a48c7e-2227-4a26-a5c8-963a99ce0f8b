from celery import shared_task

from lib.agents.tools.weather import get_weather
from lib.agents.tools_invoke import invoke_with_tools
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.constants import (
    get_lark_post_message,
)
from lib.llm.models import get_llm
from log import logger
from prompt.weather import PROMPT as WEATHER_PROMPT

aiops_config = config.lark.apps['aiops']

lark_client = LarkClient.from_config(aiops_config)


@shared_task(ignore_result=False)
def group_chat(
    content: str,
    message_id: str,  # 从这里获取到的消息ID
):
    if '天气' in content:
        reply_content = invoke_with_tools(
            get_llm('gpt4o'),
            content,
            tools=[get_weather],
            system_prompt=WEATHER_PROMPT,
        ).content

        # reply_content = baidu.get_weather()
        logger.info('天气查询结果: {}', reply_content)

        final_content = []
        final_content.append([{'tag': 'md', 'text': reply_content}])

        try:
            resp = lark_client.reply(
                message_id,
                get_lark_post_message(final_content, as_str=True),
                message_type='post',
            )
            logger.debug('reply ok, resp = {}', resp.data)
        except Exception as e:
            logger.error(f'Error replying to message {message_id}: {e}')
