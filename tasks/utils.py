import redis

from lib.configs import config


def load_user_access_token() -> str | None:
    """load user access token from redis"""
    redis_client = redis.from_url(
        config.lark.service_account.user_token_storage,
        decode_responses=True,
    )
    token_cached = redis_client.hgetall(config.lark.service_account.redis_token_key)
    if not token_cached:
        return None

    return token_cached.get('access_token')
