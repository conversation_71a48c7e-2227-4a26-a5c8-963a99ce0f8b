import os
from typing import Optional

from celery import shared_task

from lib.check.ad_checker import <PERSON><PERSON><PERSON><PERSON>
from lib.clients.lark import LarkClient
from lib.configs import config
from lib.configs.lark import LarkAppConfig
from lib.utils import timerange
from lib.utils.pipe import PipeDict


@shared_task(ignore_result=False)
def run_check(app_token: str, table_id: Optional[str] = None):
    print(f'auto create bitable, with app_token: {app_token}, table_id: {table_id}, ')

    app_config: LarkAppConfig = PipeDict(config.lark.apps) | 'aiops'
    lark_client = LarkClient.from_config(app_config)
    bitable_app = lark_client.bitable_app(app_token)

    checklist_table_id = table_id or bitable_app.get_table(config.lark.bitable_name).table_id

    new_table_id, new_view_id = bitable_app.copy_table(
        checklist_table_id,
        timerange.now_string('Y-MM-DD-HHmm'),
        True,
        with_conds=[
            {'field_name': '数据源', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': '巡检方法', 'operator': 'isNotEmpty', 'value': []},
        ],
    )
    if not new_table_id:
        return {'error': 'copy table failed'}

    print(f'today_table_id : {new_table_id}')

    # ai check start
    checker = AdChecker(lark_client, os.environ.copy())
    checker.check(bitable_app, new_table_id)
