from dotenv import load_dotenv
from flask import Blueprint, Flask

from commands import registry_commands
from exts import ext_celery, ext_cors

load_dotenv()


def create_app() -> Flask:
    """创建并配置 Flask 应用实例。

    Returns:
        Flask: 配置好的 Flask 应用实例。
    """
    app = Flask(__name__)
    app.instance_path = app.root_path

    app.url_map.strict_slashes = False
    app.config.from_prefixed_env()

    # extensions
    init_extensions(app)

    # blueprints
    init_blueprints(app)

    # cusom commands
    registry_commands(app)

    return app


def init_extensions(app: Flask):
    """初始化 Flask 扩展。

    Args:
        app (Flask): Flask 应用实例
    """
    ext_celery.init_app(app)
    ext_cors.init_app(app)


def init_blueprints(app: Flask):
    from lib.api.feishu_oauth import bp as feishu_oauth_bp
    from lib.api.pdf import bp as pdf_bp
    from lib.api.rootcause import bp as rootcause_bp

    api_bp = Blueprint('api', __name__)
    api_bp.register_blueprint(pdf_bp, url_prefix='/pdf')
    api_bp.register_blueprint(feishu_oauth_bp, url_prefix='/oauth')
    api_bp.register_blueprint(rootcause_bp, url_prefix='/rootcause')

    app.register_blueprint(api_bp, url_prefix='/api')
