"""Holiday aware scheduler."""

from datetime import datetime, timedelta

import holidays
import pendulum
from celery.schedules import crontab


class HolidayAwareScheduler(crontab):
    """A custom scheduler that skips tasks on weekends and public holidays in China (default)."""

    def __init__(self, *args, **kwargs):
        self.country = kwargs.pop('country', 'CN')
        self.holidays = holidays.country_holidays(self.country)
        super().__init__(*args, **kwargs)

    def is_due(self, last_run_at: datetime) -> tuple[bool, timedelta]:
        """Check if the task should run and calculate the next run time, and skip holidays.

        Args:
            last_run_at: The last time the task was run.

        Returns:
            A tuple of (is_due, next_time_delta) where is_due is a boolean indicating
            if the task should run now, and next_time_delta is the time until the next check.
        """
        due, next_run_at = super().is_due(last_run_at)
        if not due:
            return False, next_run_at

        now = datetime.now()
        if now.weekday() < 5 and now.date() not in self.holidays:
            return True, next_run_at

        return False, next_run_at


class LastWeekdayOfMonthScheduler(crontab):
    """A custom scheduler that runs tasks on the last weekday of the month."""

    def is_due(self, last_run_at: datetime) -> tuple[bool, timedelta]:
        """Check if the task should run on the last weekday of the month.

        Args:
            last_run_at: The last time the task was run.

        Returns:
            A tuple of (is_due, next_time_delta) where is_due is a boolean indicating
            if the task should run now, and next_time_delta is the time until the next check.
        """
        due, next_run_at = super().is_due(last_run_at)
        if not due:
            return False, next_run_at

        now = pendulum.now()
        last_day_of_month = now.end_of('month')

        if now.date() == last_day_of_month.date():
            return True, next_run_at

        return False, next_run_at
