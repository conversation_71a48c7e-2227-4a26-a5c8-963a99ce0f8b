# Git 提交消息指南（Copilot 专用）

本文档为 GitHub Copilot 提供生成 Google 风格的 git 提交消息的指南。

## Google 风格提交格式

当被要求生成提交消息时，请遵循以下结构：

```
<类型>(<范围>): <概要>

<正文>

<页脚>
```

### 类型

必须是以下之一：

- **feat**: 新功能
- **fix**: 错误修复
- **docs**: 文档变更
- **style**: 不影响代码含义的变更（格式化等）
- **refactor**: 既不修复错误也不添加功能的代码更改
- **perf**: 性能改进
- **test**: 添加或修改测试
- **chore**: 构建过程或辅助工具的变更

### 范围（可选）

受影响的代码库组件或区域（例如：auth, logging, config）。

### 概要

- 简短描述（50个字符或更少）
- 使用祈使语气（使用"添加"而非"已添加"或"添加了"）
- 末尾不加句号

### 正文（可选）

- 与概要之间用空行分隔
- 解释"做了什么"和"为什么做"，而不是"如何做"
- 每行约72个字符换行

### 页脚（可选）

- 引用issues/PRs（例如："修复 #123"）
- 破坏性变更应以"BREAKING CHANGE:"开头

## 示例

```
feat(auth): 实现JWT认证

添加基于JWT的认证系统，支持令牌刷新。
令牌默认过期时间设置为24小时。
```

```
fix(logging): 防止重复日志条目

修改日志记录器，在写入前检查重复条目。
这解决了在生产环境中报告的性能问题。
```
