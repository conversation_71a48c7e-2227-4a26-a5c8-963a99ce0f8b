import os
import re
from pathlib import Path

import pypdf

from lib.constants import DATA_ROOT

LOCATION_KEYS = {
    ' AP-': ' AP-',
    ' US-': ' US-',
    ' NA-': ' NA-',
    ' CN-': ' CN-',
    ' Singapore': ' Singapore',
    ' Washington': ' Washington',
    ' San Jose': ' San Jose',
    ' AF-': ' AF-',
    ' SA-': ' SA-',
    ' CDN-': 'CDN-',
}

DOT_NUM_MATCH_REG = r'\$(\d+),(\d+)'
DOT_NUM_REPLACE_REG = r'$\1\2'


def add_dot_for_line(line: str):
    # 1. remove dot in dollar, e.g. $1,000 -> $1000
    # line = re.sub(r'\$(\d+),(\d+)', r'$\1\2', line)
    line = re.sub(DOT_NUM_MATCH_REG, DOT_NUM_REPLACE_REG, line)

    keyword = 'Monthly Recurring'
    if 'Overage' in line:
        keyword = 'Overage'
    elif 'Non-Recurring' in line:
        keyword = 'Non-Recurring'

    pieces = line.split(keyword)
    print('pieces:', pieces)

    id, location = pieces[0].split(' ', 1)[:]

    # split location
    locations = []

    location_key = 'Customer Own Data Center'
    for key in LOCATION_KEYS:
        if key in location:
            location_key = LOCATION_KEYS[key]
            break

    location_pieces = location.split(location_key)
    print(f'location_pieces: {location_pieces}, key: {key}, location_key: {location_key}')

    if len(location_pieces) == 1:
        locations.extend([location_pieces[0], ''])
    else:
        locations.extend([location_pieces[0], location_key + location_pieces[1]])

    new_lines = [id] + locations + [keyword] + pieces[1].strip().split(' ')

    new_line_str = ', '.join(new_lines)

    return new_line_str


def extract_lines_from_page(page: pypdf.PageObject):
    text = page.extract_text()
    lines = text.splitlines()

    current_line = ''
    for line in lines:
        line = line.strip()

        if line.startswith('ZL-'):
            if current_line:
                yield add_dot_for_line(current_line)

            current_line = line  # replace
        else:
            if current_line:
                if line.startswith('Subtotal'):
                    print('break it')
                    break
                current_line += f'{line} '

    if current_line:
        yield add_dot_for_line(current_line)


def extract_text_from_pdf(pdf_path):
    reader = pypdf.PdfReader(pdf_path)

    lines = []
    for page in reader.pages:
        lines.extend(line for line in extract_lines_from_page(page))

    return lines


def save_to_csv(content, filename: Path):
    with open(filename, 'w') as f:
        f.write(content)
        return filename


if __name__ == '__main__':
    pdf_dir = DATA_ROOT / 'pdf'

    for f in os.listdir(pdf_dir):
        pdf_path = pdf_dir / f
        if not pdf_path.name.endswith('.pdf'):
            continue

        print('-' * 20, f, '-' * 20)
        lines = extract_text_from_pdf(pdf_path)
        csv_path = save_to_csv('\n'.join(lines), pdf_path.with_suffix('.csv'))
        print(f'csv path: {csv_path}')
