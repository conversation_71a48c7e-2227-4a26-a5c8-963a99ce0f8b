import time
from typing import Optional
from urllib.parse import parse_qs, quote, urlencode, urlparse, urlunparse

from loguru import logger

from lib.clients.lark import LarkClient
from lib.configs import config
from lib.constants import STORE_ROOT
from lib.screenshot import FeishuLoginPlaywrightScreenShot


def gen_auth_url(base_url, client_id, redirect_uri, scopes: Optional[list[str]] = None):
    """
    Generate the authorization URL for Feishu login.
    """

    # 使用urlparse, urlencode, urljoin等方法拼接url, scopes参数用空格分割, 并且注意encode

    # 1. 拼接url
    parsed_url = urlparse(base_url)

    # 2. 拼接query参数
    queries = parse_qs(parsed_url.query)
    queries.update(
        client_id=client_id,
        redirect_uri=redirect_uri,
    )
    qs = urlencode(queries, doseq=True)
    if scopes:
        # 保证scope中的空格被encode成%20
        qs += '&scope=' + quote(' '.join(scopes), safe=':')

    final_url = urlunparse(
        (
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            qs,
            parsed_url.fragment,
        )
    )

    return final_url


def feishu_login(scopes: Optional[list[str]] = None):
    """
    Screenshot Feishu login page using Playwright.

    """
    base_url = 'https://accounts.feishu.cn/open-apis/authen/v1/authorize'
    # scopes = [
    #     'offline_access',
    #     'contact:contact.base:readonly',
    # ]

    app = config.lark.apps[config.lark.service_account.app_name]

    auth_url = gen_auth_url(
        base_url,
        app.app_id,
        'https://eagle.lilithgame.com/api/oauth',
        scopes,
    )

    t0 = time.time()
    logger.debug(f'start screenshot {auth_url}')
    with FeishuLoginPlaywrightScreenShot(headless=True) as screenshot:
        results = screenshot.take(
            auth_url,
            (STORE_ROOT / f'feishu-oauth-{time.time_ns()}.png').as_posix(),
        )

        # 1. 发起获取授权Code请求
        image_path = next(results)['image']
        logger.info(f'screenshot took {time.time() - t0:.2f}s')
        logger.debug(f'screenshot results: {image_path}')

        # 2. 发送登录二维码给用户授权
        receiver_email = str(config.admins[0])
        lark_client = LarkClient.from_config(config.lark.apps[config.lark.service_account.app_name])

        try:
            lark_client.send_image_message(image_path, receiver_email)
        except Exception as e:
            logger.error(f'Error sending image to {receiver_email}: {e}')
            return

        # 3. 等待用户扫码登录
        login_result = next(results)
        logger.debug(f'login result: {login_result}')


if __name__ == '__main__':
    scopes = [
        'offline_access',
        'contact:contact.base:readonly',
        'contact:user.base:readonly',
        'contact:user.email:readonly',
        'contact:user.department_path:readonly',
        'calendar:calendar',
        'contact:user.employee_id:readonly',
        'wiki:wiki',
        'wiki:wiki:readonly',
        'wiki:node:read',
        'wiki:node:copy',
    ]

    # base_url = 'https://accounts.feishu.cn/open-apis/authen/v1/authorize'
    # auth_url = gen_auth_url(
    #     base_url,
    #     'cli_a739addb39e41013',
    #     'https://eagle.lilithgame.com/api/oauth',
    #     scopes,
    # )
    # print(auth_url)

    feishu_login(scopes)
