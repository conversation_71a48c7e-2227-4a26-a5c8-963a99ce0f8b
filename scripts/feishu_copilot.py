# from lib.clients.lark import LarkClient
# from lib.configs import config

# app_config = config.lark.apps['aiops']
# gh_config = config.github

# lark_client = LarkClient.from_config(app_config)

# user_email = '<EMAIL>'
# copilot_bitable = lark_client.bitable_app(gh_config.bitable_app)

# feishu_user_id = lark_client.get_user_id(user_email)
# print(f'Feishu User ID: {feishu_user_id}')

# all_users = {}

# for record in copilot_bitable.records(
#     table_id=gh_config.bitable_table,
#     view_id=gh_config.view_id,
#     conds=[
#         {'field_name': 'GithubAccount', 'operator': 'isNotEmpty', 'value': []},
#         {'field_name': 'GithubUserId', 'operator': 'isNotEmpty', 'value': []},
#         {
#             'field_name': '状态',
#             'operator': 'doesNotContain',
#             'value': ['已离职', '已邀请'],
#         },
#     ],
# ):
#     gh_user_login = record.fields.get('GithubAccount')[0]['text']
#     gh_user_id = record.fields.get('GithubUserId', '')

#     print(f'login: {gh_user_login}, id: {gh_user_id}')

from tasks.ops import sync_seat_status

sync_seat_status()
