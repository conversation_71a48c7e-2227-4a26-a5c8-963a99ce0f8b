import orjson as json
import pendulum

from lib.clients.dify import Chat<PERSON><PERSON>
from lib.configs import config
from lib.utils.printer import debug
from lib.utils.rate_limit import RateLimiter

dify_config = config.dify


def invoke(question: str):
    client = ChatClient(dify_config.api_key, dify_config.base_url)

    with client.create_chat_message(
        inputs={},
        query=question,
        user='fake-user',
        response_mode='streaming',
    ) as r:
        for line in r.iter_lines():
            line_body = line.strip('data:')
            if not len(line_body):
                continue

            json_body = json.loads(line_body)
            yield json_body


# whole_message = []
end_message = {}

limiter = RateLimiter(5, 1000)

for item in limiter(invoke('请问 beni 是谁?')):
    # for item in invoke('请问 beni 是谁?'):
    msg_type = item['event']
    if msg_type == 'message_end':
        end_message = item
    elif msg_type == 'agent_message':
        message = item['answer']
        if message:
            # whole_message.append(item['answer'])
            # debug('>>> ', ''.join(whole_message), color='green')
            print(item['answer'], flush=True, end='')

print()
debug(f'now is {pendulum.now()}')
