"""GitHub Copilot 邀请处理模块。

该模块提供 GitHub Copilot 用户邀请的自动化处理功能。
"""

from pathlib import Path
from typing import Optional, Union

from lib.clients.lark.client import LarkClient
from lib.configs import config
from lib.constants import DATA_ROOT
from tasks.ops import invite_user_to_github_org


class CopilotInviter:
    """GitHub Copilot 邀请处理类。

    该类封装了处理 GitHub Copilot 邀请的各种功能，包括读取邮件内容、
    发送邀请和通知用户等操作。
    """

    def __init__(self, lark_app_name: str = 'aiops'):
        """初始化 Copilot 邀请处理类。

        Args:
            lark_app_name: Lark 应用名称，默认为'aiops'
        """
        self.client = LarkClient.from_config(config.lark.apps[lark_app_name])

    def read_email_content(self, file_path: Union[str, Path]) -> Optional[str]:
        """读取邮件内容文件。

        Args:
            file_path: 邮件内容文件路径

        Returns:
            读取的邮件内容字符串，如果读取失败返回 None

        Raises:
            FileNotFoundError: 当指定的文件不存在时
            IOError: 当文件读取出错时
        """
        try:
            with open(file_path, 'r') as f:
                return f.read()
        except (FileNotFoundError, IOError):
            print(f'读取邮件内容文件失败: {file_path}')
            raise

    def process_invitation(self, email_content: str) -> bool:
        """处理邀请请求。

        Args:
            email_content: 包含邀请信息的邮件内容

        Returns:
            处理是否成功
        """
        try:
            invite_user_to_github_org(email_content)
            print('成功处理 Copilot 邀请请求')
            return True
        except Exception as e:
            print(f'处理 Copilot 邀请请求失败: {e}')
            return False

    def invite_from_file(self, file_path: Union[str, Path] = DATA_ROOT / 'copilot.txt') -> bool:
        """从文件读取邮件内容并处理邀请。

        Args:
            file_path: 邮件内容文件路径，默认为 DATA_ROOT/copilot.txt

        Returns:
            处理是否成功
        """
        try:
            content = self.read_email_content(file_path)
            return self.process_invitation(content)
        except Exception as e:
            print(f'从文件处理邀请失败: {e}')
            return False


def main():
    """主函数，执行 Copilot 邀请处理。"""
    inviter = CopilotInviter()
    success = inviter.invite_from_file()
    if success:
        print('Copilot 邀请处理成功')
    else:
        print('Copilot 邀请处理失败')


if __name__ == '__main__':
    main()
