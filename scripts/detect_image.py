import base64
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, SystemMessage

from lib.constants import DATA_ROOT
from lib.llm.models import get_llm

load_dotenv()


SYSTEM_PROMPT = '你是一个运维工程师，你需要 分析图片的表格数据并形成csv格式。'


class HumaneImageMessage(HumanMessage):
    def __init__(self, image_path: Path):
        super().__init__(
            content=[
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': base64_image(image_path),
                    },
                }
            ],
        )


def base64_image(image_path: Path, prefix: Optional[str] = None) -> str:
    image_base64 = base64.b64encode(image_path.read_bytes()).decode('utf-8')

    if not prefix:
        return f'data:image/jpeg;base64,{image_base64}'
    return f'{prefix};base64,{image_base64}'


def invoke(img_path: Path):
    # parse image to base64
    image_path = DATA_ROOT / img_path

    messages = [
        SystemMessage(SYSTEM_PROMPT),
        HumaneImageMessage(image_path),
    ]

    llm = get_llm('gpt4o')
    result = llm.invoke(messages)
    print(result.content)
    # cprint(result.response_metadata.get('token_usage'), 'cyan', attrs=['bold'])
    return result
