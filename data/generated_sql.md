以下是根据您提供的数据库信息生成的 MySQL 初始化语句。注意，我遵循了单词下划线连接和单数表名的命名约定。

```sql
-- 创建 user 表
CREATE TABLE user (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    group INT NOT NULL,
    FOREIGN KEY (group) REFERENCES group (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 group 表
CREATE TABLE group (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 dcmdb_machine 表
CREATE TABLE dcmdb_machine (
    id INT PRIMARY KEY AUTO_INCREMENT,
    instance_name VARCHAR(100) NOT NULL,
    instance_id VARCHAR(30) NOT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'running',
    owner INT NOT NULL,
    FOREIG<PERSON> KEY (owner) REFERENCES user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 详细说明：

1. **表名：**
   - `user` 表：存储用户信息。
   - `group` 表：存储用户组信息。
   - `dcmdb_machine` 表：存储设备信息。

2. **字段：**
   - 使用下划线命名法，如 `group`、`instance_name` 等。
   - `id` 字段作为主键，且为自动递增。
   - 外键约束：
     - `user` 表的 `group` 字段引用 `group` 表的 `id`。
     - `dcmdb_machine` 表的 `owner` 字段引用 `user` 表的 `id`。
   - 必需字段（NOT NULL）已明确标记。
   - `status` 字段默认值为 `running`。

3. **引擎和字符集：**
   - 使用 InnoDB 存储引擎支持事务和外键约束。
   - 使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序，以支持完整的 Unicode 字符集。

您可以使用这些 SQL 语句初始化您的数据库。如果需要进一步调整，请告诉我！