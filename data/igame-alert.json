[{"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "from oma monitor告警消息", "description": "Prometheus恢复信息 resolved\n函数计算平均执行时间1分钟之内超过500ms\n功能: 函数计算平均执行时间1分钟之内超过500ms\n集群：\n时间：2024-10-30T10:01:20+08:00\n图片：https://mimir-alertmanager.oss-cn-shanghai.aliyuncs.com/grafana/cRbu0h334i1WT0XOjlum.png\n\n@何思宁\n\nPrometheusAlert"}, "create_time": 1730254439842, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云DDoS攻击告警", "description": "您的IP:************** 受到攻击流量已超过云盾DDoS基础防护的带宽峰值，服务器的所有访问已被屏蔽，如果20分钟后攻击停止将自动解除否则会延期解除。"}, "create_time": 1730259148692, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云流量屏蔽取消通知", "description": "【阿里云】尊敬的iga*e@rd-gy6t*：您的IP：************** 已取消流量屏蔽，恢复正常访问，详情请登录流量安全控制台事件中心查看。"}, "create_time": 1730259248615, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "other", "title": "限流保护措施结束", "description": "【阿里云】尊敬的iga*e@rd-gy6t*：您的IP************** 已结束限流保护措施。"}, "create_time": 1730259506310, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "other", "title": "启动限流保护措施", "description": "【阿里云】尊敬的iga*e@rd-gy6t*：为了保障服务器的稳定运行，您的IP:************** 已启动限流保护措施。阈值与产品规格相关，您可以登录云盾控制台调整清洗阈值。"}, "create_time": 1730259512760, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云告警", "description": "您的IP************** 已结束限流保护措施。"}, "create_time": 1730259872191, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 16:12:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: "}, "create_time": 1730275941809, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 16:14:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: "}, "create_time": 1730276061805, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 16:17:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: "}, "create_time": 1730276241811, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "恢复: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: resolved\n告警名称: Pod重启\n恢复时间: 2024-10-30 16:19:21\n告警概述: 命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: 静默1小时 | 静默2小时 | 静默4小时 | 静默8小时 | 静默24小时"}, "create_time": 1730276361808, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0003)\n· 报警时间: 2024-10-30 18:34:58\n· instanceId: eip-0xil23kzk1ews4tbhpw1f\n· 监控指标: 流入带宽\n· 报警条件: 连续满足3次 当前值>=100Mbits/s\n· 当前值: 691.7975Mbits/s\n· 持续时间: 3分钟\n"}, "create_time": 1730284506939, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0001)\n· 报警时间: 2024-10-30 18:34:58\n· instanceId: eip-0xi8zyttvau7ybgd5m59t\n· 监控指标: 流入带宽\n· 报警条件: 连续满足3次 当前值>=100Mbits/s\n· 当前值: 1.678Gbits/s\n· 持续时间: 3分钟\n"}, "create_time": 1730284507384, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0002)\n· 报警时间: 2024-10-30 18:34:58\n· instanceId: eip-0xi6o9q6imhcvp8ka688q\n· 监控指标: 流入带宽\n· 报警条件: 连续满足3次 当前值>=100Mbits/s\n· 当前值: 954.0657Mbits/s\n· 持续时间: 3分钟\n"}, "create_time": 1730284507864, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 紧急", "description": "【云监控】共享带宽发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: tva-igame-cbwp\n· 报警规则: 带宽告警\n· 报警时间: 2024-10-30 18:37:14\n· instanceId: cbwp-0xihdoecibfwkldws2m1o\n· 监控指标: 多指标\n· 报警条件: 连续满足3次流入带宽 当前值 >= 1000Mbits/s\n或流出带宽 当前值 >= 4.8828Gbits/s\n· 当前值: 流入带宽 当前值3.2976Gbits/s(触发报警 )流出带宽 当前值85.5721Mbits/s(未触发)\n· 持续时间: 5分钟\n"}, "create_time": 1730284636456, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 18:37:23\n告警概述:\n命名空间【global】多个Pod容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: \n静默1小时 静默2小时 静默4小时 静默8小时 静默24小时"}, "create_time": 1730284643604, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】弹性公网IP恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0003)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-10-30 18:37:58\n· instanceId: eip-0xil23kzk1ews4tbhpw1f\n· 监控指标: 流入带宽\n· 报警条件: 连续满足1次\n当前值>=100Mbits/s\n· 当前值: 849.4297Kbits/s\n· 持续时间: 6分钟\n"}, "create_time": 1730284679397, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】弹性公网IP恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0001)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-10-30 18:37:58\n· instanceId: eip-0xi8zyttvau7ybgd5m59t\n· 监控指标: 流入带宽\n· 报警条件: 连续满足1次\n当前值>=100Mbits/s\n· 当前值: 856.3594Kbits/s\n· 持续时间: 6分钟\n"}, "create_time": 1730284679889, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】弹性公网IP恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0002)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-10-30 18:37:58\n· instanceId: eip-0xi6o9q6imhcvp8ka688q\n· 监控指标: 流入带宽\n· 报警条件: 连续满足1次\n当前值>=100Mbits/s\n· 当前值: 814.9766Kbits/s\n· 持续时间: 6分钟\n"}, "create_time": 1730284680333, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 18:38:23\n告警概述:\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理:"}, "create_time": 1730284703627, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】共享带宽恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: tva-igame-cbwp\n· 报警规则: 带宽告警\n· 报警时间: 2024-10-30 18:40:13\n· instanceId: cbwp-0xihdoecibfwkldws2m1o\n· 监控指标: 多指标\n· 报警条件: 连续满足1次 流入带宽 当前值 >= 1000Mbits/s 或 流出带宽 当前值 >= 4.8828Gbits/s\n· 当前值: 流入带宽 当前值 15.0029 Mbits/s 流出带宽 当前值 61.6901 Mbits/s\n· 持续时间: 8分钟"}, "create_time": 1730284815539, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-10-30 18:42:23\n告警概述:\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730284943634, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "恢复: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: resolved\n告警名称: Pod重启\n恢复时间: 2024-10-30 18:44:23\n告警概述:\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730285063868, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "system", "title": "User Invitation", "description": "朴文学 invited <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to the group. New members can view all chat history."}, "create_time": 1730360370811, "sender_id": "", "sender_type": ""}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 警告", "description": "【云监控】云服务器ECS发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: igame-test-ftest-***********\n· 实例ID: i-0xice85v9helrbsf3dml\n· 报警规则: SystemDefault_acs_ecs_dashboard_diskusage_utilization\n· 报警时间: 2024-10-31 16:02:24\n· 内网IP: ***********\n· IP组: ***********\n· Device: /dev/vdc\n· 监控指标: (Agent)disk.usage.utilization_device\n· 报警条件: 连续满足3次 平均值>89%\n· 当前值: 89.02%\n· 持续时间: 2分钟\n"}, "create_time": 1730361747497, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 - 恢复", "description": "【云监控】云服务器ECS恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: igame-test-ftest-***********\n· 实例ID: i-0xice85v9helrbsf3dml\n· 报警规则: SystemDefault_acs_ecs_dashboard_diskusage_utilization\n· 报警时间: 2024-10-31 16:08:24\n· 内网IP: ***********\n· IP组: ***********\n· Device: /dev/vdc\n· 监控指标: (Agent)disk.usage.utilization_device\n· 报警条件: 连续满足1次 平均值>89%\n· 当前值: 84.56%\n· 持续时间: 8分钟\n"}, "create_time": 1730362106996, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 16:04:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: \n静默1小时 \n静默2小时 \n静默4小时 \n静默8小时 \n静默24小时"}, "create_time": 1730448261957, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 16:05:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: \n静默1小时: https://open.feishu.cn/anycross/trigger/callback/YmI4NzhlNzJlZWM0YjgyYjBhNmY3YzMxMTE0NGZkMmEw?product_name=iGame-Global&alertname=Pod%E9%87%8D%E5%90%AF&interval=1h&cluster=tva-igame-prod-gray-k8s&namespace=gray&pods=prod-gray-game-5,prod-gray-game-4,prod-gray-game-3,prod-gray-game-2,prod-gray-game-1,prod-gray-game-0\n静默2小时: https://open.feishu.cn/anycross/trigger/callback/YmI4NzhlNzJlZWM0YjgyYjBhNmY3YzMxMTE0NGZkMmEw?product_name=iGame-Global&alertname=Pod%E9%87%8D%E5%90%AF&interval=2h&cluster=tva-igame-prod-gray-k8s&namespace=gray&pods=prod-gray-game-5,prod-gray-game-4,prod-gray-game-3,prod-gray-game-2,prod-gray-game-1,prod-gray-game-0\n静默4小时: https://open.feishu.cn/anycross/trigger/callback/YmI4NzhlNzJlZWM0YjgyYjBhNmY3YzMxMTE0NGZkMmEw?product_name=iGame-Global&alertname=Pod%E9%87%8D%E5%90%AF&interval=4h&cluster=tva-igame-prod-gray-k8s&namespace=gray&pods=prod-gray-game-5,prod-gray-game-4,prod-gray-game-3,prod-gray-game-2,prod-gray-game-1,prod-gray-game-0\n静默8小时: https://open.feishu.cn/anycross/trigger/callback/YmI4NzhlNzJlZWM0YjgyYjBhNmY3YzMxMTE0NGZkMmEw?product_name=iGame-Global&alertname=Pod%E9%87%8D%E5%90%AF&interval=8h&cluster=tva-igame-prod-gray-k8s&namespace=gray&pods=prod-gray-game-5,prod-gray-game-4,prod-gray-game-3,prod-gray-game-2,prod-gray-game-1,prod-gray-game-0\n静默24小时: https://open.feishu.cn/anycross/trigger/callback/YmI4NzhlNzJlZWM0YjgyYjBhNmY3YzMxMTE0NGZkMmEw?product_name=iGame-Global&alertname=Pod%E9%87%8D%E5%90%AF&interval=24h&cluster=tva-igame-prod-gray-k8s&namespace=gray&pods=prod-gray-game-5,prod-gray-game-4,prod-gray-game-3,prod-gray-game-2,prod-gray-game-1,prod-gray-game-0"}, "create_time": 1730448321919, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 16:09:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: "}, "create_time": 1730448561929, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "恢复: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: resolved\n告警名称: Pod重启\n恢复时间: 2024-11-01 16:11:21\n告警概述:\n命名空间【gray】Pod【prod-gray-game-0】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-1】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-2】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-3】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-4】容器【patch】重启了\n命名空间【gray】Pod【prod-gray-game-5】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: cc1e756460f464e40b698a31f7e30bc64\n实例名称: tva-igame-prod-gray-k8s\n告警处理: 静默1小时 静默2小时 静默4小时 静默8小时 静默24小时"}, "create_time": 1730448681899, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 18:37:21\n告警概述:\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730457441826, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知                     警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0002)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-11-01 18:36:58\n· instanceId: eip-0xi6o9q6imhcvp8ka688q\n· 监控指标: 流入带宽\n· 报警条件:  连续满足3次\n当前值>=100Mbits/s\n· 当前值: 2.4278Gbits/s\n· 持续时间: 3分钟\n报警规则"}, "create_time": 1730457446131, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知                     警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0001)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-11-01 18:36:58\n· instanceId: eip-0xi8zyttvau7ybgd5m59t\n· 监控指标: 流入带宽\n· 报警条件:  连续满足3次\n当前值>=100Mbits/s\n· 当前值: 1.9525Gbits/s\n· 持续时间: 3分钟\n报警规则"}, "create_time": 1730457446597, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知                     警告", "description": "【云监控】弹性公网IP发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0003)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-11-01 18:36:58\n· instanceId: eip-0xil23kzk1ews4tbhpw1f\n· 监控指标: 流入带宽\n· 报警条件:  连续满足3次\n当前值>=100Mbits/s\n· 当前值: 1.9543Gbits/s\n· 持续时间: 3分钟\n报警规则"}, "create_time": 1730457447009, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知                     恢复", "description": "【云监控】弹性公网IP恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: *************(tva-igame-prod-global-eip-0002)\n· 报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n· 报警时间: 2024-11-01 18:37:58\n· instanceId: eip-0xi6o9q6imhcvp8ka688q\n· 监控指标: 流入带宽\n· 报警条件:  连续满足1次\n当前值>=100Mbits/s\n· 当前值: 64.3343Mbits/s\n· 持续时间: 4分钟\n报警规则"}, "create_time": 1730457479466, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 18:38:21\n告警概述:\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理:\n静默1小时\n静默2小时\n静默4小时\n静默8小时\n静默24小时"}, "create_time": 1730457501908, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】弹性公网IP恢复正常\n用户名: <EMAIL>\n用户ID: 1736239365929906\n地域: 美东弗吉尼亚\n实例名称: *************(tva-igame-prod-global-eip-0003)\n报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n报警时间: 2024-11-01 18:38:58\ninstanceId: eip-0xil23kzk1ews4tbhpw1f\n监控指标: 流入带宽\n报警条件: 连续满足1次\n当前值>=100Mbits/s\n当前值: 899.4844Kbits/s\n持续时间: 5分钟\n报警规则"}, "create_time": 1730457539442, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】弹性公网IP恢复正常\n用户名: <EMAIL>\n用户ID: 1736239365929906\n地域: 美东弗吉尼亚\n实例名称: *************(tva-igame-prod-global-eip-0001)\n报警规则: SystemDefault_acs_vpc_eip_net_rx.rate\n报警时间: 2024-11-01 18:38:58\ninstanceId: eip-0xi8zyttvau7ybgd5m59t\n监控指标: 流入带宽\n报警条件: 连续满足1次\n当前值>=100Mbits/s\n当前值: 979.1992Kbits/s\n持续时间: 5分钟\n报警规则"}, "create_time": 1730457539901, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 紧急", "description": "【云监控】共享带宽发生告警\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: tva-igame-cbwp\n· 报警规则: 带宽告警\n· 报警时间: 2024-11-01 18:39:14\n· instanceId: cbwp-0xihdoecibfwkldws2m1o\n· 监控指标: 多指标\n· 报警条件: 连续满足3次\n流入带宽 当前值 >= 1000Mbits/s\n或\n流出带宽 当前值 >= 4.8828Gbits/s\n· 当前值:\n流入带宽 当前值6.348Gbits/s(触发报警)\n流出带宽 当前值122.7064Mbits/s(未触发)\n· 持续时间: 5分钟\n"}, "create_time": 1730457556711, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "cloud_event", "title": "阿里云监控报警通知 恢复", "description": "【云监控】共享带宽恢复正常\n· 用户名: <EMAIL>\n· 用户ID: 1736239365929906\n· 地域: 美东弗吉尼亚\n· 实例名称: tva-igame-cbwp\n· 报警规则: 带宽告警\n· 报警时间: 2024-11-01 18:40:13\n· instanceId: cbwp-0xihdoecibfwkldws2m1o\n· 监控指标: 多指标\n· 报警条件: 连续满足1次\n流入带宽 当前值 >= 1000Mbits/s\n或\n流出带宽 当前值 >= 4.8828Gbits/s\n· 当前值:\n流入带宽 当前值480.4149Mbits/s\n流出带宽 当前值78.8562Mbits/s\n· 持续时间: 6分钟"}, "create_time": 1730457615542, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: firing\n告警名称: Pod重启\n恢复时间: 2024-11-01 18:41:21\n告警概述:\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: 静默1小时 静默2小时 静默4小时 静默8小时 静默24小时"}, "create_time": 1730457681852, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "恢复: iGame-Global Pod重启", "description": "告警等级: crit\n告警状态: resolved\n恢复时间: 2024-11-01 18:43:21\n命名空间【global】Pod【prod-global-game-0】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-1】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-10】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-100】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-101】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-102】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-103】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-104】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-105】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-106】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-107】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-108】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-109】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-11】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-110】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-111】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-112】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-113】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-114】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-115】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-116】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-117】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-118】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-119】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-12】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-13】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-14】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-15】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-16】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-17】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-18】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-19】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-2】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-20】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-21】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-22】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-23】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-24】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-25】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-26】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-27】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-28】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-29】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-3】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-30】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-31】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-32】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-33】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-34】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-35】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-36】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-37】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-38】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-39】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-4】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-40】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-41】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-42】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-43】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-44】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-45】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-46】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-47】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-48】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-49】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-5】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-50】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-51】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-52】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-53】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-54】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-55】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-56】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-57】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-58】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-59】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-6】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-60】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-61】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-62】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-63】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-64】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-65】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-66】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-67】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-68】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-69】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-7】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-70】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-71】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-72】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-73】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-74】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-75】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-76】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-77】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-78】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-79】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-8】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-80】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-81】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-82】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-83】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-84】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-85】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-86】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-87】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-88】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-89】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-9】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-90】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-91】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-92】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-93】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-94】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-95】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-96】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-97】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-98】容器【patch】重启了\n命名空间【global】Pod【prod-global-game-99】容器【patch】重启了\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: 静默1小时 静默2小时 静默4小时 静默8小时 静默24小时"}, "create_time": 1730457801874, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global K8S节点磁盘空间不足", "description": "告警等级: crit\n告警状态: firing\n告警名称: K8S节点磁盘空间不足\n恢复时间: 2024-10-19 08:21:19\n告警概述:\n【***********:9100 】的磁盘空间/使用率超过 80.01%\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730161279000, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "告警: iGame-Global K8S节点磁盘空间不足", "description": "告警等级: crit\n告警状态: firing\n恢复时间: 2024-10-19 08:32:19\n告警名称: K8S节点磁盘空间不足\n告警概述:\n【***********:9100 】的磁盘空间/使用率超过 80.01%\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730161939000, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}, {"chat_id": "oc_0b19009c55f3d0fecb0a6a1b03cf4a34", "content": {"type": "metric", "title": "恢复: iGame-Global K8S节点磁盘空间不足", "description": "告警等级: crit\n告警状态: resolved\n告警名称: K8S节点磁盘空间不足\n恢复时间: 2024-10-19 08:35:19\n告警概述:\n【***********:9100 】的磁盘空间/使用率超过 80.01%\n告警产品: iGame-Global\n告警实例: c17e46ccb263b43f687da5f34c5cb068a\n实例名称: tva-igame-prod-global-k8s\n告警处理: "}, "create_time": 1730162119000, "sender_id": "cli_c08abc1da138d00f", "sender_type": "app"}]