要查询张三所在组有多少运行中的机器，可以按照以下步骤编写SQL语句：

1. 首先，找到张三所在的组。
2. 然后，找到该组内的所有用户。
3. 最后，统计这些用户所拥有的状态为“running”的机器数量。

以下是实现这一需求的SQL语句：

```sql
SELECT 
    COUNT(m.id) AS running_machines_count
FROM 
    dcmdb_machines m
JOIN 
    user u ON m.owner_id = u.id
WHERE 
    u.group_id = (SELECT group_id FROM user WHERE user_name = '张三')
    AND m.status = 'running';
```

### 代码解释：
1. `COUNT(m.id)`: 统计机器的数量。
2. `FROM dcmdb_machines m`: 从机器表中选择数据。
3. `JOIN user u ON m.owner_id = u.id`: 联合用户表，通过机器的owner_id与用户的id进行匹配。
4. `WHERE u.group_id = (SELECT group_id FROM user WHERE user_name = '张三')`: 过滤属于张三所在组的用户。
5. `AND m.status = 'running'`: 进一步筛选出状态为运行中的机器。

这个查询会返回张三所在组下所有用户拥有的运行中的机器数量。
