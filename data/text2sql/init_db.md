以下是根据提供的数据库信息生成的 MySQL 语句，用于完成数据初始化。注意，表名和字段名使用了下划线连接，并添加了适当的备注。

```sql
-- 创建组表
CREATE TABLE `group` (
  `id` INT(11) NOT NULL PRIMARY KEY COMMENT '组ID',
  `group_name` VARCHAR(50) NOT NULL COMMENT '组名称'
) COMMENT='用户组信息表';

-- 创建用户表
CREATE TABLE `user` (
  `id` INT(11) NOT NULL PRIMARY KEY COMMENT '用户ID',
  `user_name` VARCHAR(50) NOT NULL COMMENT '用户名',
  `group_id` INT(11) NOT NULL COMMENT '所属组ID',
  CONSTRAINT `fk_user_group` FOREIGN KEY (`group_id`) REFERENCES `group` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT='用户信息表';

-- 创建机器表
CREATE TABLE `dcmdb_machines` (
  `id` INT(11) NOT NULL PRIMARY KEY COMMENT '机器ID',
  `instance_name` VARCHAR(100) NOT NULL COMMENT '实例名称',
  `instance_id` VARCHAR(30) NOT NULL COMMENT '实例ID',
  `status` VARCHAR(30) NOT NULL DEFAULT 'running' COMMENT '状态',
  `owner_id` INT(11) NOT NULL COMMENT '拥有者ID',
  CONSTRAINT `fk_dcmdb_machines_owner` FOREIGN KEY (`owner_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT='DCMDB机器信息表';
```

### 说明：
1. **表名和字段名**：使用了下划线连接，例如 `user` 和 `user_name`。
2. **外键约束**：在 `user` 表中，`group_id` 字段引用了 `group` 表的 `id` 字段；在 `dcmdb_machines` 表中，`owner_id` 字段引用了 `user` 表的 `id` 字段。
3. **备注**：每个表和字段都添加了中文备注，便于理解表和字段的作用。
4. **默认值**：`status` 字段设置了默认值 `'running'`。
5. **外键行为**：设置了外键删除和更新时的级联操作。

### 注意事项：
- 确保数据库用户有足够的权限执行 `CREATE TABLE` 和 `ALTER TABLE` 操作。
- 确保外键约束在数据库中已启用。
- 根据实际需求，可能需要调整字段长度和数据类型。
