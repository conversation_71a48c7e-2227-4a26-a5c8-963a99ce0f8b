# Step-by-Step Analysis

1. **确定你要针对什么数据进行分析?**
   - 针对内存使用率的最大值进行分析。

2. **确定你要分析的是什么变化(用户的问题开头会提到): 变化差值 还是 变化比率, 对应的公式是什么?**
   - 分析变化差值。

3. **确定你要合规性检查的比较方法: 大于、小于 还是 不大于、不小于?**
   - 检查变化差值是否不大于阈值。

4. **确定你的阈值是多少?**
   - 阈值是5%。

5. **抽取出要要分析的数据以```###```三个#包裹，每一行有如下三个字段: 本期值、上期值、阈值**
   ```
   2.32% 2.31% 5%
   ```

6. **对上面的每一行数据, 进行合规性检查。**
   - 计算变化差值: abs(2.32% - 2.31%) = 0.01%
   - 检查是否不大于5%: 0.01% <= 5%

7. **汇总以上信息，并按照格式化要求输出最终结果(Final JSON Output)。**

### Final JSON Output ###

```
{
  "complicant": true,
  "function_type": "变化差值",
  "reason": "",
  "analysis_details": "- 内存使用率最大值(0.01%, 合规)"
}
```
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/OUTPUT_PARSING_FAILURE 