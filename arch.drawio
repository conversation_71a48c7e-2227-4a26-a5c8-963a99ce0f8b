<mxfile host="app.diagrams.net" modified="2024-03-10T00:00:00.000Z" agent="5.0 (Windows)" etag="your-etag" version="21.6.9">
  <diagram id="OPS_MATE_ARCH" name="OPS_MATE架构">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 核心应用框架 -->
        <mxCell id="2" value="Flask 应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="40" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 应用工厂 -->
        <mxCell id="3" value="app_factory.py&#xa;(应用工厂)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="4" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="330" as="sourcePoint" />
            <mxPoint x="560" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 扩展模块 -->
        <mxCell id="5" value="Extensions" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="200" y="240" width="160" height="180" as="geometry" />
        </mxCell>
        <mxCell id="6" value="ext_log" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="5">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="ext_mongo" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="5">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="ext_celery" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="5">
          <mxGeometry y="90" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" value="ext_cors" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="5">
          <mxGeometry y="120" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="ext_db (注释掉)" style="text;strokeColor=#666666;fillColor=#f5f5f5;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;fontColor=#333333;" vertex="1" parent="5">
          <mxGeometry y="150" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- API蓝图 -->
        <mxCell id="11" value="Blueprints" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="760" y="240" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="12" value="api (主蓝图)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="11">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="13" value="admin_v2" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="11">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="pdf" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="11">
          <mxGeometry y="90" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 命令模块 -->
        <mxCell id="15" value="commands.py&#xa;(自定义命令)" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="480" y="240" width="160" height="60" as="geometry" />
        </mxCell>

        <!-- 数据和服务层 -->
        <mxCell id="17" value="Service Layer" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="480" y="470" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="18" value="service/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="17">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19" value="models/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="17">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="20" value="tasks/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="17">
          <mxGeometry y="90" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 辅助工具层 -->
        <mxCell id="21" value="Support Tools" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="200" y="470" width="160" height="150" as="geometry" />
        </mxCell>
        <mxCell id="22" value="scripts/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="21">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="23" value="lib/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="21">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="prompt(s)/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="21">
          <mxGeometry y="90" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="25" value="knowledge/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="21">
          <mxGeometry y="120" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 测试和配置层 -->
        <mxCell id="26" value="Config &amp; Tests" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="760" y="470" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="27" value="configs/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="26">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="28" value="tests/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="26">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="29" value="docker/" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="26">
          <mxGeometry y="90" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 外部数据服务 -->
        <mxCell id="30" value="External Services" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="480" y="630" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="31" value="Redis (Celery Broker)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="30">
          <mxGeometry y="30" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="32" value="MongoDB" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="30">
          <mxGeometry y="60" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 连接关系 -->
        <!-- app_factory 到扩展模块 -->
        <mxCell id="40" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="3" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="170" as="sourcePoint" />
            <mxPoint x="430" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="41" value="init_extensions()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="40">
          <mxGeometry x="0.2" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="-9" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- app_factory 到蓝图 -->
        <mxCell id="42" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="3" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="170" as="sourcePoint" />
            <mxPoint x="700" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="840" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="43" value="init_blueprints()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="42">
          <mxGeometry x="0.2" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-9" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- app_factory 到命令 -->
        <mxCell id="44" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="270" as="sourcePoint" />
            <mxPoint x="610" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="45" value="registry_commands()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="44">
          <mxGeometry x="-0.2" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>

        <!-- 主要模块到服务层的连接 -->
        <mxCell id="46" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="15" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="380" as="sourcePoint" />
            <mxPoint x="610" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- API蓝图到服务层的连接 -->
        <mxCell id="47" value="" style="endArrow=classic;html=1;entryX=1;entryY=0.25;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="11" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="430" as="sourcePoint" />
            <mxPoint x="710" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="840" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 服务层到外部服务的连接 -->
        <mxCell id="48" value="" style="endArrow=classic;startArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="17" target="30">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="650" as="sourcePoint" />
            <mxPoint x="560" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 服务层到工具层的连接 -->
        <mxCell id="49" value="" style="endArrow=classic;startArrow=classic;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="19" target="23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="650" as="sourcePoint" />
            <mxPoint x="450" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="50" value="app.py&#xa;(应用入口)" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="280" y="40" width="120" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="51" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="50" target="2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="80" as="sourcePoint" />
            <mxPoint x="470" y="69.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="52" value="celery_app" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="280" y="140" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="53" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="50" target="52">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="170" as="sourcePoint" />
            <mxPoint x="430" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
