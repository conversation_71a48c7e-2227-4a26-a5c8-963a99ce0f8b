import sys
from pathlib import Path

import pytest

from lib.configs import config
from lib.constants import DATA_ROOT
from lib.llm.models import get_llm
from lib.llm.prompt import get_data_content

sys.dont_write_bytecode = True

AVAILABLE_MODELS = config.llms.keys()


def get_available_datasets(suffixes: list[str] = ['.txt', '.md']) -> list[str]:
    files = []
    for suffix in suffixes:
        files.extend([p.stem for p in DATA_ROOT.rglob(f'*{suffix}')])
    return sorted(files)


AVAILABLE_DATASETS = get_available_datasets()


@pytest.fixture
def cleanable_tmp_path(tmp_path: 'Path'):
    """Return a temporary directory path that is cleaned up after the test."""
    yield tmp_path
    if tmp_path.exists():
        print(f'Cleaning up: {tmp_path}')
        for item in tmp_path.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                item.rmdir()
        tmp_path.rmdir()


@pytest.fixture(scope='session')
def load_llm():
    """Return an OpenAI API compatiable model."""

    def _get_model(model_name: str, **kwargs):
        return get_llm(model_name, **kwargs)

    yield _get_model


@pytest.fixture(scope='session')
def dataset():
    """Return a dataset content."""

    def _get_data_content(name: str) -> str:
        return get_data_content(name)

    yield _get_data_content
