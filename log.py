import os
from typing import Optional

import loguru

from lib.configs import config

# 定义日志文件相关配置
LOG_DIR = 'logs'  # 日志目录
LOG_FILE = 'ops_mate.log'  # 日志文件名
MAX_LOG_SIZE = '50 MB'  # 单个日志文件最大容量（50MB）
BACKUP_COUNT = 5  # 保留的备份文件数量

formatter_str = (
    #'{time:YYYY-MM-DD HH:mm:ss.SSS} {level} PID:{process} {name}:{function}:{line} - {message}'
    '{time:YYYY-MM-DDTHH:mm:ss.SSSZZ} {level} PID:{process} {name}:{function}:{line} - {message}'
)


def setup_logger(level: Optional[str] = None):
    # 确保日志目录存在
    os.makedirs(LOG_DIR, exist_ok=True)
    log_file_path = os.path.join(LOG_DIR, LOG_FILE)

    target_level = level if level else 'DEBUG' if config.debug else 'INFO'

    _logger = loguru.logger
    _logger.remove()  # 移除默认的日志处理器
    _logger.add(
        log_file_path,
        level=target_level,
        format=formatter_str,
        rotation=MAX_LOG_SIZE,
        retention=BACKUP_COUNT,
    )
    return _logger


logger = setup_logger()
