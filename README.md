# OpsMate

## Requirements

- `Redis`

## Install

- Miniconda

```sh
conda create --name py312 python=3.12
python_home=$(conda env list | grep py312)
```

- uv (pip, virtualenv replacement)

```sh
uv sync -p $python_home --group dev --group test --all-extras
```

## Run serivce

```sh
./service/control.sh [start|restart|stop]
./service/control.sh [beat|beat_restart]
```

## Run Test

```sh
uv run pytest -vv
```

## Format and Fix

```sh
uv run ruff format
uv run ruff check --fix .
```
