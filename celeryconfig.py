"""
Celery 配置文件 (celeryconfig.py)
符合 Celery 5.x 标准的配置格式
"""

from celery.schedules import crontab, solar

from lib.configs import config
from lib.constants import STORE_ROOT
from lib.utils.pipe import PipeDict
from scheduler.holiday_aware_scheduler import HolidayAwareScheduler

# ===== 基本配置 =====

# 消息代理配置
broker_url = 'redis://localhost:6379/0'
result_backend = 'redis://localhost:6379/0'

# 结果后端配置
result_backend_transport_options = {'retry_policy': {'timeout': 5.0}}

# 任务配置
# task_ignore_result = True
# task_store_eager_result = True
# task_serializer = 'json'
# result_serializer = 'json'
# accept_content = ['json']
result_expires = 3600

# 连接配置
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10

# 时区配置
timezone = 'Asia/Shanghai'
# enable_utc = True

# Worker 配置
worker_max_tasks_per_child = 1000

# ===== 任务路由配置 =====

task_routes = {
    # Copilot 相关任务使用专用队列
    'tasks.ops.assign_github_copilot_seats': {'queue': 'copilot'},
    'tasks.ops.accept_copilot_request': {'queue': 'copilot'},
    'tasks.ops.cancel_seats_inactive': {'queue': 'copilot'},
    'tasks.ops.sync_seat_status': {'queue': 'copilot'},
    # 其他任务自动路由到默认队列
}

# ===== 队列配置 =====
# 队列定义
task_queues = {
    'default': {'exchange': 'default', 'exchange_type': 'direct', 'routing_key': 'default'},
    'copilot': {'exchange': 'copilot', 'exchange_type': 'direct', 'routing_key': 'copilot'},
}

# 定义默认队列
task_default_queue = 'default'
# 定义默认路由键
task_default_routing_key = 'default'
# 定义默认交换机
task_default_exchange = 'default'

# ===== 导入模块 =====
imports = [
    'tasks.ads',
    'tasks.argus',
    'tasks.bitable_clean',
    'tasks.chat',
    'tasks.cloud_notifications_summary',
    'tasks.hgame',
    'tasks.mona',
    'tasks.ops',
    'tasks.weekly_meeting',
    'tasks.oauth',
]

# ===== Beat 调度配置 =====

# 获取 Lark 配置
bitables_config = PipeDict(config.lark.bitables)

# ===== 系统检查任务组 (默认队列) =====
check_task_group = {
    # 广告系统检查任务
    'ads_check': {
        'task': 'tasks.ads.run_check',
        'schedule': crontab(hour=9, minute=0),
        'args': [bitables_config | 'ads'],
    },
    # Mona 系统检查任务
    'mona_check': {
        'task': 'tasks.mona.run_check',
        'schedule': crontab(hour=9, minute=0),
        'args': [bitables_config | 'mona'],
    },
    # HGame 系统检查任务
    'hgame_check': {
        'task': 'tasks.hgame.run_check',
        'schedule': crontab(hour=10, minute=0),
        'args': [bitables_config | 'hgame'],
    },
}

# ===== GitHub Copilot 任务组 (copilot 队列) =====
copilot_task_group = {
    # 为已加入组织的用户自动分配 Copilot 座位
    'assign-copilot-seats': {
        'task': 'tasks.ops.assign_github_copilot_seats',
        'schedule': crontab(minute='*/5'),
        'options': {
            'expires': 60 * 5,  # 5分钟过期，防止任务累积
        },
    },
    # 定时同步 Copilot seats 信息
    'sync-seat-status': {
        'task': 'tasks.ops.sync_seat_status',
        'schedule': crontab(minute=0, hour='*/2'),
        'options': {
            'expires': 60 * 60 * 1,  # 1小时过期，防止任务累积
        },
    },
    # 清理长期不活跃的 Copilot seats（每月第一天凌晨）
    'cancel-not-activity-seats': {
        'task': 'tasks.ops.cancel_seats_inactive',
        'schedule': crontab(minute='30', hour='2', day_of_month='1'),
        'args': [1],
    },
    # 定时处理 Copilot 申请邮件
    'accept-copilot-request-from-email': {
        'task': 'tasks.ops.accept_copilot_request',
        'schedule': crontab(minute='*/10'),
    },
}

# ===== 清理任务组 (默认队列) =====
cleanup_task_group = {
    # 清理临时图片文件（日落时）
    'clean-temp-images-at-sunset': {
        'task': 'tasks.ops.clean_store',
        'schedule': solar('sunset', lat=31.2304, lon=121.4737),  # 使用上海的经纬度
        'args': (STORE_ROOT.as_posix(), 8),
    },
    # 清理 Mona 表格中的过期数据
    'clean_mona_tables': {
        'task': 'tasks.bitable_clean.clean_outdated_tables',
        'schedule': crontab(minute=0, hour=8, day_of_week='mon'),
        'kwargs': {
            'app_token': bitables_config | 'mona',
            'deadline': '两周前',
        },
    },
    # 清理 HGame 表格中的过期数据
    'clean_hgame_tables': {
        'task': 'tasks.bitable_clean.clean_outdated_tables',
        'schedule': crontab(minute=0, hour=8, day_of_week='mon'),
        'kwargs': {
            'app_token': bitables_config | 'hgame',
            'deadline': '两周前',
        },
    },
    # 清理广告表格中的过期数据
    'clean_ads_tables': {
        'task': 'tasks.bitable_clean.clean_outdated_tables',
        'schedule': crontab(minute=0, hour=8, day_of_week='mon'),
        'kwargs': {
            'app_token': bitables_config | 'ads',
            'deadline': '两周前',
        },
    },
}

# ===== 云消息任务组 (默认队列) =====
cloud_notifications_task_group = {
    # 定时同步最近一小时的云消息
    'sync_last_hour_messages': {
        'task': 'tasks.cloud_notifications_summary.sync_last_hour_messages',
        'schedule': crontab(minute=9, hour='*/1'),
    },
    # 每日云消息汇总
    'daily_summarize_cloud_messages': {
        'task': 'tasks.cloud_notifications_summary.summary_cloud_notifications',
        'schedule': crontab(minute=30, hour='10'),
    },
}

# ===== 会议报告任务组 (默认队列) =====
weekly_meeting_task_group = {
    'refresh_user_token': {
        'task': 'tasks.oauth.refresh_user_access_token',
        'schedule': crontab(minute='*/10'),
        'options': {
            'expires': 60 * 10,  # 10分钟过期，防止任务累积
        },
    },
    # 运维组周会日报准备（每周五下午2点，考虑节假日）
    'create_weekly_ops_meeting_report': {
        'task': 'tasks.weekly_meeting.create_weekly_meeting_wiki',
        'schedule': HolidayAwareScheduler(minute=0, hour=14, day_of_week='fri', country='CN'),
    },
}

beat_schedule = {}
beat_schedule.update(check_task_group)
beat_schedule.update(copilot_task_group)
beat_schedule.update(cleanup_task_group)
beat_schedule.update(cloud_notifications_task_group)
beat_schedule.update(weekly_meeting_task_group)

# ===== 安全配置 =====

# 任务注解
task_annotations = {
    '*': {
        'rate_limit': '100/m',  # 默认限流
    },
    # 'tasks.ops.assign_github_copilot_seats': {
    #     'rate_limit': '10/m',  # Copilot 任务限流更严格
    # },
    'tasks.ops.sync_seat_status': {
        'rate_limit': '1/h',  # 每小时最多6次
    },
}

# ===== 其他配置 =====
# 错误处理 (保留：确保任务可靠性)
task_reject_on_worker_lost = True
task_acks_late = True

# 预取设置 (保留默认值4，适合当前工作负载)
worker_prefetch_multiplier = 4
