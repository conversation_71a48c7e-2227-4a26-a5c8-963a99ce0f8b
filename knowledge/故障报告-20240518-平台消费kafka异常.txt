## 一、故障概述
故障发生时间：2024年5月18日
故障发现途径：查询数据发现
故障名称：平台消费kafka异常
故障等级：P2
故障影响范围：
1. 故障初步定位：kafka
2. 业务影响评估：平台消费kafka数据异常

## 二、故障分及稳定性指标
故障责任人：技术故障
故障分划分：P2

## 三、故障描述
1. 故障现象
kafka扩容后新节点未加入kafka代理配置，topic分区迁移至新节点后消费程序访问kafka代理，代理找不到新节点，导致消费失败。

## 四、故障处理过程
1. 故障处理开始时间：2024年5月18号
2. 故障处理措施：kafka代理接入新节点配置，后续切换为内网
3. 故障恢复完成时间：2024年5月18号
4. 故障处理人：@陈文文 
5. 恢复结果：成功恢复

## 五、故障原因分析
kafka集群扩容后节点未加入kafka代理配置。由于kafka在新增节点后并不会在感知到新节点加入后自动将数据rebalance到新集群中，这个过程需要手动分配。新增节点后，如果有topic的partition在新增的节点上，生产和消费的客户端都应该在hosts配置文件中增加新增的节点，否则无法生产消费，而且也不报错。
参考：https://blog.csdn.net/DreamWeaver_zhou/article/details/102521249

## 六、总结与建议
使用 Kafka 客户端进行生产消费，出现以下报错（超时、元数据丢失）可能的解决方法：
1. TimeoutException
常由于网络不通导致，可通过telent命令测试
2. Leader is not available
常见于 Topic 创建中、服务升级中，如果持续报错可能是 Topic 未创建或者服务端问题。
3. terminated during authentication
建议检查客户端 IP 是否在白名单中，同时比较容易忽略的点为，IP 地址已经在白名单中存在，但是未绑定到实例上。 参考文档：
  - https://www.volcengine.com/docs/6439/115071
  - https://www.volcengine.com/docs/6439/115070
4. Not authorized to access topics/ group
应该检查用户的 Topic 和 Group 相关权限，可以在实例的 ACL 管理中进行查看和修改。