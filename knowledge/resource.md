# 资源分类和命名、负责人

## 资源类型

- 机器 machine
   1. 阿里云: 主机名常见 txx-xxx-xxx, 实例 id 常见为 i-xxx , 其中 xx 都是占位符。
   2. ucloud: 实例名常见 uhost-xxx ，其中 xxx 也是占位符。
- 日志 logstore
- Redis redis
- HBase hbase
- 数据库 mysql, postgresql
- MongoDB mongo
- 安全组 security, sg, sucurity group
- 用户 user
- Polardb porlardb
- K8s节点 machine
- 域名 domainrecord
- 负载均衡 [lb clb glb]
- 弹性公网 [eip]

## 资源命名规范

1. 旧命名, 遵循正则表达式 `^(gke-)?(?P<vendor_short>[tvagu])(?P<region>[a-z]{2})-(?P<env>prod2?|test|dev|global|staging)-(?P<name>.*)$`
2. 新命名, 遵循正则表达式 `^(?P<product>[a-z]+)-(?P<module>[a-z0-9]+)-(?P<env>prod|test|dev|staging|global)-(?P<vendor_short>[tvagu])(?P<region>[a-z]{2})-(?P<extends>.*)$`

### 字段说明

- vendor_short 云厂商缩写, 例如:
  - t 阿里云(aliyun)
  - v 火山云(volc/volcengine)
  - a 亚马逊(aws)
  - g 谷歌云(gcp/google)
  - u "优刻得(ucloud)
- region 是城市的缩写, 长度为2~4 个字符, 例如:
  - jp 日本东京
  - sh 中国上海
  - va 美国弗吉尼亚
  - ca 美国加尼弗尼亚
  - tk 亚太东北 1 (东京)
  - so 韩国（首尔）
  - mb 亚太南部 1 (孟买)
  - sg 亚太东南 1 (新加坡)
  - sy 亚太东南 2 (悉尼)
  - kl 亚太东南 3 (吉隆坡)
  - tj 亚太东南 5 (雅加达)
  - mn 菲律宾（马尼拉）
  - bk 泰国（曼谷）
  - bj 华北 2
  - cd 西南1（成都）
  - fz 华东6（福州-本地地域）
  - gz 华南3（广州）
  - hz 华东 1
  - hy 华南2（河源）
  - hk 香港
  - ht 华北 5
  - nj 华东5（南京）
  - qd 中国青岛
  - sz 华南 1
  - wh 华中1（武汉-本地地域）
  - wlcb 华北6（乌兰察布）
  - zk 华北 3
  - fk 欧洲中部 1 (法兰克福)
  - ld 英国（伦敦）
  - ri 沙特（利雅得)
  - db 中东东部 1 (迪拜)
  - va 美国东部 1 (弗吉尼亚)
  - sv 美国西部 1 (硅谷)
- env 环境
  - prod/prod2 生存环境
  - test 测试环境
  - staging 预发环境
  - global 国际环境
  - cn 国内环境
- name 是资源的名称扩展。一般带有“项目名”, “资源类型”, “服务名”

### 一些特例

- 谷歌云机器: 实例 id 都是数字, 例如: "1792959716155179126"
- ucloud机器: 实例 id一般以 uhost 开头

### 举例

Q: "tsh是什么含义?"
A: "t" 是阿里云的意思，sh 是 shanghai(上海), tsh 就是阿里云上海.

Q: "vsh是什么含义?"
A: "v" 是 火山云的意思，sh 是 shanghai(上海), vsh 就是火山云上海.

Q: "tfk是什么含义?"
A: "t" 是阿里云的意思，fk 是 frankfurt (德国法兰克福), tfk 就是阿里云法兰克福 的缩写.

Q: dgameremake-h5-prod-k8s-tsh-*********** 的含义?
A: dgameremake 的 h5 项目，在生产环境(prod) 中的 k8s 节点，
它部署在阿里云(t)上海(sh)， ip 地址是 ***********

Q: tfk-wgame-preload-charge-0002 的含义?
A: wgame项目，在预演环境(preload) 中的资源， 它部署在阿里云(t) 法兰克福(fk)，charge 是支付用途, 0002 是资源序号。

# 游戏产品名称

- Hgame
- iGame
- XGame
- Dgame Dota DgameRemake 小冰冰传奇
- SGame
- Mona
- Samo

## 项目负责人

以下是项目名称和负责人, 项目可能是多个，'()' 内的是别名, '[]' 是一个列表

1. [WGame, Avatar, Party(Partopia)] 陈佩华
2. [dgame, dgameremake, dota] 庞玉海
3. Xgame 吴涵
4. 平台中心 徐佳骐
5. Mona 庞玉海
6. igame 朴文学
7. [公共, roc, rok]  杨方亮
8. Samo 杨方亮
9. Hgame 逯飞飞
10. Sgame(aoc) 吴涵
