#!/usr/bin/env bash

# 综合服务管理脚本
# 用于管理所有 Celery 服务：默认 worker、Copilot worker 和 Beat 调度器

set -e

current_dir=$(dirname "$0")

# 导入公共函数库
source "${current_dir}/common.sh"

# 启动所有服务
start_all() {
    log_info "启动所有 Celery 服务..."
    
    log_info "启动默认队列 worker..."
    ${current_dir}/control.sh start
    
    log_info "启动 Copilot 队列 worker..."
    ${current_dir}/copilot_control.sh start
    
    log_info "启动 Beat 调度器..."
    ${current_dir}/control.sh beat
    
    log_success "所有服务启动完成！"
}

# 停止所有服务
stop_all() {
    log_info "停止所有 Celery 服务..."
    
    log_info "停止 Beat 调度器..."
    ${current_dir}/control.sh beat_stop || log_warning "Beat 调度器可能已经停止"
    
    log_info "停止默认队列 worker..."
    ${current_dir}/control.sh stop || log_warning "默认 worker 可能已经停止"
    
    log_info "停止 Copilot 队列 worker..."
    ${current_dir}/copilot_control.sh stop || log_warning "Copilot worker 可能已经停止"
    
    log_success "所有服务停止完成！"
}

# 重启所有服务
restart_all() {
    log_info "重启所有 Celery 服务..."
    stop_all
    sleep 2
    start_all
}

# 检查所有服务状态
status_all() {
    log_info "检查所有 Celery 服务状态..."
    echo ""
    
    echo "=== 默认队列 Worker 状态 ==="
    ${current_dir}/control.sh status 2>/dev/null || echo "默认 worker 未运行"
    echo ""
    
    echo "=== Copilot 队列 Worker 状态 ==="
    ${current_dir}/copilot_control.sh status 2>/dev/null || echo "Copilot worker 未运行"
    echo ""
    
    echo "=== Beat 调度器状态 ==="
    check_process_status "$PWD/run/celery-beat.pid" "Beat 调度器"
}

# 显示服务日志
logs() {
    local service=${1:-"all"}
    
    case $service in
        default)
            log_info "显示默认队列 worker 日志..."
            tail -f logs/celery-ops_runner.log
            ;;
        copilot)
            log_info "显示 Copilot 队列 worker 日志..."
            tail -f logs/celery-copilot_worker.log
            ;;
        beat)
            log_info "显示 Beat 调度器日志..."
            tail -f logs/celery-beat.log
            ;;
        all|*)
            log_info "显示所有服务日志..."
            tail -f logs/celery-*.log
            ;;
    esac
}

# 清理 PID 和日志文件
cleanup() {
    log_info "清理 PID 和日志文件..."
    
    # 清理 PID 文件
    rm -f run/celery-*.pid
    ${current_dir}/copilot_control.sh cleanup
    
    # 可选：清理日志文件
    read -p "是否清理日志文件？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f logs/celery-*.log
        log_success "日志文件已清理"
    fi
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Celery 服务管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|cleanup|help}"
    echo ""
    echo "命令说明:"
    echo "  start        - 启动所有服务 (默认worker + Copilot worker + Beat)"
    echo "  stop         - 停止所有服务"
    echo "  restart      - 重启所有服务"
    echo "  status       - 检查所有服务状态"
    echo "  logs [TYPE]  - 显示日志文件"
    echo "                 TYPE: default|copilot|beat|all (默认: all)"
    echo "  cleanup      - 清理 PID 和日志文件"
    echo "  help         - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start           # 启动所有服务"
    echo "  $0 logs copilot    # 查看 Copilot worker 日志"
    echo "  $0 status          # 检查所有服务状态"
}

# 主命令处理
case "${1:-help}" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    status)
        status_all
        ;;
    logs)
        logs ${2:-"all"}
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
