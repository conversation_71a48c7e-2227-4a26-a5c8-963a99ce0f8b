#!/usr/bin/env bash

# 服务管理公共函数库
# 提供进程状态检查和时间格式化等通用功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 格式化 etime 输出为简洁格式
# 参数: $1 - etime 字符串 (如 "07:02", "1:07:02", "1-07:02:03")
# 返回: 格式化后的时间字符串 (如 "7m2s", "1h7m", "1d7h2m")
format_etime() {
    local etime=$1
    
    # 处理不同的 etime 格式
    # 格式可能是: "07:02", "1:07:02", "1-07:02:03"
    
    if [[ $etime =~ ^([0-9]+)-([0-9]+):([0-9]+):([0-9]+)$ ]]; then
        # 格式: 1-07:02:03 (天-小时:分钟:秒)
        local days=${BASH_REMATCH[1]}
        local hours=${BASH_REMATCH[2]}
        local minutes=${BASH_REMATCH[3]}
        # 去掉前导零
        days=$((10#$days))
        hours=$((10#$hours))
        minutes=$((10#$minutes))
        
        local result=""
        [ $days -gt 0 ] && result="${days}d"
        [ $hours -gt 0 ] && result="${result}${hours}h"
        [ $minutes -gt 0 ] && result="${result}${minutes}m"
        echo "$result"
        
    elif [[ $etime =~ ^([0-9]+):([0-9]+):([0-9]+)$ ]]; then
        # 格式: 1:07:02 (小时:分钟:秒)
        local hours=${BASH_REMATCH[1]}
        local minutes=${BASH_REMATCH[2]}
        # 去掉前导零
        hours=$((10#$hours))
        minutes=$((10#$minutes))
        
        local result=""
        [ $hours -gt 0 ] && result="${hours}h"
        [ $minutes -gt 0 ] && result="${result}${minutes}m"
        echo "$result"
        
    elif [[ $etime =~ ^([0-9]+):([0-9]+)$ ]]; then
        # 格式: 07:02 (分钟:秒)
        local minutes=${BASH_REMATCH[1]}
        local seconds=${BASH_REMATCH[2]}
        # 去掉前导零
        minutes=$((10#$minutes))
        seconds=$((10#$seconds))
        
        local result=""
        [ $minutes -gt 0 ] && result="${minutes}m"
        [ $seconds -gt 0 ] && result="${result}${seconds}s"
        echo "$result"
        
    else
        # 未知格式，原样返回
        echo "$etime"
    fi
}

# 计算进程运行时长
# 参数: $1 - 进程PID
# 返回: 格式化的运行时间字符串，如 " (已运行: 2h30m)"
get_process_uptime() {
    local pid=$1
    
    # 检查进程是否存在
    if ! ps -p $pid > /dev/null 2>&1; then
        echo ""
        return
    fi
    
    # 获取进程启动时间（从 epoch 开始的秒数）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local start_time=$(ps -o lstart -p $pid | tail -1 | xargs -I {} date -j -f "%a %b %d %H:%M:%S %Y" "{}" +%s 2>/dev/null || echo "")
    else
        # Linux
        local start_time=$(stat -c %Y /proc/$pid 2>/dev/null || echo "")
    fi
    
    if [ -z "$start_time" ]; then
        # 备用方法：使用 ps 获取进程运行时间
        local etime=$(ps -o etime -p $pid | tail -1 | tr -d ' ')
        # 格式化 etime 输出
        local formatted_time=$(format_etime "$etime")
        echo " (已运行: $formatted_time)"
        return
    fi
    
    # 计算运行时长
    local current_time=$(date +%s)
    local uptime_seconds=$((current_time - start_time))
    
    # 格式化运行时长
    local days=$((uptime_seconds / 86400))
    local hours=$(((uptime_seconds % 86400) / 3600))
    local minutes=$(((uptime_seconds % 3600) / 60))
    local seconds=$((uptime_seconds % 60))
    
    local uptime_str=""
    if [ $days -gt 0 ]; then
        uptime_str="${days}d"
    fi
    if [ $hours -gt 0 ]; then
        uptime_str="${uptime_str}${hours}h"
    fi
    if [ $minutes -gt 0 ]; then
        uptime_str="${uptime_str}${minutes}m"
    fi
    if [ -z "$uptime_str" ] || [ $uptime_seconds -lt 60 ]; then
        uptime_str="${uptime_str}${seconds}s"
    fi
    
    echo " (已运行: $uptime_str)"
}

# 检查进程状态并显示信息
# 参数: $1 - PID文件路径, $2 - 进程描述名称
# 返回: 0-运行中, 1-已停止, 2-PID文件不存在
check_process_status() {
    local pid_file=$1
    local process_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            local uptime_info=$(get_process_uptime $pid)
            echo "${process_name}正在运行 (PID: $pid)$uptime_info"
            return 0
        else
            echo "${process_name}已停止 (PID 文件存在但进程不存在)"
            return 1
        fi
    else
        echo "${process_name}已停止"
        return 2
    fi
}

# 检查并显示子进程状态
# 参数: $1 - 父进程PID
show_child_processes() {
    local parent_pid=$1
    
    # 检查子进程 worker
    local child_pids=$(pgrep -P $parent_pid 2>/dev/null || true)
    if [ -n "$child_pids" ]; then
        echo "子进程 worker 运行中:"
        for child_pid in $child_pids; do
            if ps -p $child_pid > /dev/null 2>&1; then
                # 获取进程名字
                local process_name=""
                if [[ "$OSTYPE" == "darwin"* ]]; then
                    # macOS - 使用 ps 获取进程名
                    process_name=$(ps -p $child_pid -o comm= 2>/dev/null | xargs basename 2>/dev/null || echo "worker")
                else
                    # Linux - 使用 ps 获取进程名
                    process_name=$(ps -p $child_pid -o comm= 2>/dev/null || echo "worker")
                fi
                
                local child_uptime_info=$(get_process_uptime $child_pid)
                echo "  - ${process_name} PID: $child_pid$child_uptime_info"
            fi
        done
    else
        echo "未发现子进程 worker"
    fi
}
