#!/usr/bin/env bash

set -e

current_dir=$(dirname "$0")

# 导入公共函数库
source "${current_dir}/common.sh"

CELERY_BIN=$(dirname "$current_dir")/.venv/bin/celery
CELERTY_APP=app.celery_app
NAME=ops_runner

start() {
    ${CELERY_BIN} -A ${CELERTY_APP} multi start ${NAME} -Q default -l info \
        --pidfile="$PWD/run/celery-%n.pid" \
        --logfile="$PWD/logs/celery-%n.log" \
        --autoscale=10,3
}

stop() {
    ${CELERY_BIN} multi stopwait ${NAME} \
        --pidfile="$PWD/run/celery-%n.pid"
}

restart() {
    stop
    start
}

start_beat() {
    ${CELERY_BIN} -A ${CELERTY_APP} beat --detach -l info \
        --pidfile="$PWD/run/celery-beat.pid" \
        --logfile="$PWD/logs/celery-beat.log"
}

stop_beat() {
    kill "$(cat "$PWD/run/celery-beat.pid")"
}

restart_beat() {
    stop_beat
    start_beat
}

restart_all() {
    restart && restart_beat
}

stop_all() {
    stop
    stop_beat
}

status() {
    check_process_status "$PWD/run/celery-${NAME}.pid" "默认 worker 主进程"
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        # 进程正在运行，获取PID并检查子进程
        local pid=$(cat "$PWD/run/celery-${NAME}.pid")
        show_child_processes $pid
    fi
}

case "$1" in
start)
    start
    ;;

stop)
    stop
    ;;

restart)
    stop
    start
    ;;

beat)
    start_beat
    ;;

beat_stop)
    stop_beat
    ;;

beat_restart)
    stop_beat
    start_beat
    ;;

stop_all)
    stop_all
    ;;

restart_all)
    restart_all
    ;;

status)
    status
    ;;

*)
    echo "Usage: $0 {start|stop|restart|beat|beat_stop|beat_restart|stop_all|restart_all|status}"
    exit 1
    ;;
esac
