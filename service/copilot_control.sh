#!/usr/bin/env bash

# Copilot 专用队列 Worker 控制脚本
# 用于管理处理 GitHub Copilot 相关任务的独立 worker

set -e

current_dir=$(dirname "$0")

# 导入公共函数库
source "${current_dir}/common.sh"

CELERY_BIN=$(dirname "$current_dir")/.venv/bin/celery
CELETY_APP=app.celery_app
NAME=copilot_worker
QUEUE=copilot

start() {
    echo "启动 Copilot worker..."
    ${CELERY_BIN} -A ${CELETY_APP} multi start ${NAME} -Q ${QUEUE} -l info \
        --pidfile="$PWD/run/celery-${NAME}.pid" \
        --logfile="$PWD/logs/celery-${NAME}.log" \
        --concurrency=2
}

stop() {
    echo "停止 Copilot worker..."
    ${CELERY_BIN} multi stopwait ${NAME} \
        --pidfile="$PWD/run/celery-${NAME}.pid"
}

restart() {
    echo "重启 Copilot worker..."
    stop
    start
}

status() {
    check_process_status "$PWD/run/celery-${NAME}.pid" "Copilot worker 主进程"
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        # 进程正在运行，获取PID并检查子进程
        local pid=$(cat "$PWD/run/celery-${NAME}.pid")
        show_child_processes $pid
    fi
}

# 清理 PID 文件
cleanup() {
    echo "清理 Copilot worker PID 文件..."
    rm -f "$PWD/run/celery-${NAME}.pid"
}

case "$1" in
start)
    start
    ;;

stop)
    stop
    ;;

restart)
    restart
    ;;

status)
    status
    ;;

cleanup)
    cleanup
    ;;

*)
    echo "用法: $0 {start|stop|restart|status|cleanup}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动 Copilot worker"
    echo "  stop    - 停止 Copilot worker"
    echo "  restart - 重启 Copilot worker"
    echo "  status  - 检查 Copilot worker 状态"
    echo "  cleanup - 清理 PID 文件"
    exit 1
    ;;
esac
