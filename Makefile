clean:
	@pyc_count=$$(find . -type f -name "*.pyc" -delete -print | wc -l); \
	cache_count=$$(find . -type d -name "__pycache__" -exec rm -r {} + -print | wc -l); \
	pytest_count=$$(find . -type d -name ".pytest_cache" -exec rm -r {} + -print | wc -l); \
	if [ $$pyc_count -gt 0 ] || [ $$cache_count -gt 0 ] || [ $$pytest_count -gt 0 ]; then \
		printf "\033[0;32mCleaned: %d .pyc files, %d __pycache__ directories, %d .pytest_cache directories\033[0m\n" $$pyc_count $$cache_count $$pytest_count; \
	else \
		printf "Nothing to clean.\n"; \
	fi
