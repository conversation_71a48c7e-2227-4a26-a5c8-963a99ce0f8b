# https://nextjs.org/docs/pages/building-your-application/deploying/static-exports#deploying
server {
	listen 80;
	server_name eagle.lilithgame.com;
	return 301 https://$host$request_uri;
}

server {
        listen       443 ssl http2;
        server_name  eagle.lilithgame.com;
        # apt install libnginx-mod-http-headers-more-filter
        # server_tokens off;
        client_max_body_size 100M;

        ssl_certificate /etc/nginx/ssl/lilithgame.com.pem;
        ssl_certificate_key /etc/nginx/ssl/lilithgame.com.key;
        # ssl_protocols TLSv1.2 TLSv1.3;
        # ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;

        root /opt/argus-frontend/out;

        location / {
            try_files $uri $uri.html $uri/ =404;
        }

        location /api/ {
            proxy_set_header   X-Real-IP        $remote_addr;
            proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
            proxy_set_header   Host             $http_host;
            proxy_http_version 1.1;
            proxy_connect_timeout   300;
            proxy_send_timeout      300;
            proxy_read_timeout      300;
            proxy_pass http://127.0.0.1:18000/;
            rewrite ^/api/(.*)$ /api/$1 break;
        }

        error_page 404 /404.html;
        location = /404.html {
            internal;
        }
}
