{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://aiops.lilithgames.com/config.schema.json", "title": "AIOps Configuration Schema", "description": "JSON Schema for ops_mate configuration file", "type": "object", "properties": {"debug": {"type": "boolean", "description": "Enable debug mode", "default": false}, "verbose": {"type": "boolean", "description": "Enable verbose mode", "default": false}, "dry_run": {"type": "boolean", "description": "Enable dry run mode, do not execute any action", "default": false}, "admins": {"type": "array", "description": "Admin emails", "items": {"type": "string", "format": "email"}, "default": []}, "limit_pdf_max_size": {"type": "string", "description": "PDF file max size limit, e.g. 50M, 100M", "default": "50M", "pattern": "^\\d+[KMG]$"}, "oma": {"$ref": "#/$defs/OmaConfig"}, "mongo": {"$ref": "#/$defs/MongoConfig"}, "lark": {"$ref": "#/$defs/LarkConfig"}, "llms": {"type": "object", "description": "Large or Small Language models", "additionalProperties": {"$ref": "#/$defs/LLMConfig"}, "default": {}}, "mcp": {"type": "object", "description": "MCP servers configuration", "additionalProperties": {"oneOf": [{"$ref": "#/$defs/MCPStdioConfig"}, {"$ref": "#/$defs/MCPSSEConfig"}]}}, "dify": {"$ref": "#/$defs/DifyConfig"}, "search": {"$ref": "#/$defs/SearchEngineConfig"}, "github": {"$ref": "#/$defs/GithubConfig"}, "email": {"$ref": "#/$defs/EmailConfig"}, "grafanas": {"type": "object", "description": "Grafana dashboards", "additionalProperties": {"$ref": "#/$defs/GrafanaConfig"}, "default": {}}, "rootcause": {"$ref": "#/$defs/RootCauseConfig"}, "cloud_notification_groups": {"type": "array", "description": "Groups that receive cloud notifications", "items": {"$ref": "#/$defs/CloudNotificationGroup"}}, "cloud_summary_notify": {"type": "array", "description": "Cloud summary notify groups", "items": {"$ref": "#/$defs/CloudSummaryNotifyConfig"}}}, "required": ["mongo", "lark", "mcp", "rootcause", "cloud_notification_groups", "cloud_summary_notify"], "additionalProperties": false, "$defs": {"OmaConfig": {"type": "object", "properties": {"base_url": {"type": "string", "format": "uri", "description": "OMA API base URL", "default": "https://oma-api.lilithgame.com/api/"}, "token": {"type": "string", "description": "OMA API token"}}, "required": ["token"], "additionalProperties": false}, "MongoConfig": {"type": "object", "properties": {"mgo_uri": {"type": "string", "description": "MongoDB connection URI", "default": "mongodb://localhost:27017"}, "mgo_db": {"type": "string", "description": "MongoDB database name", "default": "aiops"}, "cloud_notification_collection": {"type": "string", "description": "MongoDB collection for cloud notifications", "default": "cloud_notifications"}}, "required": ["mgo_uri"], "additionalProperties": false}, "LarkConfig": {"type": "object", "properties": {"service_account": {"$ref": "#/$defs/ServiceAccountConfig"}, "apps": {"type": "object", "description": "Lark applications configuration", "additionalProperties": {"$ref": "#/$defs/LarkAppConfig"}}, "bitable_name": {"type": "string", "description": "Default bitable name", "default": "巡检条目"}, "bitables": {"type": "object", "description": "Bitable configurations", "additionalProperties": {"type": "string"}}, "card": {"$ref": "#/$defs/AIMessageCard"}}, "required": ["service_account", "apps", "bitables"], "additionalProperties": false}, "ServiceAccountConfig": {"type": "object", "properties": {"app_name": {"type": "string", "description": "Application name"}, "user_token_storage": {"type": "string", "description": "User access token storage Redis address", "default": "redis://localhost:6379/4"}, "redis_token_key": {"type": "string", "description": "Redis key for user access token", "default": "lark_user_token"}, "weekly_wiki_report_group": {"type": "string", "description": "Weekly wiki report group chat ID"}}, "required": ["app_name", "weekly_wiki_report_group"], "additionalProperties": false}, "LarkAppConfig": {"type": "object", "properties": {"app_id": {"type": "string", "description": "Lark app ID"}, "app_secret": {"type": "string", "description": "Lark app secret"}, "verification_token": {"type": ["string", "null"], "description": "Verification token for webhook"}, "encrypt_key": {"type": "string", "description": "Encryption key", "default": ""}}, "required": ["app_id", "app_secret"], "additionalProperties": false}, "AIMessageCard": {"type": "object", "properties": {"template_id": {"type": "string", "description": "Message card template ID"}, "footer": {"type": "string", "description": "Message card footer", "default": "本内容由AI生成，如有问题请联系管理员。"}}, "required": ["template_id"], "additionalProperties": false}, "LLMConfig": {"type": "object", "properties": {"model_type": {"type": "string", "description": "Model type", "enum": ["chat", "embedding"], "default": "chat"}, "provider": {"type": "string", "description": "Model provider", "enum": ["azure", "deepseek", "groq-deepseek", "openai", "ollama", "huggingface", "dashscope", "modelscope", "xai", "vertexai", "anthropic"], "default": "openai"}, "model": {"type": ["string", "null"], "description": "Model name"}, "credentials_json_path": {"type": ["string", "null"], "description": "Path to VertexAI credentials JSON file"}, "vertex_location": {"type": ["string", "null"], "description": "VertexAI location"}, "api_key": {"type": ["string", "null"], "description": "API key"}, "api_version": {"type": ["string", "null"], "description": "API version"}, "base_url": {"type": ["string", "null"], "format": "uri", "description": "Base URL for API"}, "deployment": {"type": "string", "description": "Deployment name (for Azure)", "default": ""}, "max_token": {"type": ["integer", "null"], "description": "Maximum tokens", "minimum": 1}, "max_completion_tokens": {"type": ["integer", "null"], "description": "Maximum completion tokens", "minimum": 1}, "temperature": {"type": "number", "description": "Temperature for response generation", "minimum": 0.0, "maximum": 2.0, "default": 0.0}, "top_p": {"type": "number", "description": "Top-p sampling parameter", "minimum": 0.0, "maximum": 1.0, "default": 1.0}, "top_k": {"type": "integer", "description": "Top-k sampling parameter", "minimum": 0, "default": 0}, "stream": {"type": "boolean", "description": "Enable streaming responses", "default": false}, "enable_thinking": {"type": ["boolean", "null"], "description": "Enable thinking mode (for Qwen3)"}}, "additionalProperties": false}, "MCPStdioConfig": {"type": "object", "properties": {"transport": {"type": "string", "enum": ["stdio"], "default": "stdio"}, "command": {"type": "string", "description": "Command to execute MCP server", "default": "uvx"}, "args": {"type": ["array", "null"], "description": "Arguments to pass to the MCP server command", "items": {"type": "string"}}, "env": {"type": "object", "description": "Environment variables for the MCP server", "additionalProperties": {"type": "string"}, "default": {}}}, "required": ["command"], "additionalProperties": false}, "MCPSSEConfig": {"type": "object", "properties": {"transport": {"type": "string", "enum": ["sse"]}, "url": {"type": "string", "format": "uri", "description": "URL of the MCP server"}, "env": {"type": "object", "description": "Environment variables for the MCP server", "additionalProperties": {"type": "string"}, "default": {}}}, "required": ["url"], "additionalProperties": false}, "DifyConfig": {"type": "object", "properties": {"base_url": {"type": "string", "format": "uri", "description": "Dify API base URL", "default": "https://api.dify.com/v1"}, "api_key": {"type": "string", "description": "Dify API key"}}, "required": ["api_key"], "additionalProperties": false}, "SearchEngineConfig": {"type": "object", "properties": {"name": {"type": "string", "description": "Search engine name"}, "api_key": {"type": "string", "description": "Search engine API key"}}, "required": ["name", "api_key"], "additionalProperties": false}, "GithubConfig": {"type": "object", "properties": {"access_token": {"type": "string", "description": "GitHub access token"}, "org": {"type": "string", "description": "GitHub organization"}, "bitable_app": {"type": "string", "description": "Bitable app ID"}, "bitable_table": {"type": "string", "description": "Bitable table ID"}, "view_id": {"type": "string", "description": "Bitable view ID"}, "llm_model": {"type": ["string", "null"], "description": "LLM model to use for GitHub operations"}}, "required": ["access_token", "org", "bitable_app", "bitable_table", "view_id"], "additionalProperties": false}, "EmailConfig": {"type": "object", "properties": {"imap_server": {"type": "string", "description": "IMAP server address", "default": "imap.feishu.cn"}, "imap_port": {"type": "integer", "description": "IMAP server port", "minimum": 1, "maximum": 65535, "default": 993}, "username": {"type": "string", "description": "Email username"}, "password": {"type": "string", "description": "Email password"}, "folder": {"type": "string", "description": "Email folder name", "default": "Copilot"}}, "required": ["username", "password"], "additionalProperties": false}, "GrafanaConfig": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON> alias name"}, "datasource": {"type": "string", "description": "Grafana datasource ID"}, "base_url": {"type": "string", "format": "uri", "description": "Grafana base URL", "default": "https://oma-monitor.lilithgame.com"}, "dashboard_id": {"type": "string", "description": "Grafana dashboard ID"}, "views": {"type": "array", "description": "Dashboard views configuration", "items": {"$ref": "#/$defs/GrafanaViewConfig"}, "default": []}}, "required": ["name", "datasource", "dashboard_id"], "additionalProperties": false}, "GrafanaViewConfig": {"type": "object", "properties": {"name": {"type": ["string", "null"], "description": "View name"}, "view_id": {"type": "string", "description": "View ID"}}, "required": ["view_id"], "additionalProperties": false}, "RootCauseConfig": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Root cause analysis service URL"}, "token": {"type": ["string", "null"], "description": "Authentication token"}, "timeout": {"type": ["integer", "null"], "description": "Request timeout in seconds", "minimum": 1}}, "required": ["url"], "additionalProperties": false}, "CloudNotificationGroup": {"type": "object", "properties": {"chat_id": {"type": "string", "description": "Group chat ID"}, "kind": {"type": ["string", "null"], "description": "Group kind/type"}}, "required": ["chat_id"], "additionalProperties": false}, "CloudSummaryNotifyConfig": {"type": "object", "properties": {"group": {"type": "string", "description": "Notify group name", "default": ""}, "chat_id": {"type": "string", "description": "Notify group chat ID"}}, "required": ["chat_id"], "additionalProperties": false}}}