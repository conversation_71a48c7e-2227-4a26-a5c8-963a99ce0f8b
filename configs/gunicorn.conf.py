# coding=utf-8
try:
    import setproctitle  # noqa: F401

    proc_name = 'eagle-api'
except ImportError:
    pass

# try:
#     from uvicorn.workers import UvicornWorker  # noqa: F401

#     worker_class = 'uvicorn.workers.UvicornWorker'
# except ImportError:
#     pass

bind = '127.0.0.1:18000'

workers = 4


max_requests = 10000
max_requests_jitter = int(max_requests * 0.1)
# max_requests = 0

timeout = 500
accesslog = '-'
errorlog = '-'

access_log_format = (
    '%(h)s %(l)s %(u)s pid:%(p)s  %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
)
