[project]
name = "ops-mate"
version = "0.1.0"
readme = "README.md"
authors = [{ name = "clyde", email = "<EMAIL>" }]
requires-python = ">=3.12,<3.13"
dependencies = [
    # Web框架及扩展
    "flask ~= 3.1.0",
    "flask-restful ~= 0.3",
    "flask-cors >= 5.0.0",
    "flask-pymongo >= 2.3.0",
    "gunicorn[setproctitle] ~= 23.0",
    # 数据处理和分析
    "matplotlib ~= 3.9",
    "pydantic[email] ~= 2.10",
    "protobuf ~= 5.0",
    "orjson >= 3.10, < 4",
    # 网络和HTTP
    "httpx",
    "selenium ~= 4.26",
    "playwright~=1.49",
    "beautifulsoup4 ~= 4.12",
    "imap-tools ~= 1.7",
    # 任务队列
    "celery[redis,solar]~=5.4",
    # 时间处理
    "pendulum ~= 3.0",
    # 文件处理
    "pillow ~= 11.1",
    "pypdf[image] ~= 5.0",
    "pyyaml ~= 6.0.2",
    # 云服务
    "lark-oapi ~= 1.3",
    # "alibabacloud-sls20201230 ~= 5.3",
    # 工具类
    "python-dotenv ~= 1.0",
    "loguru>=0.7.3",
    "holidays>=0.72",
]

[tool.uv]
default-groups = ["framework", "llm-tools"]

[[tool.uv.index]]
name = "aliyun"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
# url = "https://mirrors.aliyun.com/pypi/simple/"
default = true


[dependency-groups]
framework = [
    "langchain >=0.3,<0.4",
    "langchain-community >=0.3,<0.4",
    "langchain-experimental >=0.3,<0.4",
    "langchain-chroma ~= 0.2",
    "langchain-openai ~= 0.3",
    "langchain-ollama>=0.2.2",
    "openai ~= 1.52",
    "langgraph ~= 0.5.0",
    "dashscope ~= 1.20",
    "langchain-deepseek>=0.1.2",
    "langchain-groq>=0.2.4",
    "langchain-mcp-adapters~=0.1",
    "langchain-google-vertexai~=2.0.24",
    "fastmcp>=2.3.4",
    "anthropic>=0.52.0",
]
llm-tools = ["pygithub ~= 2.4", "wikipedia~=1.4"]
dev = [
    "jupyterlab ~= 4.2",
    "notebook ~= 7.3",
    "ruff~=0.9",
    "pandas ~= 2.2",
    "mypy ~= 1.14",
    "poethepoet ~= 0.32",
]
test = ["pytest~=8.0.0", "pytest-mock", "pytest-playwright>=0.7.0"]

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q -s --disable-warnings --browser=chromium"
testpaths = ["tests"]

[tool.mypy]
python_version = "3.12"
files = ['lib', 'tasks', 'tests']
ignore_missing_imports = true

# warn_redundant_casts = true
# warn_unused_ignores = false
# pretty = true
# hide_error_codes = false

# ref: https://careers.wolt.com/en/blog/tech/professional-grade-mypy-configuration
check_untyped_defs = false
no_implicit_optional = true
warn_return_any = false
hide_error_codes = false
warn_unused_ignores = false

disallow_incomplete_defs = true
disallow_untyped_defs = false
disallow_untyped_decorators = true
disallow_any_unimported = false

[tool.ruff]
line-length = 100
fix = true
extend-exclude = ["*.ipynb", "venv/", ".venv", "migrations"]

[tool.ruff.format]
quote-style = "single"

[tool.ruff.lint]
select = [
    "E",      # pycodestyle error
    "F",      # pyflakes
    "W",      # pycodestyle (warning)list_models
    "RET501",
    "RET502", # better return None handling
]
fixable = ['E', 'F', "I"]
extend-select = ["I"]

[tool.ruff.lint.isort]
combine-as-imports = true

[tool.ruff.lint.pydocstyle]
convention = "google"


[tool.poe.tasks]
models = "pytest -k 'list_models' --durations=0"
template = "pytest -m \"template\""
copilot = "pytest -k 'extract_copilot and lilith-ds-distill' -vv --durations=0"
playwright = "playwright install chromium-headless-shell"
download_model = 'modelscope download --model="Qwen/Qwen3-0.6B" --local_dir ./llm_model'
