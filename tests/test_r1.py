from typing import Callable, Dict, List

import pendulum
import pytest
from pydantic import BaseModel, Field

from conftest import AVAILABLE_MODELS
from lib.output_parsers.mixins import OutputParserMixin
from lib.utils.reasoner import split_tool_calls
from lib.utils.template import render_template
from prompt.deepseek_r1 import tool_calling_template, tool_use_template


class ToolCall(BaseModel):
    function: str = Field(..., description='function name')
    args: dict = Field(default_factory=dict, description='arguments to pass in')


class ToolCalls(BaseModel, OutputParserMixin):
    tool_calls: list[ToolCall] = Field(default_factory=list, description='list of tool calls')


class Tool:
    def __init__(self, func: Callable):
        self.func = func
        self.name = func.__name__
        self.description = func.__doc__ or ''

        # 自动生成 args_schema
        class Schema(BaseModel):
            pass

        for param, param_type in func.__annotations__.items():
            if param != 'return':
                field = Field(..., description=f'{param} parameter')
                setattr(Schema, param, (param_type, field))

        self.args_schema = Schema

    def __call__(self, *args, **kwargs):
        return self.func(*args, **kwargs)

    @property
    def args(self):
        return self.args_schema.schema()


class ToolBinder:
    def __init__(self, tools: List[Tool]):
        self.tools = {tool.name: tool for tool in tools}
        self.tool_definitions = self._create_tool_definitions()

    def _create_tool_definitions(self) -> str:
        definitions = []
        for tool in self.tools.values():
            definitions.append(f"""
            <function>
                <name>{tool.name}</name>
                <description>{tool.description}</description>
                <args>{tool.args}</args>
            </function>
            """)
        return '\n\n'.join(definitions)

    def bind(self, llm):
        def wrapped_llm(messages):
            response = llm.invoke(messages)
            tool_calls = []
            if '<tool_calls>' in response.content:
                tool_calls = self._parse_tool_calls(response.content).get('tool_calls', [])
            return {'response': response, 'tool_calls': tool_calls}

        return wrapped_llm

    def _parse_tool_calls(self, content: str) -> List[Dict]:
        try:
            return split_tool_calls(content)
        except Exception as e:
            print(f'Exception: {e}')
            return []


def add(a: int, b: int) -> int:
    """Sum of a and b.

    Args:
        a: int , an integer number
        b: int , an integer number

    Returns:
        sum of a and b, also a iteger number.
    """
    return a + b


def sub(a: int, b: int) -> int:
    """Subscription b from a.

    Args:
        a: int , an integer number
        b: int , an integer number

    Returns:
        return a - b, which is also an integer number.
    """
    return a - b


def multiply(a: int, b: int) -> int:
    """Multiply a and b.

    Args:
        a: int , an integer number
        b: int , an integer number

    Returns:
        an integer number.
    """
    return a * b


def current_time() -> str:
    """Return current time.

    Returns:
        current datetime in string formamt.
    """
    return 'current time is ' + pendulum.now().to_datetime_string()


tools = [
    Tool(func=add),
    Tool(func=sub),
    Tool(func=multiply),
    Tool(current_time),
]

tool_binder = ToolBinder(tools)


def create_prompt(tool_scratchpad: str, q: str) -> str:
    context = dict(
        tool_scratchpad=tool_scratchpad,
        question=q,
    )
    return render_template(tool_use_template, context)


def create_tools_prompt(q: str) -> str:
    context = dict(
        tool_definitions=tool_binder.tool_definitions,
        format_instructions=ToolCalls.get_format_instructions(),
        question=q,
    )
    return render_template(tool_calling_template, context)


questions = [
    # '1 + 2 等于多少, 并且 7 * 10 又是多少呢, 最后他们的结果加一起是多少?',
    '现在几点了',
    # '1.11 和 1.9 哪个大?',
]


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_r1_tools(load_llm, model_name):
    llm = load_llm(model_name)
    llm_with_tools = tool_binder.bind(llm)

    for q in questions:
        # 是否需要工具调用
        tool_prompt = create_tools_prompt(q)
        print('Tool Prompt:', tool_prompt)
        result = llm_with_tools([('human', tool_prompt)])

        print('Question =>', q)
        # print('Response =>', result['response'].content)
        print('Tool calls:', result['tool_calls'])

        # 执行函数
        scratchpad = []
        for tc in result['tool_calls']:
            function_name = tc['function']
            f = tool_binder.tools.get(function_name)
            if f:
                args = tc['args']
                result = f(**args)
                scratchpad.append(f'{f.name}{list(args.values())} = {result}')

        tool_scratchpad = ''
        for idx, tr in enumerate(scratchpad, start=1):
            tool_scratchpad += f'[tool_use {idx} begin]{tr}[tool_use {idx} end]\n'

        # 将工具调用结果放入tool scratchpad，让LLM回答
        tool_use_prompt = create_prompt(tool_scratchpad, q)
        print('Tool Use Prompt:', tool_use_prompt)

        final_result = llm.invoke([('human', tool_use_prompt)])
        print('Final Raw response:', final_result)
        print('Final Raw metadata:', final_result.response_metadata)
        print('Final:', final_result.content.strip())

        print('*' * 30)


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_deepseek(load_llm, model_name):
    llm = load_llm(model_name)

    response = llm.invoke(
        [
            ('human', '9.9和9.11 哪个更大?'),
        ]
    )

    think_content = response.additional_kwargs.get('reasoning_content', '').strip()
    if think_content:
        print(f'THINK >\n{think_content}\n')
    print(response.content)


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_reason_think(load_llm, model_name):
    llm = load_llm(model_name)

    prompt = (
        # '请首先思考, 然后回答用户的问题.'
        # '思考过程必须包裹在<think></think>标签中。\n'
        # '例如: <think>思考过程</think>\n答案\n\n'
        # '用户问题:\n{question}'
        '用户问题:\n{question}'
    )

    response = llm.invoke(
        [
            ('human', prompt.format(question='9.9和 9.11 哪个大?')),
        ]
    )

    # result = split_think_content(response.content)
    # if result.think_exists:
    print(f'THINK:\n{response.additional_kwargs["reasoning_content"]}')
    print(f'Answer:\n{response.content}')
