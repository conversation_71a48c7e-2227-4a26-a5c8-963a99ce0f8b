"""单元测试：测试根因分析API的参数解析功能."""

import json
from urllib.parse import quote

import pytest
from flask import Flask
from werkzeug.datastructures import MultiDict

from lib.api.fields.rootcause import LarkTriggerContext, QuickRootCauseRequest
from lib.api.rootcause import bp


def test_lark_trigger_context_from_dict():
    """测试从字典创建飞书触发上下文."""
    # 准备测试数据
    test_data = {
        'chatId': 'test_chat_id',
        'appId': 'test_app_id',
        'chatName': '测试群聊',
        'chatType': 2,
        'messageIds': ['msg123', 'msg456'],
        'actionTime': 1625123456789,
        'from': 'message_action',
        'avatarKey': 'test_avatar_key',
    }

    # 执行测试
    context = LarkTriggerContext.from_dict(test_data)

    # 验证结果
    assert context.chat_id == 'test_chat_id'
    assert context.app_id == 'test_app_id'
    assert context.chat_name == '测试群聊'
    assert context.chat_type == 2
    assert context.message_ids == ['msg123', 'msg456']
    assert context.action_time == 1625123456789
    assert context.from_source == 'message_action'
    assert context.avatar_key == 'test_avatar_key'


def test_quick_rootcause_request_from_request_args():
    """测试从请求参数创建快速根因分析请求."""
    # 准备测试数据
    trigger_context = {
        'chatId': 'test_chat_id',
        'appId': 'test_app_id',
        'chatName': '测试群聊',
        'chatType': 2,
        'messageIds': ['msg123', 'msg456'],
        'actionTime': 1625123456789,
        'from': 'message_action',
        'avatarKey': 'test_avatar_key',
    }

    bdp_launch_query = {
        '__trigger_id__': 'test_trigger_id',
    }

    # URL编码参数
    args = MultiDict(
        {
            'code': 'test_code',
            'from': 'message_action',
            'bdp_launch_query': quote(json.dumps(bdp_launch_query)),
            'trigger_context': quote(json.dumps(trigger_context)),
        }
    )

    # 执行测试
    req = QuickRootCauseRequest.from_request_args(args)

    # 验证结果
    assert req.trigger_id == 'test_trigger_id'
    assert req.callback_code == 'test_code'
    assert req.context.chat_id == 'test_chat_id'
    assert req.context.app_id == 'test_app_id'
    assert req.context.message_ids == ['msg123', 'msg456']
    assert req.context.from_source == 'message_action'


def test_validate_success():
    """测试参数验证成功的情况."""
    # 准备有效数据的请求对象
    context = LarkTriggerContext(
        chat_id='test_chat_id',
        app_id='test_app_id',
        chat_name='测试群聊',
        chat_type=2,
        message_ids=['msg123'],
        action_time=1625123456789,
        from_source='message_action',
    )
    req = QuickRootCauseRequest(
        trigger_id='test_trigger_id',
        context=context,
        callback_code='test_code',
    )

    # 验证不应抛出异常
    req.validate()


def test_validate_missing_trigger_id():
    """测试缺少触发ID的验证失败情况."""
    # 准备缺少trigger_id的请求对象
    context = LarkTriggerContext(
        chat_id='test_chat_id',
        app_id='test_app_id',
        chat_name='测试群聊',
        chat_type=2,
        message_ids=['msg123'],
        action_time=1625123456789,
        from_source='message_action',
    )
    req = QuickRootCauseRequest(
        trigger_id='',  # 空触发ID
        context=context,
        callback_code='test_code',
    )

    # 验证应抛出ValueError异常
    with pytest.raises(ValueError) as exc_info:
        req.validate()
    assert '缺少必要参数: trigger_id' in str(exc_info.value)


def test_validate_missing_chat_id():
    """测试缺少聊天ID的验证失败情况."""
    # 准备缺少chat_id的请求对象
    context = LarkTriggerContext(
        chat_id='',  # 空聊天ID
        app_id='test_app_id',
        chat_name='测试群聊',
        chat_type=2,
        message_ids=['msg123'],
        action_time=1625123456789,
        from_source='message_action',
    )
    req = QuickRootCauseRequest(
        trigger_id='test_trigger_id',
        context=context,
        callback_code='test_code',
    )

    # 验证应抛出ValueError异常
    with pytest.raises(ValueError) as exc_info:
        req.validate()
    assert '缺少必要参数: chat_id' in str(exc_info.value)


def test_validate_missing_message_ids():
    """测试缺少消息ID的验证失败情况."""
    # 准备缺少message_ids的请求对象
    context = LarkTriggerContext(
        chat_id='test_chat_id',
        app_id='test_app_id',
        chat_name='测试群聊',
        chat_type=2,
        message_ids=[],  # 空消息ID列表
        action_time=1625123456789,
        from_source='message_action',
    )
    req = QuickRootCauseRequest(
        trigger_id='test_trigger_id',
        context=context,
        callback_code='test_code',
    )

    # 验证应抛出ValueError异常
    with pytest.raises(ValueError) as exc_info:
        req.validate()
    assert '缺少必要参数: message_ids' in str(exc_info.value)


def test_quick_rootcause_endpoint():
    """测试根因分析API端点的参数解析功能."""
    # 创建Flask测试应用
    app = Flask(__name__)
    app.register_blueprint(bp, url_prefix='/api/argus')

    # 准备测试数据
    trigger_context = {
        'chatId': 'test_chat_id',
        'appId': 'test_app_id',
        'chatName': '测试群聊',
        'chatType': 2,
        'messageIds': ['msg123', 'msg456'],
        'actionTime': 1625123456789,
        'from': 'message_action',
        'avatarKey': 'test_avatar_key',
    }

    bdp_launch_query = {
        '__trigger_id__': 'test_trigger_id',
    }

    # 构建查询URL
    query_string = (
        f'?code=test_code'
        f'&from=message_action'
        f'&bdp_launch_query={quote(json.dumps(bdp_launch_query))}'
        f'&trigger_context={quote(json.dumps(trigger_context))}'
    )

    # 使用测试客户端发送请求
    with app.test_client() as client:
        response = client.get(f'/api/argus{query_string}')

        # 验证响应
        assert response.status_code == 200
        assert response.data.decode('utf-8') == '已发起诊断'


def test_quick_rootcause_endpoint_missing_params():
    """测试根因分析API端点的参数缺失情况."""
    # 创建Flask测试应用
    app = Flask(__name__)
    app.register_blueprint(bp, url_prefix='/api/argus')

    # 准备缺少message_ids的测试数据
    trigger_context = {
        'chatId': 'test_chat_id',
        'appId': 'test_app_id',
        'chatName': '测试群聊',
        'chatType': 2,
        'messageIds': [],  # 空消息ID列表
        'actionTime': 1625123456789,
        'from': 'message_action',
    }

    bdp_launch_query = {
        '__trigger_id__': 'test_trigger_id',
    }

    # 构建查询URL
    query_string = (
        f'?code=test_code'
        f'&from=message_action'
        f'&bdp_launch_query={quote(json.dumps(bdp_launch_query))}'
        f'&trigger_context={quote(json.dumps(trigger_context))}'
    )

    # 使用测试客户端发送请求
    with app.test_client() as client:
        response = client.get(f'/api/argus{query_string}')

        # 验证响应
        assert response.status_code == 400
        assert '缺少必要参数: message_ids' in response.data.decode('utf-8')


def test_quick_rootcause_endpoint_invalid_json():
    """测试根因分析API端点的JSON解析错误情况."""
    # 创建Flask测试应用
    app = Flask(__name__)
    app.register_blueprint(bp, url_prefix='/api/argus')

    # 构建包含无效JSON的查询URL
    query_string = (
        '?code=test_code'
        '&from=message_action'
        '&bdp_launch_query={invalid_json}'
        '&trigger_context={also_invalid}'
    )

    # 使用测试客户端发送请求
    with app.test_client() as client:
        response = client.get(f'/api/argus{query_string}')

        # 验证响应
        assert response.status_code == 400
        assert 'bdp_launch_query 参数解析失败' in response.data.decode('utf-8')
