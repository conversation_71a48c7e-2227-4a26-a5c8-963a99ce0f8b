import pytest

from conftest import AVAILABLE_MODELS
from lib.agents import complicant
from lib.agents.comparison import (
    invoke_by_llm as comparison_invoke,
    stream_invoke_by_llm as stream_comparison_invoke,
)
from lib.llm.prompt import get_data_content
from lib.output_parsers.complicant import Complicant

DATA = get_data_content('mona_data')
DATA2 = get_data_content('mona_data2')


def load_dataset(name: str) -> str:
    return get_data_content(name)


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_resolve_strategy_stream(load_llm, model_name):
    llm = load_llm(model_name)
    stream_comparison_invoke(llm, '日环比，最大值列的变化差值不大于10%')


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_resolve_strategy(load_llm, model_name):
    llm = load_llm(model_name)
    comparison_invoke(llm, '日环比，最大值列的变化差值不大于10%')


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_complicant_by_reasoning(load_llm, dataset, model_name):
    lm = load_llm(model_name)

    for ds, expect in [
        ('mona_diff_many_valid', True),
        ('mona_diff_one_invalid', False),
    ]:
        print(f'========= Dataset: {ds} ===========')
        response = complicant.invoke_by_reasoning(dataset(ds), lm, stream=True)
        print(f'Response<{type(response)}: {response}')

        assert response is not None, 'bad response'
        assert response.complicant == expect, 'expection not satisfied'


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_complicant_by_tool(load_llm, dataset, model_name):
    lm = load_llm(model_name)

    for ds, expect in [
        ('mona_diff_many_valid', True),
        ('mona_diff_one_invalid', False),
    ]:
        print(f'========= Dataset: {ds} ===========')
        response = complicant.invoke_by_tool(dataset(ds), llm=lm)
        print(f'Response<{type(response)}: {response}')

        assert response is not None, 'bad response'


def test_output_parse():
    data = get_data_content('complicant_out')
    output = Complicant.parse(data)
    print('Output:', output)
