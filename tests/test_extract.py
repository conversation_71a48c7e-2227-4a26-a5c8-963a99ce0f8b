import pytest

from conftest import A<PERSON>ILABLE_MODELS
from lib.agents.copilot_email import stream_invoke_by_model
from lib.constants import DATA_ROOT


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_extract_copilot_email(load_llm, model_name):
    model = load_llm(model_name)
    email = (DATA_ROOT / 'copilot.txt').read_text()
    # response = invoke_by_model(model, email)
    response = stream_invoke_by_model(model, email)

    # assert isinstance(response, CopilotEmail)
    print(f'response({type(response)}): {response}')
