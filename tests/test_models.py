import pytest

from conftest import AVAILABLE_MODELS
from lib.llm.invoke import stream_invoke


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_list_models(load_llm, model_name):
    model = load_llm(model_name, max_tokens=10)
    assert model, f'Model {model_name} not set.'

    stream_invoke(model, '非常简短地回答:你是谁?', verbose=True)


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_stream(load_llm, model_name):
    model = load_llm(model_name)
    assert model, f'Model {model_name} not set.'

    for chunk in model.stream('你的知识截止到什么时候?'):
        print(chunk.content, end='', flush=True)

    real_modelname = chunk.response_metadata.get('model_name')
    if not real_modelname:
        real_modelname = chunk.response_metadata.get('model')
    if real_modelname:
        print(f'\n({real_modelname})')


@pytest.mark.parametrize('model_name', AVAILABLE_MODELS)
def test_intention(load_llm, model_name):
    model = load_llm(model_name)
    assert model, f'Model {model_name} not set.'

    response = model.invoke(
        [
            (
                'system',
                (
                    '你是一个运维专家，请帮我从用户的表述中判断:是否可能发生了系统或者故障,以便于我深度检查并及时发现问题'
                    '只要回答"是"或"否",不需要解释!'
                ),
            ),
            # ('human', 'google cdn好慢啊'),
            # ('ai', '否'),
            ('human', '小强说首页挂了,但是我访问正常'),
            # ('ai', '是'),
            # ('human', 'dgame首页挺好看的'),
        ]
    )

    real_modelname = response.response_metadata.get('model_name')
    if not real_modelname:
        real_modelname = response.response_metadata.get('model')

    print(f'{real_modelname} > {response.content}')
