from lib.clients.lark import LarkClient
from lib.configs import config
from lib.utils import timerange
from lib.utils.pipe import PipeDict


def test_get_table_info():
    app_config = PipeDict(config.lark.apps) | 'aiops'
    lark_client = LarkClient.from_config(app_config)
    app_token = config.lark.bitables['hgame']
    bitable_app = lark_client.bitable_app(app_token)

    print(bitable_app)

    checklist_table_id = bitable_app.get_table(config.lark.bitable_name).table_id
    print('check list table id:', checklist_table_id)

    new_table_id, view_id = bitable_app.copy_table(
        checklist_table_id,
        timerange.now_string('Y-MM-DD-HHmm'),
        True,
        with_conds=[
            {'field_name': '数据源', 'operator': 'isNotEmpty', 'value': []},
            {'field_name': '任务', 'operator': 'isNotEmpty', 'value': []},
        ],
    )


def test_agent_clean_bitables():
    app_token = config.lark.bitables['hgame']

    from tasks.bitable_clean import clean_outdated_tables

    clean_outdated_tables(app_token, '一个月前', False)


def test_send_feishu_message():
    app_config = config.lark.apps['aiops']
    lark_client = LarkClient.from_config(app_config)

    receivers = ['<EMAIL>']
    user_open_ids = lark_client.get_user_ids(receivers)

    lark_client.batch_send_message(
        user_open_ids,
        title='测试标题',
        header_color='green',
        tags=['测试'],
        content='测试内容',
    )


def test_copilot_bitable():
    app_config = PipeDict(config.lark.apps) | 'aiops'
    lark_client = LarkClient.from_config(app_config)

    app_token = config.github.bitable_app
    bitable_app = lark_client.bitable_app(app_token)

    records = bitable_app.records(
        table_id=config.github.bitable_table,
        view_id=config.github.view_id,
    )
    for record in records:
        print(record)
