from lib.screenshot.paths import modify_path, url_qs


def test_qs():
    url = 'http://example.com?foo=bar&baz=qux'
    qs = url_qs(url)

    assert qs['foo'] == ['bar']
    assert qs['baz'] == ['qux']


def test_update_qs():
    url = 'http://example.com?foo=bar&baz=qux&&baz=xx'

    s = modify_path(url, {'foo': 'yjj', 'bar': 1})
    print(s)

    qs = url_qs(s)
    print(qs)

    context = {}
    for k, v in qs.items():
        if len(v) == 1:
            context[k] = v[0]
        else:
            context[k] = v
    print(context)
