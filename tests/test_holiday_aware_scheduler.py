from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import holidays

from scheduler.holiday_aware_scheduler import HolidayAwareScheduler


class TestHolidayAwareScheduler:
    """测试 HolidayAwareScheduler 类的功能。"""

    def setup_method(self) -> None:
        """设置测试环境。"""
        self.scheduler = HolidayAwareScheduler(hour=9, minute=0)  # 每天上午9点执行
        self.last_run_at = datetime(2024, 1, 1, 9, 0)  # 周一

    @patch('scheduler.holiday_aware_scheduler.datetime')
    def test_is_due_on_workday_non_holiday(self, mock_datetime: Mock) -> None:
        """测试工作日且非节假日时任务应该执行。"""
        # 设置当前时间为工作日（周二）且非节假日
        mock_now = datetime(2024, 1, 2, 9, 0)  # 周二
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 True
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (True, timedelta(seconds=3600))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is True
            assert next_delta == timedelta(seconds=3600)

    @patch('scheduler.holiday_aware_scheduler.datetime')
    def test_is_due_on_weekend(self, mock_datetime: Mock) -> None:
        """测试周末时任务不应该执行。"""
        # 设置当前时间为周末（周六）
        mock_now = datetime(2024, 1, 6, 9, 0)  # 周六
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 True
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (True, timedelta(seconds=3600))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is True  # 返回父类的结果
            assert next_delta == timedelta(seconds=3600)

    @patch('scheduler.holiday_aware_scheduler.datetime')
    def test_is_due_on_holiday(self, mock_datetime: Mock) -> None:
        """测试节假日时任务不应该执行。"""
        # 设置当前时间为工作日但是节假日（元旦）
        mock_now = datetime(2024, 1, 1, 9, 0)  # 元旦，周一
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 True
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (True, timedelta(seconds=3600))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is True  # 返回父类的结果
            assert next_delta == timedelta(seconds=3600)

    @patch('scheduler.holiday_aware_scheduler.datetime')
    def test_is_due_when_super_is_not_due(self, mock_datetime: Mock) -> None:
        """测试当父类返回 False 时，直接返回 False。"""
        mock_now = datetime(2024, 1, 2, 9, 0)  # 周二
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 False
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (False, timedelta(seconds=1800))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is False
            assert next_delta == timedelta(seconds=1800)

    def test_scheduler_initialization_with_default_country(self) -> None:
        """测试默认使用中国节假日初始化调度器。"""
        scheduler = HolidayAwareScheduler(hour=10, minute=30)

        assert scheduler.country == 'CN'
        assert isinstance(scheduler.holidays, holidays.HolidayBase)

    def test_scheduler_initialization_with_custom_country(self) -> None:
        """测试使用自定义国家初始化调度器。"""
        scheduler = HolidayAwareScheduler(hour=10, minute=30, country='US')

        assert scheduler.country == 'US'
        assert isinstance(scheduler.holidays, holidays.HolidayBase)

    @patch('exts.holiday_aware_scheduler.datetime')
    def test_is_due_on_friday_workday(self, mock_datetime: Mock) -> None:
        """测试周五工作日时任务应该执行。"""
        # 设置当前时间为周五且非节假日
        mock_now = datetime(2024, 1, 5, 9, 0)  # 周五
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 True
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (True, timedelta(seconds=3600))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is True
            assert next_delta == timedelta(seconds=3600)

    @patch('scheduler.holiday_aware_scheduler.datetime')
    def test_is_due_on_sunday(self, mock_datetime: Mock) -> None:
        """测试周日时任务不应该执行。"""
        # 设置当前时间为周日
        mock_now = datetime(2024, 1, 7, 9, 0)  # 周日
        mock_datetime.now.return_value = mock_now

        # Mock 父类的 is_due 方法返回 True
        with patch.object(self.scheduler.__class__.__bases__[0], 'is_due') as mock_super_is_due:
            mock_super_is_due.return_value = (True, timedelta(seconds=3600))

            is_due, next_delta = self.scheduler.is_due(self.last_run_at)

            assert is_due is True  # 返回父类的结果
            assert next_delta == timedelta(seconds=3600)
