from lib.clients.argus import ArgusClient
from lib.clients.github import Github<PERSON>lient
from lib.clients.lark.client import LarkClient
from lib.configs import config


def test_argus_client(capfd):
    """测试 ArgusClient 的基本功能

    Args:
        capfd: pytest 的内置夹具，用于捕获标准输出
    """
    client = ArgusClient.from_config(config.rootcause)
    response = client.get('/rootcause', params={'messageID': 'om_x100bb0648e49e4a00f2cc2bb2ac4f8d'})

    # 验证响应
    assert response is not None
    assert isinstance(response, str)  # 修改为验证字节类型

    # 将字节类型解码为字符串并打印
    print('\n响应内容：')
    print(response)

    # 确保输出被正确捕获
    captured = capfd.readouterr()
    assert '响应内容' in captured.out


def test_github_client():
    client = GithubClient.from_config(config.github)
    gh_user = client.search_user('akayj')
    print(gh_user)
    print(
        f'login: {gh_user.login}, display: {gh_user.display_login}, id: {gh_user.id}, email: {gh_user.email}'
    )


def test_lark_client():
    client = LarkClient.from_config(config.lark.apps['aiops'])

    user_id = client.get_user_id('<EMAIL>')
    assert user_id is not None, '用户ID不能为空'

    client.batch_send_message(
        [user_id],
        title='Github Copilot 邀请测试',
        content=(
            f'<at id={user_id}></at>, 你好!\n在使用 Copilot 之前, 请以 **akayj** 账号登录Github'
            '\n<font color="red">**注意: 1个月不活跃将会被自动取消邀请。**</font>'
        ),
    )
