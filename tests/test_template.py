import pytest

from lib.utils.template import render_template


@pytest.mark.template
def test_replace_jinja():
    t = 'foo-{bar}-{baz}'
    assert render_template(t, {'bar': 'bar', 'baz': 'baz'}) == 'foo-bar-baz'


@pytest.mark.template
def test_empty_placeholder():
    t = 'foo-{bar}-{notExist}'
    assert render_template(t, {'bar': 'bar'}) == 'foo-bar-'


@pytest.mark.template
def test_dash_name():
    t = '{panel_name}-{var-instanceName}'
    assert (
        render_template(t, {'panel_name': 'foo', 'var-instanceName': 'name'})
        == 'foo-name'
    )


@pytest.mark.template
def test_auto_prefix_template():
    t = '{panel_name}-{instanceName}'
    assert (
        render_template(t, {'panel_name': 'foo', 'var-instanceName': 'name'})
        == 'foo-name'
    )
