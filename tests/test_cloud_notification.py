from lib.agents.cloud_notification_pre_cate import invoke


def test_cloud_notification():
    content = """\
【阿里云】尊敬的******************：您的云数据库RDS的1个实例因风险隐患（内存故障风险）原因触发并完成主备故障切换，当前已经恢复正常。请检查程序连接是否正常，如无影响请忽略，同时建议您对应用程序设置自动重连机制以避免此类切换带来影响。实例：华东2（上海） rr-uf65p7wbz7932e87b(tsh-hgame-channel-read-0001)，切换时间：2025-05-05T02:20:06+08:00。
"""
    # result = invoke(content, stream=True, model_name='gpt-4-1')
    result = invoke(content, stream=True, model_name='deepseek-v3')
    # result = invoke(content, stream=True, model_name='claude-3-5')
    print(result)
