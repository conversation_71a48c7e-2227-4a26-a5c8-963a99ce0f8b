import pytest

from lib.utils.size_utils import parse_size


def test_parse_size_empty_string():
    with pytest.raises(ValueError) as exc_info:
        parse_size('')
    assert str(exc_info.value) == 'Size string cannot be empty'


def test_parse_size_invalid_format():
    with pytest.raises(ValueError) as exc_info:
        parse_size('10X')
    assert 'Invalid size format' in str(exc_info.value)


def test_parse_size_no_unit():
    assert parse_size('1024') == 1024


def test_parse_size_kb():
    assert parse_size('10K') == 10 * 1024
    assert parse_size('10k') == 10 * 1024
    assert parse_size('10 K') == 10 * 1024
    assert parse_size('10 k') == 10 * 1024


def test_parse_size_mb():
    assert parse_size('2M') == 2 * 1024 * 1024
    assert parse_size('2m') == 2 * 1024 * 1024
    assert parse_size('2 M') == 2 * 1024 * 1024
    assert parse_size('2 m') == 2 * 1024 * 1024


def test_parse_size_gb():
    assert parse_size('1G') == 1 * 1024 * 1024 * 1024
    assert parse_size('1g') == 1 * 1024 * 1024 * 1024
    assert parse_size('1 G') == 1 * 1024 * 1024 * 1024
    assert parse_size('1 g') == 1 * 1024 * 1024 * 1024


def test_parse_size_tb():
    assert parse_size('0.5T') == int(0.5 * 1024 * 1024 * 1024 * 1024)
    assert parse_size('0.5t') == int(0.5 * 1024 * 1024 * 1024 * 1024)
    assert parse_size('0.5 T') == int(0.5 * 1024 * 1024 * 1024 * 1024)
    assert parse_size('0.5 t') == int(0.5 * 1024 * 1024 * 1024 * 1024)
