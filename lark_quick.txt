URL:
/api/argus?bdp_launch_query=%7B%22__trigger_id__%22%3A%222b907be936023058e3deffb56ba3e97d%22%7D&from=message_action&trigger_context=%7B%22chatId%22%3A%227336772621230637058%22%2C%22appId%22%3A%22cli_a7ce9bf2db33d013%22%2C%22chatName%22%3A%22%E8%BF%90%E7%BB%B4%E6%9E%B6%E6%9E%84%22%2C%22chatType%22%3A2%2C%22avatarKey%22%3A%22default-avatar_100f43a5-7dfc-4cba-8eac-93ca9496a96d%22%2C%22messageIds%22%3A%5B%227520081057979596801%22%5D%2C%22actionTime%22%3A1751531807219%2C%22from%22%3A%22message_action%22%7D

trigger_context:
{
  "__trigger_id__": "2b907be936023058e3deffb56ba3e97d",
  "from": "message_action",
  "trigger_context": {
	"chatId": "7336772621230637058",
	"appId": "cli_a7ce9bf2db33d013",
	"chatName": "运维架构",
	"chatType": 2,
	"avatarKey": "default-avatar_100f43a5-7dfc-4cba-8eac-93ca9496a96d",
	"messageIds": [
	  "7520081057979596801"
	],
	"actionTime": 1751531807219,
	"from": "message_action"
  }
}