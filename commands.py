import sys

import click
from celery import Celery
from celery.result import Async<PERSON><PERSON>ult
from flask import Flask, current_app

from lib.configs import config


@click.command('config')
@click.option('--ignore-unset', is_flag=True, help='ignore unset field')
def print_config(ignore_unset: bool):
    """Print the current configuration."""
    click.echo(
        f'{config.model_dump_json(indent=2, exclude_none=ignore_unset)}',
    )


def abort_if_dryrun(ctx, param, value):
    """Abort if the value is not true."""
    if value:
        ctx.abort()


@click.command('check', help='start check')
@click.argument('category')
def start_check(category: str):
    bitable_token = config.lark.bitables[category]

    celery_app: Celery = current_app.extensions['celery']  # type: ignore
    task: 'AsyncResult' = celery_app.send_task(
        f'tasks.{category}.run_check',
        args=[bitable_token],
    )
    return {'task_id': task.task_id}


@click.command('clean', help='clean bitable')
@click.argument('category')
@click.option('--deadline', default='2周前', help='The deadline for cleaning up data')
def clean_bitable(category: str, deadline: str):
    bitable_token = config.lark.bitables[category]

    celery_app: Celery = current_app.extensions['celery']  # type: ignore
    task: 'AsyncResult' = celery_app.send_task(
        'tasks.bitable_clean.clean_outdated_tables',
        args=[bitable_token, deadline],
    )
    return {'task_id': task.task_id}


@click.command('create-weekly-wiki', help='create weekly wiki')
@click.option('--meeting-day', default='本周五', help='The day of the weekly meeting')
@click.option('--background', is_flag=True, help='Run in the background')
def create_weekly_wiki(meeting_day: str, background: bool):
    if background:
        celery_app: Celery = current_app.extensions['celery']  # type: ignore
        task: 'AsyncResult' = celery_app.send_task(
            'tasks.weekly_meeting.create_weekly_meeting_wiki',
            kwargs={'weekly_meeting_day': meeting_day, 'notify': True},
        )
        return {'task_id': task.task_id}

    # run in foreground
    from tasks.weekly_meeting import create_weekly_meeting_wiki

    create_weekly_meeting_wiki()


@click.command('start_argus_robot', help='start argus robot')
def start_argus_robot():
    from lib.lark_robots.argus_robot import main

    main()


@click.command('sync-feishu-message', help='sync feishu message')
def sync_feishu_message():
    celery_app: Celery = current_app.extensions['celery']  # type: ignore
    task: 'AsyncResult' = celery_app.send_task(
        'tasks.cloud_notifications_summary.sync_last_hour_messages',
    )
    return {'task_id': task.task_id}


@click.command('install-browser', help='install browser for playwright')
def install_playwright_browser():
    """Install the browser for playwright."""
    from playwright.__main__ import main

    sys.argv = ['playwright', 'install', '--only-shell', 'chromium']
    main()


def registry_commands(app: Flask):
    app.cli.add_command(print_config)
    app.cli.add_command(start_check)
    app.cli.add_command(clean_bitable)
    app.cli.add_command(sync_feishu_message)
    app.cli.add_command(install_playwright_browser)
    app.cli.add_command(create_weekly_wiki)

    # robots
    # app.cli.add_command(start_ops_robot)
    app.cli.add_command(start_argus_robot)
