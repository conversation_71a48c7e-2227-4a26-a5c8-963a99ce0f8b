from pathlib import Path

from playwright.sync_api import expect, sync_playwright

from log import logger


def take_screenshot(url: str, saved_to: Path):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        context = browser.new_context(
            storage_state=None,
            ignore_https_errors=True,
        )
        page = context.new_page()
        page.context.clear_cookies()

        try:
            page.goto(url, wait_until='domcontentloaded')
            page.wait_for_load_state('domcontentloaded')
            page.screenshot(path=saved_to, full_page=True)
        finally:
            browser.close()


def baidu_weather(q: str, saved_to: Path):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        context = browser.new_context(
            storage_state=None,
            ignore_https_errors=True,
        )
        page = context.new_page()
        page.context.clear_cookies()

        try:
            qurl = f'https://www.baidu.com/s?wd={q}'
            logger.debug('fetching weather from {}', qurl)
            page.goto(qurl, wait_until='domcontentloaded')

            # 隐藏header
            page.evaluate('document.querySelector("div#head").style.display = "none"')

            selector = 'div#content_left > div.result-op:first-child'
            element = page.locator(selector)

            expect(element).to_be_visible()

            element.screenshot(path=saved_to)
        except Exception as e:
            print(e)
        finally:
            browser.close()
