import asyncio

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent

from lib.llm.mcp import load_mcp_tools_schema
from lib.llm.models import get_llm
from lib.output_parsers.weekly_meeting import WeeklyMeetingOutputParser
from log import logger
from prompt.weely_wiki import WEEKLY_WIKI_PROMPT_TEMPLATE

from .tools_invoke import graph_invoke

mcp_tool_names = ['feishu_wiki']
# model = get_llm('deepseek-v3')
# model = get_llm('gemini-2.5-flash')
model = get_llm('claude-3-5')


async def ainvoke_by_model(meeting_day: str):
    logger.debug('开始准备周会wiki...')

    system_prompt = WEEKLY_WIKI_PROMPT_TEMPLATE.format(
        meeting_day=meeting_day,
        # current_time=pendulum.now('Asia/Shanghai').to_rfc3339_string(),
        format_instructions=WeeklyMeetingOutputParser.get_format_instructions(),
    )
    logger.debug('系统提示:\n{}', system_prompt)

    # user prompt
    question = '请帮我准备周会wiki!'

    mcp_client = MultiServerMCPClient(load_mcp_tools_schema(names=mcp_tool_names))
    tools = await mcp_client.get_tools()
    graph = create_react_agent(
        model,
        tools,
        prompt=system_prompt,
    )

    result = await graph_invoke(graph, [('user', question)])
    logger.info('周会wiki创建结果:\n{}', result)
    try:
        parsed_result: WeeklyMeetingOutputParser = WeeklyMeetingOutputParser.parse(result)
        return True, parsed_result
    except Exception as e:
        logger.error(f'Failed to parse weekly meeting wiki: {e}')
        return False, str(result)


def invoke(weekly_meeting_day: str = '本周五'):
    return asyncio.run(ainvoke_by_model(weekly_meeting_day))
