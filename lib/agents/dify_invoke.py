import orjson as json

from lib.clients.dify import ChatClient
from lib.configs import config
from log import logger


def invoke(question: str, user: str, session_id: str):
    client = ChatClient(config.dify.api_key, config.dify.base_url)
    with client.create_chat_message(
        query=question,
        user=user,
        response_mode='streaming',
        conversation_id=session_id,
    ) as r:
        messages = []
        metadata = {}
        conversation_id = ''

        for line in r.iter_lines():
            msg_body = line.strip('data:').strip()
            if not msg_body:
                continue

            try:
                msg = json.loads(msg_body)

                if msg['event'] == 'message_end':
                    metadata = msg['metadata']
                    conversation_id = msg['conversation_id']
                    break

                elif msg['event'] == 'agent_message':
                    messages.append(msg)
            except json.JSONDecodeError as e:
                logger.error(f'parse msg error: {msg_body}')
                logger.exception(e)

        answer = ''.join([m['answer'] for m in messages if m['answer']])

        final_response = {
            'answer': answer,
            'metadata': metadata,
            'conversation_id': conversation_id,
        }
        return True, final_response


def iter_invoke(question: str, user: str, session_id: str):
    client = ChatClient(config.dify.api_key, config.dify.base_url)
    with client.create_chat_message(
        query=question,
        user=user,
        response_mode='streaming',
        conversation_id=session_id,
    ) as r:
        if not r.is_success:
            return

        for line in r.iter_lines():
            msg_body = line.strip('data:').strip()
            if not msg_body:
                continue

            try:
                msg = json.loads(msg_body)
                yield msg
            except json.JSONDecodeError as e:
                print('JSONDecodeError:', e)
                return
