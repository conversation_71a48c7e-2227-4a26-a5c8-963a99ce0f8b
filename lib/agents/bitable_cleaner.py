import asyncio

import pendulum
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from loguru import logger

from lib.llm.mcp import load_mcp_tools_schema
from lib.llm.models import get_llm

from .tools_invoke import graph_invoke

model_name = 'deepseek-v3'
# model_name = 'claude-3-5'

# ruff: noqa: E501
prompt_template = """You are a feishu/lark bitable assistant, best of the world assistant ever seen.\
You know how to list tables in a bitable app, and can figure out create time of the table \
by its name, and delete tables.

## Guidelines
- Current time is {current_time}.
- bitable_app token is {bitable_app}.
- You are given tools to assist you to accomplish tasks.
- Table name like 'YYYY-MM-DD-HHmm' indicates the table is created at that time.
- **DO NOT** say sorry or apologize, just do your best to assist the user, and give a nice and clean task report at last.
- **DO NOT** ask for the next action, or give any suggestions.
- **DO NOT** delete '巡检配置' or '巡检条目' like table, because they are used to store the inspection configuration.
- **DO NOT** delete table created today, because its fresh.
- **MUST** filter tables by the days before deletion.
- **MUST** follow the report format strictly when you do some deletion, it's very important to user.
- **MUST** respond in Chinese.

## Task report format
Content should be in Chinese, and follow the format strictly:
- Bitable: [here is the name of bitable]
- Result: [description of the result, like '没有符合条件的表格' or '没有表格被删除' or '删除了 XX 个表格: table1, table2,..., tableN']

## User Task
Task is indicated by a user_task tag:
<user_task>
{task}
</user_task>
"""


async def ainvoke(bitable_app: str, task: str):
    model = get_llm(model_name)
    tools_schema = load_mcp_tools_schema()

    prompt = prompt_template.format(
        current_time=pendulum.now('Asia/Shanghai').to_rfc3339_string(),
        bitable_app=bitable_app,
        task=task,
    )
    logger.debug('prompt: {}', prompt)

    mcp_client = MultiServerMCPClient(tools_schema)
    tools = await mcp_client.get_tools()
    graph = create_react_agent(model, tools, prompt=prompt)

    logger.debug('开始执行清理任务...')
    response = await graph_invoke(graph, [('user', task)])

    # message = response['messages'][-1]
    # content = message.content

    # return content
    return response


def invoke(bitable_app: str, task: str):
    """Invoke the agent with the given task."""
    return asyncio.run(ainvoke(bitable_app, task))
