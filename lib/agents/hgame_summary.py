import yaml

from lib.llm.models import get_llm
from lib.llm.prompt import (
    get_data_content,
)
from lib.output_parsers.hgame_summary import HgameSummaryResult
from prompt import hgame_summary

llm = get_llm('gpt-4-1')


def invoke_struct(task: str, data: str):
    response = llm.invoke(
        [
            ('system', hgame_summary.PROMPT.format(task=task)),
            ('human', f'请帮忙这里data标签中的数据:\n<data>{data}</data>'),
        ],
    )
    print('response:', response.content)
    result = HgameSummaryResult.parse(response.content)
    return result


if __name__ == '__main__':
    # content = get_data_content('hgame_errors')
    content = get_data_content('hgame_tcs')
    # content = get_data_content('hgame_dau')

    yaml_data = yaml.safe_load(content)

    task = yaml_data['task']
    data = yaml_data['data']

    message = invoke_struct(task, data)
    print(message)
    print(f'{type(message)}')
