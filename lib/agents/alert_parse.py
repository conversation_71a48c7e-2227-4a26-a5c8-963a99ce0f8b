from langchain_core.runnables import RunnablePassthrough

from lib.llm.models import get_llm
from lib.llm.prompt import get_prompt_from_messages
from lib.output_parsers.alert import Alert

parser = Alert.get_parser()


def invoke(content: str) -> Alert:
    """解析告警字段"""
    chat = get_llm('gpt4o')

    prompt = get_prompt_from_messages(
        'alert',
        ('user', ' 告警内容如下:\n```{alert_content}```'),
    ).partial(
        format_instructions=parser.get_format_instructions(),
    )

    chain = (
        {'alert_content': RunnablePassthrough()}
        | prompt
        | chat.with_structured_output(Alert)
    )

    return chain.invoke(content)
