from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, SystemMessage

from lib.llm.models import get_llm
from lib.output_parsers.cloud_notification import CloudNotification
from prompt.cloud_notification import CLOUD_NOTIFICATION_CATE_PROMPT_TEMPLATE


def invoke(
    content: str, *, stream: bool = True, model_name: str = 'claude-3-5'
) -> CloudNotification:
    llm = get_llm(model_name)

    if stream:
        return stream_invoke_by_model(llm, content)
    else:
        return invoke_by_model(llm, content)


def invoke_by_model(llm: 'BaseLanguageModel', message: str) -> CloudNotification:
    """Parse message content to `CloudNotification` object."""

    chain = llm | CloudNotification.get_parser()

    prompt = CLOUD_NOTIFICATION_CATE_PROMPT_TEMPLATE.format(
        format_instructions=CloudNotification.get_format_instructions()
    )

    output = chain.invoke([SystemMessage(prompt), HumanMessage(message)])
    return output


def stream_invoke_by_model(llm: 'BaseLanguageModel', message: str) -> CloudNotification:
    prompt = CLOUD_NOTIFICATION_CATE_PROMPT_TEMPLATE.format(
        format_instructions=CloudNotification.get_format_instructions()
    )

    output = []
    for chunk in llm.stream([SystemMessage(prompt), HumanMessage(message)]):
        if hasattr(chunk, 'content'):
            chunk_content = chunk.content
        else:
            # 如果chunk已经是字符串
            chunk_content = str(chunk)

        # 累积输出内容
        output.append(chunk_content)

    # 流式输出完成后，解析完整的输出字符串
    return CloudNotification.parse(''.join(output))
