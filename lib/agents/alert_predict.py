from typing import Optional

import pendulum

from lib.clients.oma import OmaClient
from lib.configs import config
from lib.configs.grafana import GrafanaConfig, GrafanaViewConfig
from lib.constants import STORE_ROOT
from lib.output_parsers.alert import Alert
from lib.utils import image
from log import logger

from .alert_parse import invoke as alert_parse
from .chat_image import invoke as chat_image_invoke

parser = Alert.get_parser()

oma_client = OmaClient.from_config(config.oma)


def get_view_config(cfg: GrafanaConfig, name: str):
    for view in cfg.views:
        if view.name == name:
            return view


def generate_url(alert: Alert, render: bool = False):
    grafan_config = config.grafanas['k8s']

    # 1. get view config
    view_config: Optional[GrafanaViewConfig] = None
    alert_type = alert.alert_type

    base_url = grafan_config.base_url.strip('/')
    if render:
        base_url += '/render/d-solo'

    # 1.1 pod cpu
    if alert_type == 'cpu':
        view_config = get_view_config(grafan_config, 'pod_cpu')
        if view_config:
            url = (
                f'{base_url}/{grafan_config.dashboard_id}/{grafan_config.name}?'
                'orgId=1'
                f'&var-datasource={grafan_config.datasource}'
                f'&var-cluster_name={alert.cluster_name}'
                f'&var-NameSpace={alert.namespace}'
                f'&var-Pod={alert.pod}'
                f'&from=now-24h&to=now'
                # f'&viewPanel={view_config.view_id}'
                f'&panelId={view_config.view_id}'
                f'&width=1000&height=500'
            )
            return url

    # 1.2 pod mem
    elif alert_type == 'memory':
        view_config = get_view_config(grafan_config, 'pod_mem')
        # 2. generate url
        if view_config:
            url = (
                f'{base_url}/{grafan_config.dashboard_id}/{grafan_config.name}?'
                'orgId=1'
                f'&var-datasource={grafan_config.datasource}'
                f'&var-cluster_name={alert.cluster_name}'
                f'&var-NameSpace={alert.namespace}'
                f'&var-Pod={alert.pod}'
                f'&from=now-24h&to=now'
                # f'&viewPanel={view_config.view_id}'
                f'&panelId={view_config.view_id}'
                f'&width=1000&height=500'
            )
            return url


def invoke(content: str):
    # 1. 解析告警字段
    alert = alert_parse(content)

    alert_type = alert.alert_type
    if alert_type not in ['cpu', 'memory']:
        return f'告警类型({alert.alert_type})不支持预测', None

    # 2. 准备截图
    # 2.1 生成截图地址
    # grafana_url = generate_url(alert)
    # if grafana_url is None:
    #     return '告警类型不支持预测', None

    # # 2.2 截图
    # with GrafanaTableScreenShot(remote_driver()) as shot:
    #     logger.debug(f'start take screenshot for {grafana_url}')
    #     saved_to, meta = shot.take(grafana_url, STORE_ROOT, timeout=30)
    #     logger.debug('saved_to: %s, metainfo: %s', saved_to, meta)

    grafana_url = generate_url(alert, render=True)
    if grafana_url is None:
        return '告警类型不支持预测', None

    # 2.2 截图
    logger.debug(f'start downloading image from {grafana_url}')
    saved_to = image.download(grafana_url, STORE_ROOT, timeout=30)

    logger.debug(f'save image to {saved_to}')

    # 3. AI预测
    predict_what = f'{alert.alert_type}的未来多久会到峰值100%'
    system_prompt = (
        f'你是一个告警预测的助手, 请帮我预测{predict_what},'
        f'下面是集群{alert.cluster_name}在过去一段时间的监控图片，请推测未来的峰值。'
    )

    logger.debug('starting predict with prompt: `%s`', system_prompt)

    response = chat_image_invoke(system_prompt, saved_to)

    logger.debug('predict message: %s', response)

    # chat = get_chat_llm(temperature=0)

    # prompt = get_prompt_from_messages(
    #     'alert_predict',
    # )

    # chain = (
    #     {'predict_what': RunnablePassthrough()}
    #     | prompt
    #     | chat.with_structured_output(Alert)
    # )

    # logger.debug('starting predict `%s`', content)
    # response: Alert = chain.invoke(content)
    # logger.debug('alert message parsed: %s', response)

    # 4. 回复用户
    final_content = response.content
    logger.debug('final content: %s', final_content)

    # 加水印
    now_t = pendulum.now().format('YYYYMMDD')
    watermark_text = f'Argus {now_t}'
    watermarked = image.watermark(saved_to, watermark_text, position='top-right')

    return final_content, {'image': watermarked, 'meta': {}, 'grafana_url': grafana_url}
