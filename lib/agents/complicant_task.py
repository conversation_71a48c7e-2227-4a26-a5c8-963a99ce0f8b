from typing import Literal, Optional, Tuple

import pendulum
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers.openai_tools import PydanticToolsParser
from pydantic import BaseModel, Field

from lib.llm.models import get_llm
from lib.output_parsers.period import TimeRange


def get_now():
    """获取当前时间"""
    return pendulum.now('Asia/Shanghai')


TimeRangeType = Literal[
    'yestoday',
    'latest-24-hours',
    'latest-24-hours-endwith-specific-time',
    'lastweek',
    'lastmonth',
]


class TaskInfo(BaseModel):
    """获取任务信息."""

    time_format: str = 'YYYY-MM-DD HH:mm:ss'

    task_action_time: str = Field(..., description='任务执行时间, 格式为 HH:mm')
    period_type: TimeRangeType = Field(..., description='时间区间的类型')
    start_hour: Optional[str] = Field(
        None,
        description='从一天的什么时间开始, 格式为 HH:mm, 用途: yestoday, 其他情况忽略',
    )
    end_hour: Optional[str] = Field(
        None,
        description=(
            '到一天的什么时间结束, 格式为 HH:mm, 用途: yestoday, 配合 start_hour, 其他情况忽略'
        ),
    )

    endtime: Optional[str] = Field(
        None,
        description=(
            '结束的时间, 格式为 HH:mm, 用途: latest-24-hours-endwith-specific-time, 其他情况忽略'
        ),
    )

    task_type: str = Field(..., description='任务类型, 例如: 对比, 提取')
    task_desc: str = Field(..., description='任务描述')

    def __call__(self) -> Tuple[str, str]:
        """调用获取比较周期的方法"""
        now = get_now()
        return self.get_timerange(now, self.time_format)

    def get_timerange(self, now: pendulum.DateTime, formats: str):
        """
        根据 self.range_time 获取比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        """
        if self.period_type == 'yestoday':
            return self.get_yestoday(now, formats)
        elif self.period_type == 'lastweek':
            return self.get_lastweek(now, formats)
        elif self.period_type == 'lastmonth':
            return self.get_lastmonth(now, formats)
        elif self.period_type == 'latest-24-hours':
            return self.get_latest_24_hours(now, formats)
        elif self.period_type == 'latest-24-hours-endwith-specific-time':
            return self.get_latest_24_hours_endwith_specify_time(now, formats)
        else:
            raise ValueError(f'Unsupported range_time: {self.period_type}')

    def get_yestoday(
        self,
        now: pendulum.DateTime,
        formats: str,
    ) -> Tuple[str, str]:
        """
        获取最近一天(昨天)的时间范围。

        参数:
        - now: 当前时间对象
        - formats: 时间格式
        """
        yestoday = now.subtract(days=1)

        if self.start_hour and self.end_hour:
            start = pendulum.from_format(
                self.start_hour,
                'HH:mm',
                tz='Asia/Shanghai',
            )
            end = pendulum.from_format(
                self.end_hour,
                'HH:mm',
                tz='Asia/Shanghai',
            )
            return (
                yestoday.set(hour=start.hour)
                .start_of('hour')
                .set(minute=start.minute)
                .format(formats),
                yestoday.set(hour=end.hour).start_of('hour').set(minute=end.minute).format(formats),
            )
        return (
            yestoday.start_of('day').format(formats),
            yestoday.end_of('day').format(formats),
        )

    def get_lastweek(
        self,
        now: pendulum.DateTime,
        formats: str,
    ) -> Tuple[str, str]:
        """
        获取上一周的时间范围。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        last_week = now.subtract(weeks=1)
        return (
            last_week.start_of('week').format(formats),
            last_week.end_of('week').format(formats),
        )

    def get_lastmonth(
        self,
        now: pendulum.DateTime,
        formats: str,
    ) -> Tuple[str, str]:
        """
        获取上月的时间范围。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        last_month = now.subtract(months=1)
        return (
            last_month.start_of('month').format(formats),
            last_month.end_of('month').format(formats),
        )

    def get_latest_24_hours(self, now: pendulum.DateTime, formats: str) -> Tuple[str, str]:
        """
        获取 以当前时间为结束的最近24小时的时间范围。

        参数:
        - now: 当前时间对象
        - formats: 时间格式
        """
        period = now.subtract(hours=24)

        return (
            period.format(formats),
            now.format(formats),
        )

    def get_latest_24_hours_endwith_specify_time(
        self, now: pendulum.DateTime, formats: str
    ) -> Tuple[str, str]:
        """
        获取以今天的某个时间为结束的最近24小时的时间周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式
        """
        that = (
            pendulum.from_format(self.endtime, 'HH:mm', tz='Asia/Shanghai') if self.endtime else now
        )
        period = that.subtract(hours=24)
        return (period.format(formats), that.format(formats))


tools = [TaskInfo]

llm = get_llm('gpt4o-mini')
llm_with_tools = llm.bind_tools(tools=tools)


def invoke(question: str):
    chain = llm_with_tools | PydanticToolsParser(tools=list(tools))
    response = chain.invoke([HumanMessage(content=question)])
    print(response)

    result: list[str] = []
    for r in response:
        result = r()
        # print(f'{type(r)}:\n\t{result}')

    # print('Final results:', result)
    return TimeRange(start_time=result[0], end_time=result[1])


if __name__ == '__main__':
    inputs = [
        # '每天10点发出最近24小时最大值（返回列: 在线人数)',
        # '每天10点发出最近24小时错误码最大的top2（返回列: 错误码名字，最大值)',
        # '每天早上发出前一天的错误码最大的top2（返回列: 错误码名字，最大值)',
        '每天早上10点发出前一天8到9点的错误码最大的top2（返回列: 错误码名字，最大值)',
        # '每天10点发出最近24小时(10点为止)最大值（在线人数）',
        '按max降序，每天10点发出最近24小时(10点为止)错误码最大的两个（错误码名字，最大值）',
        '每天10点发出： 最近24小时(10点为止)max列最大的top5（返回列:函数名字，最大值）',
        # '每天10点发出： 最近24小时(10点为止)最大值（在线人数）',
        '每天10点发出: 最近24小时(10点为止)错误数top2（返回列：错误码名字，最大值）',
    ]
    for input in inputs:
        print(f'Input: {input}')
        response = invoke(input)
        print(response)
