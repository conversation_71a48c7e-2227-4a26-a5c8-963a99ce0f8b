from lib.clients.oma import OmaClient
from lib.configs import config
from lib.constants import DATA_ROOT
from lib.output_parsers.alert import Alert
from log import logger

from .alert_parse import invoke as alert_parse

parser = Alert.get_parser()

oma_client = OmaClient.from_config(config.oma)


def invoke(content: str):
    response = alert_parse(content)

    logger.debug(f'alert struct parsed: {response}')

    # 2. 补充必要信息
    response_content = ''

    # k8s告警 - disk
    if response.alert_type == 'disk':
        nodes = response.nodes

        logger.debug(f'starting complete disk type alert for nodes: {nodes}')

        for node_ip in nodes:
            # machine info
            machine_info = oma_client.get_machine_by(private_ip=str(node_ip))
            if machine_info:
                node_info = oma_client.get_node(machine_info['external_uuid'])
                response_content += f' 节点{node_ip} 所在节点池 {node_info["pool_name"]}\n'
        logger.debug(f'complete disk type alert: {response_content}')

    # 阿里云告警
    elif response.alert_type == 'aliyun_alert':
        logger.debug(f'starting complete aliyun ddos alert, for ip {response.ddos_ip}')

        info = oma_client.search_one(response.ddos_ip)
        if info:
            response_content = ', '.join(
                [
                    f'项目: {info["产品名称"]}',
                    f'名称: {info["名称"]}',
                    f'类型: {info["关联资源类型"] or info["最后关联资源类型"]}',
                ]
            )
        logger.debug(f'aliyun alert info found: {response_content}')

    elif response.alert_type == 'aliyun_eip_alert':
        logger.debug(f'starting complete aliyun eip alert, for ip {response.eip}')

        info = oma_client.search_one(response.eip)
        if info:
            response_content = ', '.join(
                [
                    f'项目: {info["产品名称"]}',
                    f'名称: {info["名称"]}',
                    f'类型: {info["关联资源类型"] or info["最后关联资源类型"]}',
                ]
            )
        logger.debug(f'aliyun eip alert info found: {response_content}')

    return response_content


if __name__ == '__main__':
    from lib.utils.printer import debug

    sources = [
        # 'alert_mem.txt',
        # 'alert_cpu.txt',
        'alert_disk.txt',
        # 'alert_other.txt',
    ]
    sources = map(lambda x: str(DATA_ROOT / x), sources)  # type: ignore

    for src in sources:
        with open(src) as f:
            alert_content = f.read()
            debug(f'待处理的告警信息:\n{alert_content}\n')

            response = invoke(alert_content)
            debug(f'格式化结果:\n{response}', color='green')
        print('-' * 20 + '\n\n')
