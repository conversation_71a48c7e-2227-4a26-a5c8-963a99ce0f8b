import click
from fastmcp import FastMCP
from pydantic import Field

from .client import OMAClient


@click.command()
def main():
    app = FastMCP('mcp-oma')

    oma_client = OMAClient()

    @app.tool()
    def search_rds(
        rds_instance_id: str = Field(description='rds(mysql) instance id, allways like "rm-xxx"'),
    ) -> str:
        """Search for RDS(mysql) instances."""
        response = oma_client.search_rds(rds_instance_id)
        if not response:
            return f'No RDS(mysql) instance found with ID: {rds_instance_id}'
        return response

    @app.tool()
    def search_redis(
        redis_instance_id: str = Field(description='redis instance id, allways like "r-xxx"'),
    ) -> str:
        """Search for Redis instances."""
        response = oma_client.search_redis(redis_instance_id)
        if not response:
            return f'No Redis instance found with ID: {redis_instance_id}'
        return response

    @app.tool()
    def search_k8s_cluster(
        cluster_id: str = Field(
            description='Kubernetes cluster id, contained in "worker-k8s-for-cs-<cluster_id>"'
        ),
    ) -> str:
        """Search for Kubernetes clusters by ID."""
        response = oma_client.search_k8s_cluster(cluster_id)
        if not response:
            return f'No Kubernetes cluster found with ID: {cluster_id}'
        return response

    app.run(transport='stdio')
