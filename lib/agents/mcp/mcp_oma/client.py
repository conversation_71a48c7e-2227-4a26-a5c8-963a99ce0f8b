import os
from typing import Optional

import httpx


class OMAClient:
    """OMA Client for interacting with the OMA API."""

    def __init__(
        self,
        base_url: str = os.getenv(
            'OMA_API_BASE_URL',
            'https://oma-api.lilithgame.com',
        ),
        token: Optional[str] = os.getenv('OMA_TOKEN'),
    ):
        headers = {'Authorization': f'Token {token}'} if token else {}
        headers.update({'User-Agent': 'AIOps/1.0'})
        self.client = httpx.Client(base_url=base_url, headers=headers)

    def fuzz_search(self, query: str, resource_type: Optional[str] = None) -> dict:
        """Perform a fuzz search with the given query."""
        params = {'query': query}
        if resource_type:
            params['resource_type'] = resource_type

        response = self.client.get('/api/cmdb/multiple-search/', params=params)
        response.raise_for_status()
        return response.json()

    def find_resouce(self, resource_id: str, resource_type: str) -> dict:
        """Search for a specific resource by ID and type."""
        response = self.client.get(
            f'/api/cmdb/{resource_type}-document/',
            params={'search': resource_id, 'limit': 1},
        )
        response.raise_for_status()
        return response.json()

    def search_rds(self, rds_instance_id: str) -> dict:
        """Search for RDS(mysql) instances."""
        return self.find_resouce(rds_instance_id, 'mysql')

    def search_redis(self, redis_instance_id: str) -> dict:
        """Search for RDS(redis) instances."""
        return self.find_resouce(redis_instance_id, 'redis')

    def search_k8s_cluster(self, cluster_id: str) -> dict:
        """Search for Kubernetes clusters by ID."""
        return self.find_resouce(cluster_id, 'k8scluster')

    def search_machine(self, instance_id: str) -> dict:
        """Search for machines by ID."""
        return self.find_resouce(instance_id, 'machine')
