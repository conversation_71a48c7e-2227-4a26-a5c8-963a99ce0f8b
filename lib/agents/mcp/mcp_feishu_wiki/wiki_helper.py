from datetime import datetime, timedelta

from lib.clients.lark.client import UserAuthenClientBuilder


class WikiInfoHelper:
    @staticmethod
    def get_wiki_nodes_info(root_node_token: str, user_access_token: str):
        lark_client = UserAuthenClientBuilder.build_user_client(user_token=user_access_token)
        ok, node = lark_client.get_wiki_node(root_node_token)
        if not ok or not node:
            raise Exception(f'获取wiki nodes失败: {node}')

        content = []

        node_info = dict(
            title=node.title,
            space_id=node.space_id,
            parent_node_token=node.parent_node_token,
            node_token=node.node_token,
            has_child=node.has_child,
        )
        content.append(f'root node: {node_info}')

        now = datetime.now()
        this_year = str(now.year)
        # 根据当前时间，计算是 Q1、Q2、Q3 还是 Q4
        this_quarter = (now.month - 1) // 3 + 1

        root_child_node = {}

        # get child nodes
        for child_node in lark_client.get_space_child_nodes(node.space_id, node.node_token):
            child_node_info = dict(
                title=child_node.title,
                space_id=child_node.space_id,
                parent_node_token=child_node.parent_node_token,
                node_token=child_node.node_token,
                has_child=child_node.has_child,
            )
            root_child_node[child_node.title] = child_node_info

        for title, node_info in root_child_node.items():
            if this_year in title:
                content.append(f'{title}: {node_info}')

                # get child node
                if not node_info['has_child']:
                    continue

                for child_node2 in lark_client.get_space_child_nodes(
                    node_info['space_id'], node_info['node_token']
                ):
                    child_node2_info = dict(
                        title=child_node2.title,
                        space_id=child_node2.space_id,
                        parent_node_token=child_node2.parent_node_token,
                        node_token=child_node2.node_token,
                        has_child=child_node2.has_child,
                    )

                    if f'Q{this_quarter}' in child_node2.title:
                        content.append(f'    {child_node2.title}: {child_node2_info}')

                        # get child node
                        if not child_node2_info['has_child']:
                            continue
                        for child_node3 in lark_client.get_space_child_nodes(
                            child_node2_info['space_id'], child_node2_info['node_token']
                        ):
                            child_node3_info = dict(
                                title=child_node3.title,
                                space_id=child_node3.space_id,
                                parent_node_token=child_node3.parent_node_token,
                                node_token=child_node3.node_token,
                                has_child=child_node3.has_child,
                            )
                            content.append(f'        {child_node3.title}: {child_node3_info}')
            else:
                content.append(f'{title}: {node_info}')

        return '\n'.join(content)

    @staticmethod
    def get_meeting_date(week_day: int) -> str:
        """获取本周指定星期几的日期, week_day: 0-6, 0: Monday, 1: Tuesday, ..., 6: Sunday"""
        meeting_date = datetime.now() + timedelta(days=(week_day - datetime.now().weekday()) % 7)
        this_week_date = meeting_date.strftime('%Y-%m-%d')

        quarter = (meeting_date.month - 1) // 3 + 1
        return f'{this_week_date} (Q{quarter})'
