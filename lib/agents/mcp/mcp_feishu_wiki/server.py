from typing import Annotated

import click
from fastmcp import FastMCP
from pydantic import Field

from lib.clients.lark.client import UserAuthenClientBuilder
from tasks.utils import load_user_access_token

from .wiki_helper import WikiInfoHelper


@click.command()
@click.option('--root_node_token', required=True, help='root node token of wiki')
def main(root_node_token: str):
    app = FastMCP('mcp-feishu-wiki')

    @app.tool()
    def copy_node(
        space_id: Annotated[str, Field(description='source wiki space id')],
        node_token: Annotated[str, Field(description='source template wiki node token')],
        target_space_id: Annotated[str, Field(description='target wiki space id')],
        target_parent_node_token: Annotated[
            str, Field(description='target wiki parent node token')
        ],
        target_node_title: Annotated[str, Field(description='target wiki node title')],
    ) -> str:
        """Copy a template wiki node from one space to another under specified parent node."""
        user_access_token = load_user_access_token()
        if not user_access_token:
            return 'user_access_token not found, please update it first, operation aborted'

        lark_client = UserAuthenClientBuilder.build_user_client(user_token=user_access_token)
        ok, result = lark_client.copy_node(
            src_space_id=space_id,
            src_node_token=node_token,
            target_space_id=target_space_id,
            target_parent_token=target_parent_node_token,
            title=target_node_title,
        )
        if not ok:
            return f'copy node failed: {result}'
        return (
            f'New wiki node created with token {result.node_token} and title is {target_node_title}'
        )

    @app.tool()
    def get_meeting_date(
        week_day: Annotated[
            int,
            Field(
                ge=0,
                le=6,
                description=(
                    'week day of meeting, 0-6, '
                    '0: Monday, 1: Tuesday, 2: Wednesday, '
                    '3: Thursday, 4: Friday, '
                    '5: Saturday, 6: Sunday'
                ),
            ),
        ] = 4,
    ) -> str:
        """Get the meeting date of this week based on the week day provided.

        Args:
            week_day: 0-6,
                0: Monday, 1: Tuesday, 2: Wednesday, 3: Thursday, 4: Friday, 5: Saturday, 6: Sunday
        """
        meeting_date = WikiInfoHelper.get_meeting_date(week_day)
        return f'周会时间安排在 {meeting_date}'

    @app.tool()
    def get_wiki_nodes_info() -> str:
        """Get the wiki nodes info of this week based on the root node token provided."""
        user_access_token = load_user_access_token()
        if not user_access_token:
            return 'user_access_token not found, please update it first, operation aborted'

        wiki_nodes = WikiInfoHelper.get_wiki_nodes_info(root_node_token, user_access_token)
        return f'现在已经创建的wiki信息如下:\n<wiki_nodes>\n{wiki_nodes}\n</wiki_nodes>'

    app.run(transport='stdio')
