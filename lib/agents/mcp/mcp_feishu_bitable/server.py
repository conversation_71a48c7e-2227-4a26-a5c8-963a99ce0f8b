import os
from typing import Annotated, List, Optional

import click
import pendulum
from fastmcp import FastMCP
from pydantic import Field

from lib.clients.lark import LarkClient
from lib.configs import config
from log import logger


@click.command()
def main():
    app_id = os.getenv('APP_ID')
    app_secret = os.getenv('APP_SECRET')

    mcp_app = FastMCP('mcp-feishu-bitable')

    @mcp_app.tool()
    def list_tables(
        bitable_token: Annotated[str, Field(description='Token of the bitable')],
        days: Annotated[
            Optional[int],
            Field(
                description='How many days ago the table created, if not set, list all tables',
            ),
        ] = None,
    ) -> str:
        """List all tables in the bitable app, you can filter by days created."""
        logger.info(f'List all tables in app: {app_id}')

        client = LarkClient.new(app_id, app_secret)

        bitable_app = client.bitable_app(bitable_app_token=bitable_token)

        table_list = bitable_app.tables()
        if not table_list:
            return 'No bitables found.'

        results = []
        for table in table_list:
            table_info = f'name: {table.name}, table_id: {table.table_id}'

            if days is not None:
                try:
                    t = pendulum.from_format(table.name, fmt='YYYY-MM-DD-HHmm', tz='Asia/Shanghai')
                    live_days = t.diff().days

                    if live_days >= days:
                        table_info += f', {live_days} days ago'
                    else:
                        continue
                except Exception:
                    continue

            results.append(table_info)

        if not results:
            return f'No tables created {days} days ago in {bitable_app.name}.'

        # 根据结果数量确定是否使用复数形式
        plural_suffix = 's' if len(results) > 1 else ''
        suffix = f'created {days} days ago' if days else ''
        return (
            f'Found {len(results)} table{plural_suffix} {suffix} in bitable {bitable_app.name}:\n'
            + '\n'.join(results)
        )

    @mcp_app.tool()
    def delete_tables(
        bitable_token: Annotated[str, Field(description='Token of the bitable')],
        table_ids: Annotated[List[str], Field(description='List of table ids to delete')],
    ) -> str:
        """Delete tables in a bitable."""
        logger.info(f'Deleting tables in app: {app_id} with table_ids: {table_ids}')
        if config.dry_run:
            return f'deleted {len(table_ids)} tables in the bitable.'

        client = LarkClient.new(app_id, app_secret)
        bitable = client.bitable_app(bitable_app_token=bitable_token)

        failed_count = 0
        for table_id in table_ids:
            response = bitable.delete_table(table_id)
            if not response.success:
                failed_count += 1
                logger.error(f'Failed to delete table: {table_id}, error: {response.error}')

        if failed_count > 0:
            return (
                f'{len(table_ids) - failed_count} tables deleted, but failed {failed_count} tables.'
            )

        return f'Successfully deleted {len(table_ids)} tables in the bitable {bitable.name}.'

    mcp_app.run(transport='stdio')
