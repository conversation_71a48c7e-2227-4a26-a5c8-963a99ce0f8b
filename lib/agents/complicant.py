from typing import Optional

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_openai.chat_models.base import BaseChatOpenAI

from lib.agents.tools.complicant import tools as complicant_tools
from lib.configs import config
from lib.llm.models import get_llm
from lib.llm.prompt import prompt_messages
from lib.output_parsers.complicant import Complicant
from lib.utils.reasoner import split_think_content
from prompt import complicant_plan_v2

format_instructions = Complicant.get_format_instructions()


def invoke(question: str, llm: Optional[BaseChatOpenAI] = None):
    llm = llm or get_llm('gpt4o')

    response = invoke_by_tool(question, llm)

    output = response.get('output')
    if not output:
        return None

    # split think and content parts
    results = split_think_content(output)

    # parse output
    output_obj = Complicant.parse(results.content)
    return output_obj


def invoke_by_tool(question: str, llm: BaseChatOpenAI):
    prompt = prompt_messages(
        complicant_plan_v2.PROMPT,
        ('human', '{question}'),
    ).partial(format_instructions=format_instructions)

    agent = create_tool_calling_agent(llm, complicant_tools, prompt)
    agent_executor = AgentExecutor.from_agent_and_tools(
        agent=agent,
        tools=complicant_tools,
        verbose=True,
    )

    response = agent_executor.invoke({'question': f'请检查如下数据:\n\n###\n{question}\n###\n\n'})
    return response


def invoke_by_reasoning(question: str, llm: BaseChatOpenAI, *, stream: bool = False):
    prompt = complicant_plan_v2.REASONING_PROMPT.format(
        format_instructions=format_instructions,
        question=f'请检查如下数据:\n\n###\n{question}\n###\n\n',
    )

    if stream:
        content = ''
        for chunk in llm.stream(prompt):
            chunk_content = chunk.content
            if config.debug:
                print(chunk_content, end='', flush=True)
            content += chunk_content
        return Complicant.parse(content)

    chain = llm | Complicant.get_parser()
    return chain.invoke(prompt)
