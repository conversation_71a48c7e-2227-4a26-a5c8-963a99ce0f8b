from langchain_core.language_models.base import BaseLanguageModel

from lib.configs import config
from lib.llm.models import get_llm
from lib.output_parsers.copilot import CopilotEmail
from prompt.extract_copilot_email import get_prompt


def invoke(email_content: str, stream: bool = False):
    model = config.github.llm_model or 'gpt4o'
    llm = get_llm(model)

    if stream:
        return stream_invoke_by_model(llm, email_content)
    else:
        return invoke_by_model(llm, email_content)


def invoke_by_model(llm: 'BaseLanguageModel', email_content: str) -> CopilotEmail:
    """parse email content to `CopilotEmail` object.

    ref: https://python.langchain.com/docs/concepts/structured_outputs/
    """

    chain = llm | CopilotEmail.get_parser()

    output = chain.invoke(get_prompt(email_content))
    return output


def stream_invoke_by_model(llm: 'BaseLanguageModel', email_content: str) -> CopilotEmail:
    """parse email content to `CopilotEmail` object.

    ref: https://python.langchain.com/docs/concepts/structured_outputs/
    """

    output = []

    for chunk in llm.stream(get_prompt(email_content)):
        if hasattr(chunk, 'content'):
            chunk_content = chunk.content
        else:
            # 如果chunk已经是字符串
            chunk_content = str(chunk)

        # 累积输出内容
        output.append(chunk_content)

    # 流式输出完成后，解析完整的输出字符串
    return CopilotEmail.parse(''.join(output))
