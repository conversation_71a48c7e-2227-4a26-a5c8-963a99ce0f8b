import asyncio

import pendulum
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent

from lib.configs import config
from lib.llm.mcp import load_mcp_tools_schema
from lib.llm.models import get_llm
from lib.output_parsers.cloud_notification import GroupedNotification
from prompt.cloud_notification import CLOUD_NOTIFICATION_SUMMARY_PROMPT_TEMPLATE

from .tools_invoke import graph_invoke


def current_time(tz: str = 'Asia/Shanghai') -> str:
    return pendulum.now(tz).to_rfc3339_string()


def summary(
    content: str,
    *,
    stream: bool = True,
    use_tool: bool = True,
    model_name: str = 'claude-3-5',
    temperature: float = 0.0,
) -> GroupedNotification:
    llm = get_llm(model_name, temperature=temperature)

    if use_tool:
        return summary_by_mcp(llm, content)

    if stream:
        return stream_summary_by_model(llm, content)
    else:
        return summary_by_model(llm, content)


def summary_by_model(llm: 'BaseLanguageModel', message: str) -> GroupedNotification:
    system_prompt = CLOUD_NOTIFICATION_SUMMARY_PROMPT_TEMPLATE.format(
        current_time=current_time(),
        format_instructions=GroupedNotification.get_format_instructions(),
    )
    question = f'以下是所有的公有云消息:\n####\n{message}\n####'

    chain = llm | GroupedNotification.get_parser()
    output = chain.invoke([SystemMessage(system_prompt), HumanMessage(question)])
    return output


def stream_summary_by_model(llm: 'BaseLanguageModel', message: str):
    system_prompt = CLOUD_NOTIFICATION_SUMMARY_PROMPT_TEMPLATE.format(
        current_time=current_time(),
        format_instructions=GroupedNotification.get_format_instructions(),
    )
    question = f'以下是所有的公有云消息:\n####\n{message}\n####'

    chain = llm

    output = []
    for chunk in chain.stream([SystemMessage(system_prompt), HumanMessage(question)]):
        if hasattr(chunk, 'content'):
            chunk_content = chunk.content
        else:
            # 如果chunk已经是字符串
            chunk_content = str(chunk)

        # 累积输出内容
        output.append(chunk_content)

        if config.debug:
            print(chunk_content, end='', flush=True)

    return GroupedNotification.parse(''.join(output))


async def asummary_by_mcp(
    llm: 'BaseLanguageModel',
    message: str,
) -> GroupedNotification:
    system_prompt = CLOUD_NOTIFICATION_SUMMARY_PROMPT_TEMPLATE.format(
        current_time=current_time(),
        format_instructions=GroupedNotification.get_format_instructions(),
    )

    mcp_client = MultiServerMCPClient(load_mcp_tools_schema(names=['cmdb']))
    tools = await mcp_client.get_tools()

    graph = create_react_agent(llm, tools, prompt=system_prompt)

    question = f'请帮我总结以下公有云消息:\n####\n{message}\n####'
    result = await graph_invoke(graph, [('user', question)])

    parsed_result: GroupedNotification = GroupedNotification.parse(result)
    return parsed_result


def summary_by_mcp(llm: 'BaseLanguageModel', message: str) -> GroupedNotification:
    return asyncio.run(asummary_by_mcp(llm, message))
