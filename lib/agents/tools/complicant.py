from langchain.agents import tool


@tool
def diff_lte(a: float, b: float, threshold: float, include_equal: bool = True) -> bool:
    """计算a相对b的变化差值是否小于或者等于(不大于)阈值.

    Args:
        a: The first number.
        b: The second number.
        threshold: The thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
            例如: "a是10%, b是5%, 阈值是25%, 那么a=10, b=5, threshold=25".
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        >> # 10s 20s 1s
        >> diff_lte(10.0, 20.0, 1)
        False # Returns True, because |10.0-20.0| = 10.0 <= 1
        >> # 1ms 20ms 1s
        >> diff_lte(1, 20, 1000)
        True # Returns True, because 1s = 1000ms, |1-20| = 19 <= 1000
        >> # 10% 20% 5%
        >> diff_lte(10, 20, 5)
        False # Returns True, because |10-20| = 10 > 5
        ```

    Returns true if a is less(or equal) than b, otherwise false.
    """
    if include_equal:
        return abs(a - b) <= threshold
    return abs(a - b) < threshold


@tool
def ratio_lte(a: float, b: float, threshold: float, include_equal: bool = True) -> bool:
    """计算a相对b的变化比率是否小于或等于(不大于)阈值.

    Args:
        a: The first number.
        b: The second number.
        threshold: The thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
            例如: "a是10%, b是5%, 阈值是25%, 那么a=10, b=5, threshold=25".
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        # 10.0% 20.0% 50%
        >> ratio_lte(10.0, 20.0, 50)
        True # Returns True, because (20.0-10.0)*100/20.0 = 50% >= 50%
        ```

    Returns true if a is less(or equal) than b, otherwise false.
    """
    if include_equal:
        return abs((a - b) * 100 / b) <= threshold
    return abs((a - b) * 100 / b) < threshold


@tool
def diff_gte(a: float, b: float, threshold: float, include_equal: bool = True) -> bool:
    """计算a相对b的变化差值是否大于或者等于(不小于)阈值.

    Args:
        a: The first number.
        b: The second number.
        threshold: The thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        >> diff_gte(20.0, 10.0, 10)
        True # Returns True, because |20.0-10.0| = 10.0 >= 10
        ```
    """
    if include_equal:
        return abs(a - b) >= threshold
    return abs(a - b) > threshold


@tool
def ratio_gte(a: float, b: float, threshold: float, include_equal: bool = True) -> bool:
    """计算a相对b的变化比率是否大于或等于(不小于)阈值.

    Args:
        a: The first number.
        b: The second number.
        threshold: The thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        # 10.0% 20.0% 20%
        >> ratio_gte(10.0, 20.0, 20)
        True # Returns True, because (20.0-10.0)*100/20.0 = 50 > 20
        ```

    Returns true if a is less(or equal) than b, otherwise false.
    """
    if include_equal:
        return abs((a - b) * 100 / b) >= threshold
    return abs((a - b) * 100 / b) > threshold


@tool
def batch_diff_lte(
    a: list[float], b: list[float], threshold: float, include_equal: bool = True
) -> bool:
    """批量计算a中所有数值相对b中所有对应下表的数值的变化差值是否小于或者等于阈值,
    当include_equal为True时，小于或等于的结果都被认为True, 否则仅在小于时才为True.

    Args:
        a: The numbers of current period.
        b: The number of preriod before current.
        threshold: the thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
            例如: "a是10%, b是5%, 阈值是25%, 那么a=10, b=5, threshold=25".
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        >> data = '''
        10s 10.1s 1s
        2s 1s 1s
        '''
        >> batch_diff_lte(a=[10,2], b=[10.1,1], 1, True)
        True # Returns True, because |10-10.1| = 0.1 < 1, |2-1| = 1 == 1
        ```

    Returns True if all number in a is less(or equal) than numbers in b as some index,
    otherwise False.
    """
    if include_equal:
        return all(abs(a[i] - b[i]) <= threshold for i in range(len(a)))
    return all(abs(a[i] - b[i]) < threshold for i in range(len(a)))


@tool
def batch_diff_gte(
    a: list[float], b: list[float], threshold: float, include_equal: bool = True
):
    """批量计算a中所有数值相对b中所有对应下表的数值的变化差值是否大于或等于(include_equal为True时)阈值,
    当include_equal为True时，大于或等于的结果为True, 否则仅在大于时才为True.

    Args:
        a: The numbers of current period.
        b: The number of preriod before current.
        threshold: the thresshold value,
            如果a和b都是百分数的数值部分, 那么threshold也是阈值的数值部分.
            例如: "a是10%, b是5%, 阈值是25%, 那么a=10, b=5, threshold=25".
        include_equal: Include equal value in the comparison,
            set to False if you want to exclude equal value, default True.

    Examples:
        ```python
        >> data = '''
        10s 10.1s 1s
        2s 1s 1s
        '''
        >> batch_diff_gte(a=[10,2], b=[10.1,1], 1, True)
        True # Returns True, because |10-10.1| = 0.1 < 1, |2-1| = 1 == 1
        ```

    Returns True if all number in a is less(or equal) than numbers in b as some index,
    otherwise False.
    """
    if include_equal:
        return all(abs(a[i] - b[i]) >= threshold for i in range(len(a)))
    return all(abs(a[i] - b[i]) > threshold for i in range(len(a)))


tools = [
    # diff_lte,
    # diff_gte,
    # ratio_lte,
    # ratio_gte,
    batch_diff_lte,
    batch_diff_gte,
]
