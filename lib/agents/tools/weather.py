import os

from pydantic import BaseModel, Field

from lib.agents.chat_image import invoke as chat_image_invoke
from lib.constants import STORE_ROOT
from lib.llm.models import get_llm
from lib.tools.baidu import baidu_weather
from lib.utils.timerange import same_minute_in
from prompt.weather import PROMPT as WEATHER_PROMPT


class get_weather(BaseModel):
    """获取城市的天气信息."""

    city: str = Field(default='上海', description='城市名称')
    when: str = Field(default='今天', description='时间')
    cache: bool = Field(default=True, description='是否使用缓存')

    def __call__(self):
        # 1. 从天气网站获取城市天气页面的 URL
        # url = 'http://hunan.promotion.weather.com.cn/mweather/101020100.shtml'

        os.makedirs(STORE_ROOT, exist_ok=True)
        saved_to = STORE_ROOT / f'{self.city}-{self.when}-weather-{same_minute_in()}'

        cache_file = saved_to.with_suffix('.txt')
        img_file = saved_to.with_suffix('.png')

        if self.cache:
            if cache_file.exists():
                return cache_file.read_text()

        if not img_file.exists():
            baidu_weather(f'{self.city}{self.when}天气', img_file)

        # 4. LLM 分析图片中的天气信息
        response = chat_image_invoke(WEATHER_PROMPT, img_file, get_llm('claude-3-5'))
        if self.cache:
            cache_file.write_text(response)
        return f'{self.city}{self.when}的天气如下:\n' + response


def main():
    weather = get_weather(city='上海', cache=True)
    response = weather()
    print(response)


if __name__ == '__main__':
    main()
