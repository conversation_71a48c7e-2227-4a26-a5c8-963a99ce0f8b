from typing import Callable, List, Literal, Optional, Sequence, Tuple

import pendulum
from langchain_core.messages import (
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.output_parsers.openai_tools import PydanticToolsParser
from langchain_openai.chat_models.base import BaseChatOpenAI
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

from lib.llm.models import get_llm
from lib.output_parsers.period import Strategy
from log import logger


def get_now():
    """获取当前时间"""
    return pendulum.now('Asia/Shanghai')


TimeRangeType = Literal[
    'day-on-day',
    'week-on-week',
    'month-on-month',
    'year-on-year',
    '24-hour-on-24-hour',
]


class Timerange(BaseModel):
    """获取时间范围"""

    range_time: TimeRangeType = Field(..., description='时间的周期')

    def __call__(self):
        """调用获取比较周期的方法"""
        now = get_now()
        formats = 'YYYY-MM-DD HH:mm:ss'
        return self.get_comparison_periods(now, formats)

    def get_comparison_periods(self, now: pendulum.DateTime, formats: str):
        """
        根据 self.range_time 获取比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        if self.range_time == 'day-on-day':
            return self.get_day_on_day(now, formats)
        elif self.range_time == 'week-on-week':
            return self.get_week_on_week(now, formats)
        elif self.range_time == 'month-on-month':
            return self.get_month_on_month(now, formats)
        elif self.range_time == 'year-on-year':
            return self.get_year_on_year(now, formats)
        elif self.range_time == '24-hour-on-24-hour':
            return self.get_24_hour_on_24_hour(now, formats)
        else:
            raise ValueError(f'Unsupported range_time: {self.range_time}')

    def get_day_on_day(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取日环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(days=1)
        period_previous = now.subtract(days=2)
        return {
            'period_previous': (
                period_previous.start_of('day').format(formats),
                period_previous.end_of('day').format(formats),
            ),
            'period_current': (
                period_current.start_of('day').format(formats),
                period_current.end_of('day').format(formats),
            ),
        }

    def get_week_on_week(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取周环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(weeks=1)
        period_previous = now.subtract(weeks=2)
        return {
            'period_previous': (
                period_previous.start_of('week').format(formats),
                period_previous.end_of('week').format(formats),
            ),
            'period_current': (
                period_current.start_of('week').format(formats),
                period_current.end_of('week').format(formats),
            ),
        }

    def get_month_on_month(
        self, now: pendulum.DateTime, formats: str
    ) -> dict[str, Tuple[str, str]]:
        """
        获取月环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(months=1)
        period_previous = now.subtract(months=2)
        return {
            'period_previous': (
                period_previous.start_of('month').format(formats),
                period_previous.end_of('month').format(formats),
            ),
            'period_current': (
                period_current.start_of('month').format(formats),
                period_current.end_of('month').format(formats),
            ),
        }

    def get_year_on_year(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取年环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(years=1)
        period_previous = now.subtract(years=2)

        return {
            'period_previous': (
                period_previous.start_of('year').format(formats),
                period_previous.end_of('year').format(formats),
            ),
            'period_current': (
                period_current.start_of('year').format(formats),
                period_current.end_of('year').format(formats),
            ),
        }

    def get_24_hour_on_24_hour(
        self, now: pendulum.DateTime, formats: str
    ) -> dict[str, Tuple[str, str]]:
        """
        获取24小时环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(hours=24)
        period_previous = now.subtract(hours=48)
        return {
            'period_previous': (
                period_previous.format(formats),
                period_current.subtract(seconds=1).format(formats),
            ),
            'period_current': (
                period_current.format(formats),
                now.format(formats),
            ),
        }


class CompareStrategy(BaseModel):
    """巡检的策略. 包含巡检的列、比较的方法和阈值信息.
    例如: 最大值的差值在正负10%之间.
    """

    threshold_info: str = Field(
        description='比较的方法和阈值信息',
    )

    def __call__(self):
        return {'compare_info': self.threshold_info}


tools: List[type[BaseModel]] = [Timerange, CompareStrategy]

llm = get_llm('gpt4o')
llm_with_tools = llm.bind_tools(tools=tools)


def invoke_by_llm(model: BaseChatOpenAI, question: str):
    model_with_tools = model.bind_tools(tools=tools)
    chain = model_with_tools | PydanticToolsParser(tools=tools)
    response = chain.invoke([HumanMessage(content=question)])
    print(response)

    results = {}
    for r in response:
        result = r()
        print(f'{type(r)}:\n\t{result}')
        results.update(result)

    print('Final results:', results)

    return Strategy(**results)


def invoke(question: str):
    chain = llm_with_tools | PydanticToolsParser(tools=tools)
    response = chain.invoke([HumanMessage(content=question)])
    print(response)

    results = {}
    for r in response:
        result = r()
        print(f'{type(r)}:\n\t{result}')
        results.update(result)

    print('Final results:', results)

    return Strategy(**results)


class add(BaseModel):
    """Add a an b."""

    a: int = Field(..., description='The first integer.')
    b: int = Field(..., description='The second integer.')

    def __call__(self):
        return self.a + self.b


class multiply(BaseModel):
    """Multiply a and b."""

    a: int = Field(..., description='The first integer.')
    b: int = Field(..., description='The second integer.')

    def __call__(self):
        return self.a * self.b


CallableModel = Callable[..., BaseModel]


def invoke_sample_tools(model: BaseChatOpenAI, question: str):
    tools: List[CallableModel] = [add, multiply]
    return invoke_with_tools(model, question, tools=tools)


def invoke_with_tools(
    model: BaseChatOpenAI,
    question: str,
    *,
    tools: Sequence[CallableModel],
    system_prompt: Optional[str] = None,
):
    tools_map = {tool.__name__.lower(): tool for tool in tools}

    model_with_tools = model.bind_tools(tools=tools)

    messages: List[BaseMessage] = [HumanMessage(content=question)]
    if system_prompt:
        messages.insert(0, SystemMessage(content=system_prompt))

    msg = model_with_tools.invoke(messages)
    messages.append(msg)

    while 1:
        if not msg.tool_calls:  # type: ignore
            break

        for tool_call in msg.tool_calls:  # type: ignore
            print(f'tool_call({type(tool_call)}):', tool_call)

            tool_name = tool_call['name'].lower()

            if tool_name not in tools_map:
                print('Unsupported tool:', tool_call)
                continue

            tool: CallableModel = tools_map[tool_name](**tool_call['args'])  # type: ignore
            tool_msg = tool()
            messages.append(ToolMessage(content=f'{tool_msg}', tool_call_id=tool_call['id']))

        msg = model_with_tools.invoke(messages)
        messages.append(msg)

    # At last, get the final message
    final_msg = messages[-1]
    # print(f'final_msg({type(final_msg)}):', final_msg)

    return final_msg


async def graph_invoke(graph: CompiledStateGraph, question: str | List[tuple]):
    async for chunk in graph.astream({'messages': question}, debug=True):
        message_type = ''
        chunk_messages = None

        if 'agent' in chunk:
            chunk_messages = chunk['agent']['messages']
            message_type = 'AgentMessage'
        elif 'tools' in chunk:
            chunk_messages = chunk['tools']['messages']
            message_type = 'ToolMessage'

        if chunk_messages:
            for message in chunk_messages:
                if message_type == 'AgentMessage':
                    tool_calls = message.additional_kwargs.get('tool_calls', [])
                    if tool_calls:
                        for tool_call in tool_calls:
                            logger.debug('AgentMessage.ToolCall: {}', tool_call)
                    else:
                        logger.debug('AgentMessage: {}', message)
                else:
                    logger.debug('{}: {}', message_type, message)

    if 'agent' in chunk:
        content = chunk['agent']['messages'][-1].content
        return content
    return ''
