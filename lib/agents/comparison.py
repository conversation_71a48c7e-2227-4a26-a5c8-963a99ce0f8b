from typing import Callable, List, Literal, Tuple

import pendulum
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_core.output_parsers.openai_tools import PydanticToolsParser
from langchain_openai.chat_models.base import BaseChatOpenAI
from pydantic import BaseModel, Field

from lib.llm.models import get_llm
from lib.output_parsers.period import Strategy


def get_now():
    """获取当前时间"""
    return pendulum.now('Asia/Shanghai')


TimeRangeType = Literal[
    'day-on-day',
    'week-on-week',
    'month-on-month',
    'year-on-year',
    '24-hour-on-24-hour',
]


class Timerange(BaseModel):
    """获取时间范围"""

    range_time: TimeRangeType = Field(
        ...,
        description=(
            '时间的周期,'
            '注意: 24-hour-on-24-hour 是 24小时环比(以当前时间为结束时间), 而day-on-day 则是日环比(以一天的0点为结束时间)'
        ),
    )

    def __call__(self):
        """调用获取比较周期的方法"""
        now = get_now()
        formats = 'YYYY-MM-DD HH:mm:ss'
        return self.get_comparison_periods(now, formats)

    def get_comparison_periods(self, now: pendulum.DateTime, formats: str):
        """
        根据 self.range_time 获取比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        if self.range_time == 'day-on-day':
            return self.get_day_on_day(now, formats)
        elif self.range_time == 'week-on-week':
            return self.get_week_on_week(now, formats)
        elif self.range_time == 'month-on-month':
            return self.get_month_on_month(now, formats)
        elif self.range_time == 'year-on-year':
            return self.get_year_on_year(now, formats)
        elif self.range_time == '24-hour-on-24-hour':
            return self.get_24_hour_on_24_hour(now, formats)
        else:
            raise ValueError(f'Unsupported range_time: {self.range_time}')

    def get_day_on_day(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取日环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(days=1)
        period_previous = now.subtract(days=2)
        return {
            'period_previous': (
                period_previous.start_of('day').format(formats),
                period_previous.end_of('day').format(formats),
            ),
            'period_current': (
                period_current.start_of('day').format(formats),
                period_current.end_of('day').format(formats),
            ),
        }

    def get_week_on_week(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取周环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(weeks=1)
        period_previous = now.subtract(weeks=2)
        return {
            'period_previous': (
                period_previous.start_of('week').format(formats),
                period_previous.end_of('week').format(formats),
            ),
            'period_current': (
                period_current.start_of('week').format(formats),
                period_current.end_of('week').format(formats),
            ),
        }

    def get_month_on_month(
        self, now: pendulum.DateTime, formats: str
    ) -> dict[str, Tuple[str, str]]:
        """
        获取月环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(months=1)
        period_previous = now.subtract(months=2)
        return {
            'period_previous': (
                period_previous.start_of('month').format(formats),
                period_previous.end_of('month').format(formats),
            ),
            'period_current': (
                period_current.start_of('month').format(formats),
                period_current.end_of('month').format(formats),
            ),
        }

    def get_year_on_year(self, now: pendulum.DateTime, formats: str) -> dict[str, Tuple[str, str]]:
        """
        获取年环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(years=1)
        period_previous = now.subtract(years=2)

        return {
            'period_previous': (
                period_previous.start_of('year').format(formats),
                period_previous.end_of('year').format(formats),
            ),
            'period_current': (
                period_current.start_of('year').format(formats),
                period_current.end_of('year').format(formats),
            ),
        }

    def get_24_hour_on_24_hour(
        self, now: pendulum.DateTime, formats: str
    ) -> dict[str, Tuple[str, str]]:
        """
        获取24小时环比的比较周期。

        参数:
        - now: 当前时间对象
        - formats: 时间格式

        返回:
        - dict: 包含 period_previous 和 period_current 的字典
        """
        period_current = now.subtract(hours=24)
        period_previous = now.subtract(hours=48)
        return {
            'period_previous': (
                period_previous.format(formats),
                period_current.subtract(seconds=1).format(formats),
            ),
            'period_current': (
                period_current.format(formats),
                now.format(formats),
            ),
        }


class CompareStrategy(BaseModel):
    """巡检的策略. 包含巡检的列、比较的方法和阈值信息.
    例如: 最大值的差值在正负10%之间.
    """

    threshold_info: str = Field(
        description='比较的方法和阈值信息',
    )

    def __call__(self):
        return {'compare_info': self.threshold_info}


tools: List[type[BaseModel]] = [Timerange, CompareStrategy]


def stream_invoke_by_llm(model: BaseChatOpenAI, question: str):
    model_with_tools = model.bind_tools(tools=tools)
    chain = model_with_tools | PydanticToolsParser(tools=tools)

    prompt = f"""请根据用户问题解析出JSON格式的巡检信息

{Strategy.get_format_instructions()}
"""
    stream = chain.stream(
        [
            SystemMessage(prompt),
            HumanMessage(question),
        ]
    )

    results = {}
    for chunk in stream:
        print(f'chunk<{type(chunk)}>: {chunk}')

    for f in chunk:
        result = f()
        results.update(result)

    print('Final results:', results)
    return Strategy(**results)


def invoke_by_llm(model: BaseChatOpenAI, question: str):
    model_with_tools = model.bind_tools(tools=tools)
    chain = model_with_tools | PydanticToolsParser(tools=tools)
    response = chain.invoke([HumanMessage(content=question)])
    print(response)

    results = {}
    for r in response:
        result = r()
        print(f'{type(r)}:\n\t{result}')
        results.update(result)

    print('Final results:', results)

    return Strategy(**results)


def invoke(question: str, model_name: str = 'gpt-4-1'):
    llm = get_llm(model_name)
    return stream_invoke_by_llm(llm, question)


class add(BaseModel):
    """Add a and b."""

    a: int = Field(..., description='The first integer.')
    b: int = Field(..., description='The second integer.')

    def __call__(self):
        return self.a + self.b


class multiply(BaseModel):
    """Multiply a and b."""

    a: int = Field(..., description='The first integer.')
    b: int = Field(..., description='The second integer.')

    def __call__(self):
        return self.a * self.b


CallableModel = Callable[..., BaseModel]


def invoke_sample_tools(model: BaseChatOpenAI, question: str):
    tools: List[CallableModel] = [add, multiply]
    return invoke_custom_tools(model, question, tools)


def invoke_custom_tools(
    model: BaseChatOpenAI,
    question: str,
    tools: List[CallableModel],
):
    tools_map = {tool.__name__.lower(): tool for tool in tools}

    model_with_tools = model.bind_tools(tools=tools)

    messages: List[BaseMessage] = [HumanMessage(content=question)]

    msg = model_with_tools.invoke(messages)
    print(f'{type(msg)} msg:', msg)
    messages.append(msg)

    while 1:
        if not msg.tool_calls:  # type: ignore
            break

        for tool_call in msg.tool_calls:  # type: ignore
            print(f'tool_call({type(tool_call)}):', tool_call)

            tool_name = tool_call['name'].lower()

            if tool_name not in tools_map:
                print('Unsupported tool:', tool_call)
                continue

            tool: CallableModel = tools_map[tool_name](**tool_call['args'])  # type: ignore
            tool_msg = tool()
            messages.append(ToolMessage(content=f'{tool_msg}', tool_call_id=tool_call['id']))

        msg = model_with_tools.invoke(messages)
        messages.append(msg)

    # At last, get the final message
    final_msg = messages[-1]
    print(f'final_msg({type(final_msg)}):', final_msg)

    return final_msg
