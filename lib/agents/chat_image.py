import base64
from pathlib import Path
from typing import Optional

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage
from PIL import Image

from lib.llm.models import get_llm


class HumaneImageMessage(HumanMessage):
    def __init__(self, image_path: Path):
        super().__init__(
            content=[
                {
                    'type': 'image_url',
                    'image_url': {
                        'url': base64_image(image_path),
                    },
                }
            ],
        )


def get_image_type(img_path: Path):
    # 文件头部特征码
    try:
        with Image.open(img_path) as img:
            return f'image/{img.format.lower()}'
    except Exception:
        return None


def base64_image(image_path: Path, with_prefix: bool = True) -> str:
    with open(image_path, 'rb') as f:
        image_base64 = base64.b64encode(f.read()).decode('utf-8')
        # 如何判定图片类型: image/png, image/jpeg, image/webp

    if with_prefix:
        mime_type = get_image_type(image_path)
        if mime_type:
            return f'data:{mime_type};base64,{image_base64}'
        raise Exception(f'unknown image type, {mime_type}')
    return image_base64


# https://python.langchain.com/v0.2/docs/integrations/chat/azure_chat_openai/
# llm = get_llm('gpt4o-mini')
# llm = get_llm('qwen-vl')


def invoke(system_prompt: str, img_path: Path, llm: Optional[BaseChatModel] = None):
    model = llm or get_llm('gpt4o-mini')
    messages = [
        SystemMessage(system_prompt),
        HumaneImageMessage(img_path),
    ]

    content = []
    for chunk in model.stream(messages):
        print(chunk.content, end='', flush=True)
        content.append(chunk.content)

    return ''.join(content)
