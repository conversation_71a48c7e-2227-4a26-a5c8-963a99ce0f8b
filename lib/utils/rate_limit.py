import time
from typing import Iterator

from lib.utils.printer import debug


class RateLimiter:
    """速率限制器, 用于控制迭代器生成的速度。

    Args:
        limit (int, `defualt 5`): The max number of item yield allowed per second.
        period (int, `default 1000`): The period of time in milliseconds.
    """

    def __init__(self, limit: int = 5, period: int = 1000):
        self.limit = limit
        self.period = period

        self.count = 0
        self.last_time = time.time() * 1000

    def __call__(self, it: Iterator):
        """迭代器生成项时应用速率控制。

        Args:
            it (`Iterator`): 迭代器对象。
        """
        for item in it:
            debug('item', item)
            current_time = time.time() * 1000
            elapsed_time = current_time - self.last_time

            if elapsed_time < self.period:
                debug(f'count is {self.count}, limit is {self.limit}')

                self.count += 1
                if self.count > self.limit:
                    # debug(
                    #     f'trigger rate limit, wait 1 sec, sleep {self.period - elapsed_time}',
                    #     color='red',
                    # )
                    time.sleep((self.period - elapsed_time) / 1000)
                    self.last_time = time.time() * 1000
                    self.count = 0
            else:
                debug('reset count', color='green')
                self.last_time = current_time
                self.count = 0

            yield item
