import os
import time
from pathlib import Path
from typing import List, Optional

import httpx
from PIL import Image, ImageDraw, ImageFont

from lib.constants import STORE_ROOT


def download(url: str, base_path: Path, timeout: float = 10.0) -> Path:
    """
    下载图片文件并保存到指定路径。

    Args:
        url (str): 图片文件的 URL。
        base_path (Path): 保存图片文件的基础路径。

    Returns:
        Path: 下载的图片文件的路径。
    """
    local_filename = base_path / Path(f'{time.time_ns()}.png').name
    with httpx.stream('GET', url, verify=False, timeout=timeout) as response:
        response.raise_for_status()

        os.makedirs(base_path, exist_ok=True)

        with open(local_filename, 'wb') as file:
            for chunk in response.iter_bytes():
                file.write(chunk)
    return local_filename


def watermark(
    input_image_path: Path,
    watermark_text: str,
    output_image_path: Optional[Path] = None,
    position: str = 'bottom-right',
    font_size: int = 20,
):
    """Add watermark to an image.

    Args:
        input_image_path Path: The path to the image.
        output_image_path Path: The path to save the image.
        watermark_text str: The text of the watermark.
        position str: The position of the watermark. Default to 'bottom-right'.
    """
    with Image.open(input_image_path).convert('RGBA') as original:
        width, height = original.size

        font = ImageFont.load_default(size=font_size)

        x1, y1, x2, y2 = font.getbbox(watermark_text)

        if position == 'top-left':
            x = x1 + 10
            y = y1 + 10
        elif position == 'top-right':
            x = width - x2 - 10
            y = y1 + 10
        elif position == 'bottom-right':
            x = width - x2 - 10
            y = height - y2 - 10
        elif position == 'bottom-left':
            x = x1 + 10
            y = height - y2 - 10
        else:
            raise ValueError(f'Invalid position: {position}')

        # with Image.new('RGBA', original.size, (255, 255, 255, 0)) as txt:
        with Image.new('RGBA', original.size, (0, 0, 0, 0)) as txt:
            # 在空白图片上绘制水印文字
            editable_image = ImageDraw.Draw(txt)
            editable_image.text(
                (x, y), watermark_text, fill=(255, 255, 255, 128), font=font
            )

            # 将原始图片和水印图片合成
            watermarked = Image.alpha_composite(original, txt)

            output_image_path = output_image_path or input_image_path.with_name(
                input_image_path.with_suffix('.watermark.png').name
            )
            watermarked.save(output_image_path)
            return output_image_path


def combine(files: List[Path], save_to: str):
    """Combine multiple images into one image.

    Args:
        files List[Path]: A list of image files.
        save_to str: The path to save the combined image.
    """
    images = []

    for filepath in files:
        with open(filepath, 'rb') as fp:
            image = Image.open(fp).copy()
            images.append(image)

    total_height = sum(img.height for img in images)
    max_width = max(img.width for img in images)
    combied_image = Image.new('RGB', (max_width, total_height))

    y_offset = 0
    for img in images:
        combied_image.paste(img, (0, y_offset))
        y_offset += img.height

    combied_image.save(save_to)


if __name__ == '__main__':
    # saved_to = download(
    #     'https://oma-monitor.lilithgame.com/render/d-solo/PwMJtdvnz/k8s-dashboard?orgId=1&var-datasource=f70420bb-c623-4c49-b58b-7bed3803830e&var-cluster_name=tva-igame-prod-global-k8s&var-NameSpace=btcheck-global&var-Pod=prod-global-btcheck-qbg9h&from=now-24h&to=now&panelId=58&width=1280&height=600',
    #     STORE_ROOT,
    # )
    # print(f'saved to : {saved_to}')

    saved_to = STORE_ROOT / '1732291802444267022.png'
    watermark(
        saved_to,
        'Generated by Argus',
        font_size=13,
        position='top-right',
    )
