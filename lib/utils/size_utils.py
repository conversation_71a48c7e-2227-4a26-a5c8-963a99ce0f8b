import re


def parse_size(size_str: str) -> int:
    """将大小字符串（如 '50M', '10K'）转换为字节数

    Args:
        size_str: 大小字符串，支持 K, M, G, T 单位

    Returns:
        转换后的字节数

    Raises:
        ValueError: 如果输入格式无效
    """
    if not size_str:
        raise ValueError('Size string cannot be empty')

    # 匹配数字和单位
    match = re.match(r'^(\d+(?:\.\d+)?)\s*([KMGTP]?)$', size_str.upper())
    if not match:
        raise ValueError(f'Invalid size format: {size_str}')

    num = float(match.group(1))
    unit = match.group(2)

    # 根据单位计算字节数
    if unit == 'K':
        return int(num * 1024)
    elif unit == 'M':
        return int(num * 1024 * 1024)
    elif unit == 'G':
        return int(num * 1024 * 1024 * 1024)
    elif unit == 'T':
        return int(num * 1024 * 1024 * 1024 * 1024)
    else:
        return int(num)  # 无单位时返回原始字节数
