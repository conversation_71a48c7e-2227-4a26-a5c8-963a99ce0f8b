from operator import itemgetter


class PipeDict(dict):
    def __or__(self, other):
        """重载 | 操作符，使得可以使用 bitables | itemgetter('ads').

        Args:
            other: itemgetter instance on the right side of "|".

        Returns:
            The result of itemgetter(self) or raise Exception.

        Examples:
            >>> bitables = PipeDict({'ads': 'ads_table', 'mona': 'mona_table'})
            >>> bitables | itemgetter('ads')
            'ads_table'

            >>> # step further:
            >>> bitables | 'ads'
            'ads_table'
        """
        if isinstance(other, itemgetter):
            value = other(self)
            if isinstance(value, dict):
                return PipeDict(value)
            return value

        elif isinstance(other, str):
            value = self[other]
            if isinstance(value, dict):
                return PipeDict(value)
            return value

        raise NotImplementedError()
