import json
import re

from pydantic import BaseModel, Field


class ThinkContent(BaseModel):
    think_content: str = Field(default='')
    think_exists: bool = Field(default=False)
    content: str = Field(default='')


def split_think_content(s: str) -> 'ThinkContent':
    pattern = r'<think>(.*?)</think>'
    think_content = re.search(pattern, s, re.DOTALL)
    think_exists = False
    if think_content:
        think_content = think_content.group(1).strip()
        think_exists = True
    else:
        think_content = ''

    result = re.sub(r'<think>.*?</think>', '', s, flags=re.DOTALL).strip()
    return ThinkContent(
        think_content=think_content,
        content=result,
        think_exists=think_exists,
    )


default_tool_calls_tag = 'tool_calls'
default_tool_calls_pattern = rf'<{default_tool_calls_tag}>(.*?)</{default_tool_calls_tag}>'


def split_tool_calls(s: str, pattern: str = default_tool_calls_pattern) -> list:
    content = re.search(pattern, s, re.DOTALL)
    if content:
        content = content.group(1).strip()
        if content:
            return json.loads(content)
    return []
