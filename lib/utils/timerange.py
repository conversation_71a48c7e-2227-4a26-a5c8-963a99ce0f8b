import time
from datetime import datetime, timedelta
from typing import Tuple

import pendulum


def today() -> str:
    return pendulum.today().to_date_string()


def now_string(format: str = 'Y-MM-DD HH:mm') -> str:
    return pendulum.now().format(format)


def timestamp_ms(t: str, tz: str = 'Asia/Shanghai'):
    """将时间字符串转换为时间戳（毫秒"""
    that = pendulum.parse(t, tz=tz)
    ts = that.timestamp() * 1000  # type: ignore
    return int(ts)


def same_minute_in(adjust_minute: int = 10, fomat: str = 'YMMDDHHmm'):
    now = pendulum.now()
    adjust_minute = now.minute // adjust_minute * adjust_minute
    return now.replace(minute=adjust_minute).format(fomat)


def monday():
    now = datetime.now()
    weekday = now.weekday()
    days_to_monday = weekday
    monday_date = now - timedelta(days=days_to_monday)
    monday_start = monday_date.replace(hour=0, minute=0, second=0, microsecond=0)
    return int(monday_start.timestamp()) * 1000


def ago(**kwargs):
    return pendulum.now().subtract(**kwargs).timestamp()


def ago_ns(**kwargs):
    return ago(**kwargs) * 1000


def now_ns():
    return int(time.time() * 1000)


def morning_and_midnight_ts(n: int = 1) -> Tuple[int, int]:
    dt = pendulum.now('Asia/Shanghai').subtract(days=n)

    dt_start = dt.at(0)
    dt_end = dt.at(hour=23, minute=59, second=59, microsecond=999999)

    return (int(dt_start.timestamp()) * 1000, int(dt_end.timestamp()) * 1000)


def named_timestamp(name_a: str, name_b: str, delta: int = 1):
    """返回一个字典，包含两个键值对，键为 `name_a` 和 `name_b`，值为两个时间戳.

    :param:
        :name_a 键 a 的名称

        :name_b 键 b 的名称

        :delta(optional) 表示偏移天数，默认为 1.
    """
    return dict(zip([name_a, name_b], morning_and_midnight_ts(delta)))


def last_week_range():
    return weeks_before(1)


def weeks_before(n: int = 1) -> Tuple[int, int]:
    now = datetime.now()
    weekday = now.weekday()

    # 计算本周一的日期
    this_monday = now - timedelta(days=weekday)

    # 计算 n 周前的周一的日期
    monday_of_weeks_ago = this_monday - timedelta(days=7 * n)

    # 计算 n 周前的周日的日期
    sunday_of_week_agao = monday_of_weeks_ago + timedelta(days=6)

    weeks_monday_start = monday_of_weeks_ago.replace(
        hour=0,
        minute=0,
        second=0,
        microsecond=0,
    )
    # 将上周日的日期时间调整为23点59分59秒
    weeks_sunday_end = sunday_of_week_agao.replace(
        hour=23,
        minute=59,
        second=59,
        microsecond=999999,
    )

    return int(weeks_monday_start.timestamp()) * 1000, int(
        weeks_sunday_end.timestamp()
    ) * 1000


# TODO:
def time_range(delta: str):
    """parse time range expression to timestamp range.

    Examples:
        - '-24h' -> (24 hours ago, now)
        - '-1d' -> (1 day ago, now), same as '-24h'
        - '-1w' -> (last 7 days, now), same as '-7d'

        but you can shift end time by adding a comma:
        - '24h' -> (last 24 hours, end at 00:00 of today)
        - '1d' -> (last 1 day, end at 00:00 of today)
        - '1w' -> (last 7 days, end at 00:00 of monday this week)
    """
    if ',' in delta:
        start, end = delta.split(',')
        return time_range(start), time_range(end)

    if delta.endswith('h'):
        return int(ago(hours=int(delta[:-1]))), int(ago())
    if delta.endswith('d'):
        shift = int(delta[:-1]) * 24
        return time_range(f'{shift}h')
    if delta.endswith('w'):
        return weeks_before(int(delta[:-1]))
