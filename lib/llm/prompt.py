import os
from pathlib import Path
from typing import List, Literal, Tu<PERSON>, TypedDict

from langchain.prompts import Chat<PERSON>romptTemplate

from lib.constants import DATA_ROOT, PROMPT_ROOT


class Message(TypedDict):
    role: Literal['system', 'user', 'assistant']
    content: str


Messages = List[Message]


MessageTuple = Tuple[Literal['system', 'user', 'assistant'], str]
MessagesTuple = List[MessageTuple]


def get_content(filename: str, base_dir: Path) -> str:
    _, ext = os.path.splitext(filename)

    if not ext:
        possible_file_paths = [
            base_dir / f'{filename}{ext}'
            for ext in [
                '.txt',
                '.md',
                '.yaml',
                '.yml',
            ]
        ]

        for p in possible_file_paths:
            if p.exists():
                return p.read_text()
        return ''
    else:
        return (base_dir / filename).read_text()


def get_data_content(filename: str) -> str:
    return get_content(filename, DATA_ROOT)


def get_prompt_content(filename: str) -> str:
    return get_content(filename, PROMPT_ROOT)


def get_prompt(name: str):
    """Get prompt content from file"""
    return ChatPromptTemplate.from_template(get_prompt_content(name))


def get_prompt_from_messages(system_prompt_file: str, *messages):
    """Get prompt content from messages.

    Args:
        system_prompt_file: str, file name of system prompt
        *messages: list of messages.
    """
    return prompt_messages(
        [
            ('system', get_prompt_content(system_prompt_file)),
        ]
        + list(messages)
    )


def prompt_messages(*messages):
    return ChatPromptTemplate.from_messages(list(messages))
