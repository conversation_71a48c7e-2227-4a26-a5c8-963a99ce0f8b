from typing import Optional

from langchain.chat_models.base import BaseChatModel

from lib.utils.reasoner import split_think_content


def stream_invoke(
    llm: 'BaseChatModel', prompt: str, *, system_prompt: Optional[str] = None, verbose: bool = False
) -> str:
    message = [('human', prompt)]
    if system_prompt:
        message.insert(0, ('system', system_prompt))

    content = []

    # Use the message list instead of the hardcoded prompt
    for chunk in llm.stream(message):
        chunk_content = chunk.content
        content.append(chunk_content)

        if verbose:
            print(chunk_content, end='', flush=True)

            if 'reasoning_content' in chunk.response_metadata:
                print(chunk.response_metadata['reasoning_content'], end='', flush=True)

        model_name = chunk.response_metadata.get('model_name')
        if not model_name:
            model_name = chunk.response_metadata.get('model')
        if model_name:
            print(f'\n({model_name})', end='', flush=True)

    content = ''.join(content)
    result = split_think_content(content)
    return result.content


def invoke(llm, prompt: str) -> str:
    response = llm.invoke(prompt)
    content = response.content

    result = split_think_content(content)
    return result.content
