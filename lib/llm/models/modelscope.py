from langchain_community.embeddings import ModelScopeEmbeddings

from lib.configs.llm import LLMConfig


def modelscope_embedding(cfg: LLMConfig) -> ModelScopeEmbeddings:
    # embedding_model_kwargs = {
    #     # 'device': 'cuda:0',
    #     'device': 'cup',
    # }
    # embedding_encode_kwargs = {
    #     'batch_size': 32,
    #     'normalize_embeddings': True,
    #     'show_progress_bar': False,
    # }
    # return HuggingFaceEmbeddings(
    #     model=cfg.model,
    #     model_kwargs=embedding_model_kwargs,
    #     encode_kwargs=embedding_encode_kwargs,
    # )
    return ModelScopeEmbeddings(model_id=cfg.model)
