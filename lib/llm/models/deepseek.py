from typing import Union

import openai
from langchain_core.outputs import ChatResult
from langchain_deepseek import ChatDeepSeek
from langchain_groq import <PERSON>tGroq
from langchain_openai import OpenAI

from lib.configs.llm import LLMConfig


class DeepSeek(OpenAI):
    """deepseek v3 completetion model."""

    pass


class ParseChatGroq(ChatGroq):
    def _create_chat_result(
        self, response: Union[dict, openai.BaseModel]
    ) -> ChatResult:
        # cprint(f'defualt query: {self.default_query}', color='yellow')

        result = super()._create_chat_result(response)

        if not isinstance(response, openai.BaseModel):
            if hasattr(response.choices[0].message, 'reasoning'):
                reasoning = response.choices[0].message.reasoning
                # 跟标准推理内容的 key 对齐
                additional_args = result.generations[0].message.additional_kwargs
                additional_args['reasoning_content'] = reasoning

            return result

        return result


def deepseek_chat_llm(cfg: LLMConfig) -> ChatDeepSeek:
    kwargs = {
        'model': cfg.model,
        'api_key': cfg.api_key or '',
        'temperature': cfg.temperature,
        'max_tokens': cfg.max_token,
    }
    if cfg.base_url:
        kwargs['api_base'] = cfg.base_url

    return ChatDeepSeek(**kwargs)


def deepseek_llm(cfg: LLMConfig) -> DeepSeek:
    kwargs = {
        'model': cfg.model,
        'api_key': cfg.api_key or '',
        'temperature': cfg.temperature,
        'max_tokens': cfg.max_token,
    }
    if cfg.base_url:
        kwargs['api_base'] = cfg.base_url

    return DeepSeek(**kwargs)


def groq_deepseek_chat_llm(cfg: LLMConfig) -> ParseChatGroq:
    return ParseChatGroq(
        model=cfg.model,
        api_key=cfg.api_key or '',
        temperature=cfg.temperature,
        max_tokens=cfg.max_token,
        reasoning_format='parsed',
    )
