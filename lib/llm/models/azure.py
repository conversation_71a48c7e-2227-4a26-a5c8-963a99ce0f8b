from langchain_openai import (
    AzureChatOpenAI,
    AzureOpenAI,
    AzureOpenAIEmbeddings,
)
from pydantic import SecretStr

from lib.configs.llm import LLMConfig


def azure_chat_llm(cfg: LLMConfig) -> AzureChatOpenAI:
    kwargs = dict(
        api_key=SecretStr(cfg.api_key or ''),
        azure_endpoint=cfg.base_url,
        azure_deployment=cfg.deployment,
        api_version=cfg.api_version,
        temperature=cfg.temperature,
    )

    if cfg.max_completion_tokens is not None:
        kwargs.setdefault('max_completion_tokens', cfg.max_completion_tokens)
    elif cfg.max_token is not None:
        kwargs.setdefault('max_tokens', cfg.max_token)

    return AzureChatOpenAI(**kwargs)


def azure_llm(cfg: LLMConfig) -> AzureOpenAI:
    return AzureOpenAI(
        api_key=SecretStr(cfg.api_key or ''),
        azure_endpoint=cfg.base_url,
        azure_deployment=cfg.deployment,
        api_version=cfg.api_version,
        temperature=cfg.temperature,
        max_tokens=cfg.max_token,
        # 'top_p': config.llm.top_p,
    )


def azure_embedding(cfg: LLMConfig) -> AzureOpenAIEmbeddings:
    return AzureOpenAIEmbeddings(
        api_key=SecretStr(cfg.api_key or ''),
        azure_endpoint=cfg.base_url,
        azure_deployment=cfg.deployment,
        api_version=cfg.api_version,
        model=cfg.model,
    )
