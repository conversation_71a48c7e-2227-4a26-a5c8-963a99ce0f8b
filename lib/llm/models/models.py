from functools import wraps
from typing import Literal, Optional

from lib.configs import config
from lib.configs.llm import LLMConfig, ModelProvider, ModelType

from .azure import azure_chat_llm, azure_embedding, azure_llm
from .dashscope import dashscope_chat_llm, dashscope_embedding
from .deepseek import deepseek_chat_llm, deepseek_llm, groq_deepseek_chat_llm
from .modelscope import modelscope_embedding
from .ollama import chat_ollama_llm, ollama_embedding, ollama_llm
from .openai import openai_chat_llm, openai_embedding, openai_llm
from .vertexai import anthropic_chat_llm, vertexai_chat_llm, vertexai_llm


def bind_model(model_type: str = ModelType.Chat):
    """装饰器工厂函数，用于根据模型类型加载 LLM 配置。

    Args:
        model_type (str): 模型类型，默认为 ModelType.Chat。
        支持的模型类型定义在 ModelType 枚举中。

    Returns:
        function: 装饰器函数，返回一个包装函数。包装函数接受以下参数：
            - name (str): LLM 配置名称
            - *args: 可变位置参数
            - **kwargs: 可变关键字参数

        包装函数返回被装饰函数的返回值，如果找不到匹配的配置则返回 None。

    Example:
        ```
        @bind_model(ModelType.Chat)
        def get_llm(llm_config: LLMConfig):
            # 使用 llm_config 获取 LLM 实例
            pass
        ```
    """

    def decorator(func):
        @wraps(func)
        def wrapper(name: str, *args, **kwargs):
            llm_config = config.llms.get(name)
            if not llm_config or llm_config.model_type != model_type:
                return None
            return func(llm_config, *args, **kwargs)

        return wrapper

    return decorator


@bind_model(ModelType.Embedding)
def get_embedding(embedding_config: LLMConfig):
    """Get the embedding model based on the name and provider"""
    # embedding_config = config.llms.get(name)
    # if not embedding_config or embedding_config.model_type != ModelType.Embedding:
    #     return None

    provider = embedding_config.provider
    if provider == ModelProvider.AZURE:
        return azure_embedding(embedding_config)
    elif provider == ModelProvider.OPENAI:
        return openai_embedding(embedding_config)
    elif provider == ModelProvider.OLLAMA:
        return ollama_embedding(embedding_config)
    elif provider == ModelProvider.DASHSCOPE:
        return dashscope_embedding(embedding_config)
    elif provider == ModelProvider.MODELSCOPE:
        return modelscope_embedding(embedding_config)


@bind_model(ModelType.Chat)
def get_llm(
    llm_config: LLMConfig,
    completion_type: Literal['chat', 'text'] = 'chat',
    *,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
):
    provider = llm_config.provider

    if temperature is not None:
        llm_config.temperature = temperature
    else:
        llm_config.temperature = llm_config.temperature or 0.0

    # Chat Model
    if completion_type == 'chat':
        if max_tokens is not None:
            llm_config.max_token = max_tokens

        if provider == ModelProvider.AZURE:
            return azure_chat_llm(llm_config)

        elif provider == ModelProvider.DEEPSEEK:  # 新增 deepseek provider 支持
            return deepseek_chat_llm(llm_config)

        elif provider == ModelProvider.DASHSCOPE:
            return dashscope_chat_llm(llm_config)

        elif provider == ModelProvider.GROQ_DEEPSEEK:
            return groq_deepseek_chat_llm(llm_config)

        elif provider in (ModelProvider.OPENAI, ModelProvider.X):
            return openai_chat_llm(llm_config)

        elif provider == ModelProvider.VERTEXAI:
            return vertexai_chat_llm(llm_config)

        elif provider == ModelProvider.ANTHROPIC:
            return anthropic_chat_llm(llm_config)

        elif provider == ModelProvider.OLLAMA:
            return chat_ollama_llm(llm_config)

    # Text Model
    elif completion_type == 'text':
        llm_config.temperature = temperature
        if provider == ModelProvider.AZURE:
            return azure_llm(llm_config)

        elif provider == ModelProvider.DEEPSEEK:  # 新增 deepseek provider 支持
            return deepseek_llm(llm_config)

        elif provider in (ModelProvider.OPENAI, ModelProvider.X):
            return openai_llm(llm_config)

        elif provider == ModelProvider.VERTEXAI:
            return vertexai_llm(llm_config)

        elif provider == ModelProvider.OLLAMA:
            return ollama_llm(llm_config)
