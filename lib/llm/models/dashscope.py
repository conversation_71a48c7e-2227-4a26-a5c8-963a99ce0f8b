from langchain_community.chat_models.tongyi import Chat<PERSON><PERSON><PERSON>
from langchain_community.embeddings import DashScopeEmbeddings

from lib.configs.llm import LLMConfig


def dashscope_embedding(cfg: LLMConfig) -> DashScopeEmbeddings:
    """Dashscope embedding.

    ref this (langchain)[`https://python.langchain.com/docs/integrations/text_embedding/dashscope/`]
    """
    return DashScopeEmbeddings(
        dashscope_api_key=cfg.api_key,
        model=cfg.model,
    )


def dashscope_chat_llm(cfg: LLMConfig) -> ChatTongyi:
    return ChatTongyi(
        dashscope_api_key=cfg.api_key,
        model=cfg.model,
    )
