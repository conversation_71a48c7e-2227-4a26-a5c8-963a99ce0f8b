from langchain_openai import ChatOpenAI, OpenAI, OpenAIEmbeddings
from pydantic import SecretStr

from lib.configs.llm import LLMConfig


def openai_chat_llm(cfg: LLMConfig) -> ChatOpenAI:
    params = {
        'model': cfg.model or '',
        'base_url': cfg.base_url,
        'api_key': SecretStr(cfg.api_key or ''),
        'temperature': cfg.temperature,
    }

    if cfg.enable_thinking is not None:
        params['extra_body'] = {'enable_thinking': cfg.enable_thinking}

    return ChatOpenAI(**params)


def openai_llm(cfg: LLMConfig) -> OpenAI:
    return OpenAI(
        model=cfg.model or '',
        base_url=cfg.base_url,
        api_key=SecretStr(cfg.api_key or ''),
        temperature=cfg.temperature,
        # FIXME: o1-preview 不支持，应该是 max_completion_tokens
        max_tokens=cfg.max_token,
    )


def openai_embedding(cfg: LLMConfig) -> OpenAIEmbeddings:
    return OpenAIEmbeddings(
        model=cfg.model or '',
        base_url=cfg.base_url,
        api_key=SecretStr(cfg.api_key or ''),
    )
