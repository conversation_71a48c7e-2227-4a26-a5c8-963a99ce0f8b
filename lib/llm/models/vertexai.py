import google.auth
from langchain_google_vertexai import ChatVertexAI, VertexAI
from langchain_google_vertexai.model_garden import ChatAnthropicVertex

from lib.configs.llm import LLMConfig

vertexai_scopes = ['https://www.googleapis.com/auth/cloud-platform']


def _vertexai_kwargs(cfg: LLMConfig):
    credentials, project_id = google.auth.load_credentials_from_file(
        cfg.credentials_json_path, scopes=vertexai_scopes
    )

    kwargs = dict(
        model=cfg.model,
        project=project_id,
        temperature=cfg.temperature,
        credentials=credentials,
    )

    if cfg.vertex_location:
        kwargs.setdefault('location', cfg.vertex_location)

    if cfg.max_token is not None:
        kwargs.setdefault('max_tokens', cfg.max_token)

    return kwargs


def vertexai_chat_llm(cfg: LLMConfig) -> ChatVertexAI:
    return ChatVertexAI(**_vertexai_kwargs(cfg))


def vertexai_llm(cfg: LLMConfig) -> VertexAI:
    return VertexAI(**_vertexai_kwargs(cfg))


def anthropic_chat_llm(cfg: LLMConfig) -> ChatAnthropicVertex:
    return ChatAnthropicVertex(**_vertexai_kwargs(cfg))
