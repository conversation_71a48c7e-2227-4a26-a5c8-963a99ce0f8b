from langchain_ollama import ChatOllama, OllamaEmbeddings, OllamaLLM

from lib.configs.llm import LLMConfig


def chat_ollama_llm(cfg: LLMConfig) -> ChatOllama:
    return ChatOllama(
        model=cfg.model or '',
        base_url=cfg.base_url or None,
        temperature=cfg.temperature,
        extract_reasoning=True,
    )


def ollama_llm(cfg: LLMConfig) -> OllamaLLM:
    return OllamaLLM(
        model=cfg.model or '',
        base_url=cfg.base_url or None,
        temperature=cfg.temperature,
    )


def ollama_embedding(cfg: LLMConfig) -> OllamaEmbeddings:
    return OllamaEmbeddings(
        model=cfg.model or '',
        base_url=cfg.base_url or None,
    )
