from typing import Optional

from lib.configs import config


def load_mcp_tools_schema(wrapped: bool = False, *, names: Optional[list[str]] = None) -> dict:
    """Load MCP tools schema with optional wrapping.

    Args:
        wrapped (bool): If True, wrap the schemas in a dictionary with key `mcpServers`.
        names (Optional[list[str]]): List of specific MCP tool names to load. If None, load all tools.

    Returns:
        dict: Dictionary containing the MCP tool schemas.
    """
    schemas = {}

    for name, x in config.mcp.items():
        if not names or name in names:
            schemas[name] = x.model_dump()

    if wrapped:
        schemas = {'mcpServers': schemas}

    print('MCP tools schemas:', schemas)
    return schemas
