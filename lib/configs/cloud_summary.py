from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class CloudSummaryNotifyConfig(BaseSettings):
    """总结后的信息应该发送到的群组"""

    group: str = Field(description='Notify group name', default='')
    chat_id: str = Field(description='Notify group chat id')


class CloudNotificationGroup(BaseSettings):
    """接收公有云消息的飞书群"""

    chat_id: str = Field(description='group chat id')
    kind: Optional[str] = Field(default=None, description='group kind')
