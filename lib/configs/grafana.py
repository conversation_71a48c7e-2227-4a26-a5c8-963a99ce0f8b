from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class GrafanaViewConfig(BaseSettings):
    name: Optional[str] = Field(default=None, description='view name')
    view_id: str = Field(..., description='id of view')


class GrafanaConfig(BaseSettings):
    name: str = Field(description='alias name')
    datasource: str = Field(description='grafana datasource')
    base_url: str = Field(
        description='base url of grafana',
        default='https://oma-monitor.lilithgame.com',
    )
    dashboard_id: str = Field(description='id of grafana dashboard')
    # views: Mapping[str, GrafanaViewConfig] = Field(
    #     default_factory=dict, alias='views', description='views of dashboard'
    # )
    views: List[GrafanaViewConfig] = Field(
        default_factory=list, description='views of dashboard'
    )
