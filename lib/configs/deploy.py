from typing import List

from pydantic import EmailStr, Field
from pydantic_settings import BaseSettings


class DeployConfig(BaseSettings):
    debug: bool = Field(description='Enable debug mode', default=False)
    verbose: bool = Field(description='Enable verbose mode', default=False)
    dry_run: bool = Field(
        description='Enable dry run mode, do not execute any action', default=False
    )

    admins: List[EmailStr] = Field(description='Admin emails', default_factory=list)
