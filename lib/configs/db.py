from pydantic import Field
from pydantic_settings import BaseSettings


class MongoConfig(BaseSettings):
    # url: str = Field(description='database_uri', default='sqlite:///db.sqlite')
    mgo_uri: str = Field(description='Mongo uri', default='mongodb://localhost:27017')
    mgo_db: str = Field(description='Mongo db', default='aiops')
    cloud_notification_collection: str = Field(
        description='Mongo collection for cloud notifications',
        default='cloud_notifications',
    )
