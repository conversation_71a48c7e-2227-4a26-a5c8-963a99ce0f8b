from typing import Mapping, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class BitableConfig(BaseSettings):
    name: str
    table_id: str


class LarkAppConfig(BaseSettings):
    """Configuration for the Language Model."""

    # name: Optional[str] = None
    app_id: str
    app_secret: str
    verification_token: str | None = None
    encrypt_key: str = ''


class AIMessageCard(BaseSettings):
    template_id: str
    footer: str = '本内容由AI生成，如有问题请联系管理员。'


class ServiceAccountConfig(BaseSettings):
    """Configuration for the user access token management."""

    app_name: str
    user_token_storage: Optional[str] = Field(
        default='redis://localhost:6379/4', description='user_access_token 存储的redis 地址'
    )
    redis_token_key: str = Field(
        default='lark_user_token', description='user_access_token 存储的redis key'
    )
    weekly_wiki_report_group: str = Field(description='周报 wiki 群组 uid')


class LarkConfig(BaseSettings):
    service_account: ServiceAccountConfig
    apps: Mapping[str, LarkAppConfig]
    bitable_name: Optional[str] = '巡检条目'
    bitables: Mapping[str, str]
    card: Optional[AIMessageCard] = None
