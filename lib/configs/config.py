import os
from typing import List, Mapping, Optional

from pydantic import Field
from pydantic_settings import SettingsConfigDict, TomlConfigSettingsSource, YamlConfigSettingsSource

from lib.constants import CONFIG_ROOT, PROJECT_ROOT

from .cloud_summary import CloudNotificationGroup, CloudSummaryNotifyConfig
from .db import MongoConfig
from .deploy import DeployConfig
from .dify import DifyConfig
from .github import GithubConfig
from .grafana import GrafanaConfig
from .lark import LarkConfig
from .llm import LLMConfig
from .mail import EmailConfig
from .mcp import MCPSSEConfig, MCPStdioConfig
from .oma import OmaConfig
from .rootcause import RootCauseConfig
from .search import SearchEngineConfig


class Config(DeployConfig):
    """Config for all."""

    oma: Optional[OmaConfig] = None

    mongo: MongoConfig

    lark: LarkConfig

    llms: Mapping[str, LLMConfig] = Field(
        default_factory=dict,
        description='Large or Small Language models',
    )

    mcp: Mapping[str, MCPStdioConfig | MCPSSEConfig] = Field(..., description='mcp servers')

    dify: Optional[DifyConfig] = None

    # search engine
    search: Optional[SearchEngineConfig] = None

    # github copilot
    github: Optional[GithubConfig] = None

    email: Optional[EmailConfig] = None

    grafanas: Mapping[str, GrafanaConfig] = Field(
        default_factory=dict, description='Grafana dashboards'
    )

    rootcause: RootCauseConfig = Field(description='Root cause analysis')

    cloud_notification_groups: List[CloudNotificationGroup] = Field(
        description='Groups that receive cloud notifications'
    )

    cloud_summary_notify: List[CloudSummaryNotifyConfig] = Field(
        description='Cloud summary notify groups'
    )

    limit_pdf_max_size: str = Field(
        default='50M', description='PDF file max size limit, e.g. 50M, 100M'
    )

    model_config = SettingsConfigDict(
        yaml_file=os.getenv(
            'CONFIG_PATH',
            (
                CONFIG_ROOT / 'config.yaml',
                PROJECT_ROOT / 'config.yaml',
            ),
        ),
        toml_file=os.getenv(
            'CONFIG_PATH',
            (
                CONFIG_ROOT / 'config.toml',
                PROJECT_ROOT / 'config.toml',
            ),
        ),
        extra='ignore',
        frozen=True,
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        return (
            YamlConfigSettingsSource(settings_cls),
            TomlConfigSettingsSource(settings_cls),
        )
