from typing import Literal, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings

from lib.constants import PROJECT_ROOT


class MCPConfig(BaseSettings):
    """Configuration for MCP (Model Context Protocol) servers."""

    transport: Literal['stdio', 'sse'] = Field(
        default='stdio', description='Transport method between MCP server and client'
    )
    env: Optional[dict[str, str]] = Field(
        default_factory=dict, description='Environment variables for the MCP server'
    )


class MCPStdioConfig(MCPConfig):
    """Configuration for MCP servers using stdio transport."""

    command: str = Field(default='uvx', description='Command to execute MCP server')
    args: Optional[list[str]] = Field(
        default=None, description='Arguments to pass to the MCP server command'
    )

    @field_validator('args', mode='after')
    @classmethod
    def replace_project_root_placeholder(cls, args: list[str]) -> list[str]:
        """replace PROJECT_ROOT with the actual path in args."""
        if args is None:
            return []
        return [arg.replace('{PROJECT_ROOT}', PROJECT_ROOT.as_posix()) for arg in args]


class MCPSSEConfig(MCPConfig):
    """Configuration for MCP servers using SSE transport."""

    url: str = Field(description='URL of the MCP server')
