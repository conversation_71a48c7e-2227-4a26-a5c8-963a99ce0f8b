from dataclasses import dataclass
from enum import Enum
from typing import Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings


@dataclass
class ModelType:
    Chat = 'chat'
    Embedding = 'embedding'


class ModelProvider(Enum):
    AZURE = 'azure'
    DEEPSEEK = 'deepseek'
    GROQ_DEEPSEEK = 'groq-deepseek'
    OPENAI = 'openai'
    OLLAMA = 'ollama'
    HUGGINGFACE = 'huggingface'
    DASHSCOPE = 'dashscope'  # 阿里云百炼
    MODELSCOPE = 'modelscope'  # 魔塔社区
    X = 'xai'
    VERTEXAI = 'vertexai'  # Google VertexAI
    ANTHROPIC = 'anthropic'  # VertexAI Anthropic

    def __missing__(self, _):
        return ModelProvider.AZURE


class LLMConfig(BaseSettings):
    """Configuration for the Language Model."""

    model_type: str = ModelType.Chat

    provider: ModelProvider = ModelProvider.OPENAI
    model: Optional[str] = None

    # vertextai
    credentials_json_path: Optional[str] = None
    vertex_location: Optional[str] = None

    api_key: Optional[str] = None
    api_version: Optional[str] = None
    base_url: Optional[str] = None
    deployment: str = ''

    max_token: Optional[int] = None
    max_completion_tokens: Optional[int] = None

    temperature: float = 0.0
    top_p: float = 1.0
    top_k: int = 0
    stream: bool = False

    # Qwen3
    enable_thinking: Optional[bool] = None

    @field_validator('base_url', mode='before')
    @classmethod
    def validate_base_url(cls, v, info):
        """根据 provider 设置 base_url 的默认值"""
        # 如果 base_url 已经被显式设置，则不修改
        if v is not None:
            return v

        # 获取 provider 值
        provider = info.data.get('provider')

        # 如果 provider 是 ollama，则 base_url 默认为 None
        if provider == ModelProvider.OLLAMA:
            return None

        # 其他情况使用默认的 base_url
        return 'https://llm-proxy.lilith.com/v1'
