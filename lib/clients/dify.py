from dataclasses import dataclass, field
from typing import List, Literal, Optional

import httpx

from .auth.bear import BearAuth

STREAMING_MODE = Literal['blocking', 'streaming']


class DifyClient:
    def __init__(self, api_key, base_url: str = 'https://api.dify.ai/v1', timeout: int = 10):
        self.api_key = api_key
        self.base_url = base_url
        self.client = httpx.Client(
            auth=BearAuth(api_key),
            base_url=base_url,
            timeout=timeout,
        )

    def _send_request(self, method, endpoint, json=None, params=None, data=None):
        return self.client.request(
            method,
            endpoint,
            json=json,
            params=params,
            data=data,
        )

    def _send_request_with_files(self, method, endpoint, data, files):
        # url = f'{self.base_url}{endpoint}'
        url = endpoint
        response = self.client.request(method, url, data=data, files=files)

        return response

    def message_feedback(self, message_id, rating, user):
        payload = {'rating': rating, 'user': user}
        return self._send_request('POST', f'/messages/{message_id}/feedbacks', json=payload)

    def get_application_parameters(self, user):
        params = {'user': user}
        return self._send_request('GET', '/parameters', params=params)

    def file_upload(self, user, files):
        data = {'user': user}
        return self._send_request_with_files('POST', '/files/upload', data=data, files=files)

    def text_to_audio(self, text: str, user: str, streaming: bool = False):
        data = {'text': text, 'user': user, 'streaming': streaming}
        return self._send_request('POST', '/text-to-audio', data=data)

    def get_meta(self, user):
        params = {'user': user}
        return self._send_request('GET', '/meta', params=params)


class CompletionClient(DifyClient):
    def create_completion_message(self, inputs, response_mode: STREAMING_MODE, user, files=None):
        data = {
            'inputs': inputs,
            'response_mode': response_mode,
            'user': user,
            'files': files,
        }
        return self._send_request(
            'POST',
            '/completion-messages',
            data,
            # stream=True if response_mode == 'streaming' else False,
        )


class ChatClient(DifyClient):
    def create_chat_message(
        self,
        query,
        user,
        inputs: dict = {},
        response_mode: STREAMING_MODE = 'blocking',
        conversation_id=None,
        files=None,
    ):
        data = {
            'inputs': inputs,
            'query': query,
            'user': user,
            'response_mode': response_mode,
            'files': files,
        }
        if conversation_id:
            data['conversation_id'] = conversation_id

        # return self._send_request('POST', '/chat-messages', json=data)
        if response_mode == 'streaming':
            response = self.client.stream('POST', '/chat-messages', json=data)
            return response
        return self._send_request('POST', '/chat-messages', json=data)

    def get_suggested(self, message_id, user: str):
        params = {'user': user}
        return self._send_request('GET', f'/messages/{message_id}/suggested', params=params)

    def stop_message(self, task_id, user):
        data = {'user': user}
        return self._send_request('POST', f'/chat-messages/{task_id}/stop', data)

    def get_conversations(self, user, last_id=None, limit=None, pinned=None):
        params = {'user': user, 'last_id': last_id, 'limit': limit, 'pinned': pinned}
        return self._send_request('GET', '/conversations', params=params)

    def get_conversation_messages(self, user, conversation_id=None, first_id=None, limit=None):
        params = {'user': user}

        if conversation_id:
            params['conversation_id'] = conversation_id
        if first_id:
            params['first_id'] = first_id
        if limit:
            params['limit'] = limit

        return self._send_request('GET', '/messages', params=params)

    def rename_conversation(self, conversation_id, name, auto_generate: bool, user: str):
        data = {'name': name, 'auto_generate': auto_generate, 'user': user}
        return self._send_request('POST', f'/conversations/{conversation_id}/name', data)

    def delete_conversation(self, conversation_id, user):
        data = {'user': user}
        return self._send_request('DELETE', f'/conversations/{conversation_id}', data)

    def audio_to_text(self, audio_file, user):
        data = {'user': user}
        files = {'audio_file': audio_file}
        return self._send_request_with_files('POST', '/audio-to-text', data, files)


@dataclass(kw_only=True)
class FileObject:
    # file_type: Literal['image', 'audio', 'video', 'document'] = 'image'
    file_type: Literal['image', 'audio', 'video', 'document'] = field(
        default='image', metadata={'description': 'file type', 'alias': 'type'}
    )
    transform_method: Literal['remote_url', 'local_file'] = 'remote_url'
    url: str = ''
    upload_file_id: str = ''


class WorkflowClient(DifyClient):
    def run(
        self,
        inputs: dict,
        response_mode: str = 'streaming',
        user: str = 'abc-123',
        # files: Optional[List[str]] = None,
        files: Optional[List[FileObject]] = None,
    ):
        data = {'inputs': inputs, 'response_mode': response_mode, 'user': user}
        if files:
            data['files'] = [f.__dict__ for f in files]
        return self._send_request('POST', '/workflows/run', data)

    def stop(self, task_id, user):
        data = {'user': user}
        return self._send_request('POST', f'/workflows/tasks/{task_id}/stop', data)
