from .base import EndpointMixin


class Node(EndpointMixin):
    def list_node(self, **params):
        ok, response = self._get('cmdb/k8s_node', params=params)

        if not ok:
            return []

        return response.json()

    def get_node(self, instance_id: str):
        content = self.list_node(instance_id=instance_id, limit=1)
        if not content:
            return None

        if content['results']:
            return content['results'][0]
