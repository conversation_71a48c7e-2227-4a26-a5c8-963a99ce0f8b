from typing import Protocol

import httpx


class EndpointMixin(Protocol):
    """Base mixin for all endpoints"""

    client: httpx.Client

    def _request(self, method, endpoint, **kwargs):
        auto_slash = kwargs.pop('auto_slash', True)
        if auto_slash:
            endpoint = endpoint.strip('/') + '/'

        response = self.client.request(method, endpoint, **kwargs)
        return response.is_success, response

    def _get(self, url, **kwargs):
        return self._request('GET', url, **kwargs)
