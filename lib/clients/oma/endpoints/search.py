from .base import EndpointMixin


class Search(EndpointMixin):
    def search(self, **params):
        ok, response = self._get('cmdb/multiple-search', params=params)
        if not ok:
            return None

        return response.json()

    def search_one(self, query: str):
        content = self.search(query=query, limit=1)
        if not content:
            return None
        return content[0]
