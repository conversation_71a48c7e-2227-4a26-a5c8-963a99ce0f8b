from .base import EndpointMixin


class Machine(EndpointMixin):
    def list_machines(self, **params):
        ok, response = self._get('cmdb/machine', params=params)
        if not ok:
            print(f'ok: {ok}, response: {response}')
            return []
        return response.json()

    def get_machine_by(self, **params):
        params.setdefault('limit', 1)

        content = self.list_machines(**params)
        if not content:
            return None

        if content['results']:
            return content['results'][0]

    def get_machine(self, instance_id: str):
        content = self.get_machine_by(instance_id=instance_id)
        if not content:
            return None

        if content['results']:
            return content['results'][0]
