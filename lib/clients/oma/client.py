import httpx

from lib.clients.auth.bear import Bear<PERSON><PERSON>
from lib.configs.oma import OmaConfig

from .endpoints import Machine, Node, Search
from .user_agent import USER_AGENT


class OmaClient(Machine, Node, Search):
    def __init__(
        self, token: str, base_url: str = 'https://oma-api.lilithgame.com/api/'
    ):
        self.token = token
        self.base_url = base_url

        self.client = httpx.Client(
            auth=BearAuth(token, 'Token'),
            headers={'User-Agent': USER_AGENT},
            base_url=base_url,
        )

    @classmethod
    def new(cls, token: str, base_url: str):
        return cls(token=token, base_url=base_url)

    @classmethod
    def from_config(cls, cfg: OmaConfig):
        return cls.new(token=cfg.token, base_url=cfg.base_url)
