"""MongoDB 客户端封装类

该模块提供了一个简单易用的 MongoDB 客户端封装，使 MongoDB 操作更加便捷。
"""

from typing import Any, Dict, List, Optional

from pymongo import MongoClient as PyMongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.read_preferences import ReadPreference

from lib.configs import config
from lib.configs.db import MongoConfig


class MongoClient:
    """MongoDB 客户端封装类

    简化 MongoDB 操作的客户端封装。

    示例:
        >>> client = MongoClient()
        >>> client.insert_one("collection_name", {"key": "value"})
        >>> docs = client.find("collection_name", {"key": "value"})
    """

    def __init__(
        self,
        uri: Optional[str] = None,
        db_name: Optional[str] = None,
        read_preference: ReadPreference = ReadPreference.PRIMARY,
    ) -> None:
        """初始化 MongoDB 客户端

        Args:
            uri: MongoDB 连接 URI，如果为 None 则使用配置文件中的默认值
            db_name: 数据库名称，如果为 None 则使用配置文件中的默认值
            read_preference: 读取偏好设置，默认为 PRIMARY
        """
        self.uri = uri or config.mongo.mgo_uri
        self.db_name = db_name or config.mongo.mgo_db
        self._client = PyMongoClient(self.uri)
        self.db: Database = self._client.get_database(self.db_name, read_preference=read_preference)

    @classmethod
    def from_config(cls, cfg: MongoConfig) -> 'MongoClient':
        """从配置对象创建 MongoClient 实例

        Args:
            cfg: MongoConfig 对象

        Returns:
            MongoClient: MongoClient 实例
        """
        return cls(uri=cfg.mgo_uri, db_name=cfg.mgo_db)

    def __enter__(self) -> 'MongoClient':
        return self

    def close(self) -> None:
        """显式关闭 MongoDB 连接"""
        if hasattr(self, '_client'):
            self._client.close()
            delattr(self, '_client')

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器退出时关闭连接"""
        self.close()

    def get_collection(self, collection_name: str) -> Collection:
        """获取指定的集合

        Args:
            collection_name: 集合名称

        Returns:
            Collection: MongoDB 集合对象
        """
        return self.db[collection_name]

    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> str:
        """插入单个文档

        Args:
            collection_name: 集合名称
            document: 要插入的文档

        Returns:
            str: 插入文档的 ID
        """
        result = self.get_collection(collection_name).insert_one(document)
        return str(result.inserted_id)

    def insert_many(
        self, collection_name: str, documents: List[Dict[str, Any]], ordered: bool = True
    ) -> List[str]:
        """插入多个文档

        Args:
            collection_name: 集合名称
            documents: 要插入的文档列表
            ordered: 是否按顺序插入，默认为 True

        Returns:
            List[str]: 插入文档的 ID 列表
        """
        result = self.get_collection(collection_name).insert_many(documents, ordered=ordered)
        return [str(id_) for id_ in result.inserted_ids]

    def find_one(
        self,
        collection_name: str,
        filter_dict: Dict[str, Any],
        projection: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """查找单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 指定返回的字段，None 表示返回所有字段

        Returns:
            Optional[Dict[str, Any]]: 查询到的文档，如果没有找到则返回 None
        """
        return self.get_collection(collection_name).find_one(filter_dict, projection)

    def find(
        self,
        collection_name: str,
        filter_dict: Dict[str, Any],
        projection: Optional[Dict[str, Any]] = None,
        sort: Optional[List[tuple]] = None,
        limit: int = 0,
        skip: int = 0,
    ) -> List[Dict[str, Any]]:
        """查找多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 指定返回的字段，None 表示返回所有字段
            sort: 排序条件，如 [("field", 1)] 表示升序，[("field", -1)] 表示降序
            limit: 限制返回的文档数量，0 表示不限制
            skip: 跳过的文档数量

        Returns:
            List[Dict[str, Any]]: 查询到的文档列表
        """
        cursor = self.get_collection(collection_name).find(filter_dict, projection)
        if sort:
            cursor = cursor.sort(sort)
        if skip:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)
        return list(cursor)

    def update_one(
        self,
        collection_name: str,
        *,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False,
    ) -> int:
        """更新单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新操作
            upsert: 如果文档不存在是否插入，默认为 False

        Returns:
            int: 更新的文档数量
        """
        result = self.get_collection(collection_name).update_one(
            filter_dict, update_dict, upsert=upsert
        )
        return result.modified_count

    def update_many(
        self,
        collection_name: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False,
    ) -> int:
        """更新多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新操作
            upsert: 如果文档不存在是否插入，默认为 False

        Returns:
            int: 更新的文档数量
        """
        result = self.get_collection(collection_name).update_many(
            filter_dict, update_dict, upsert=upsert
        )
        return result.modified_count

    def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """删除单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            int: 删除的文档数量
        """
        result = self.get_collection(collection_name).delete_one(filter_dict)
        return result.deleted_count

    def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """删除多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            int: 删除的文档数量
        """
        result = self.get_collection(collection_name).delete_many(filter_dict)
        return result.deleted_count

    def count_documents(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """统计文档数量

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            int: 符合条件的文档数量
        """
        return self.get_collection(collection_name).count_documents(filter_dict)

    def aggregate(
        self, collection_name: str, pipeline: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """执行聚合操作

        Args:
            collection_name: 集合名称
            pipeline: 聚合管道

        Returns:
            List[Dict[str, Any]]: 聚合结果
        """
        return list(self.get_collection(collection_name).aggregate(pipeline))
