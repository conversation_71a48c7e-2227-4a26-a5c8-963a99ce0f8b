from typing import Callable, Literal

from imap_tools import MailBox
from imap_tools.message import MailMessage
from typing_extensions import Self

from lib.configs.mail import EmailConfig

MailHandler = Callable[[MailMessage], bool]


Criteria = Literal['ALL', 'NOT SEEN', 'SEEN', 'RECENT']


class EmailClient(MailBox):
    @classmethod
    def from_config(cls, cfg: EmailConfig) -> Self:
        self = cls(cfg.imap_server, cfg.imap_port)
        self.login(cfg.username, cfg.password)
        return self

    def fetch_mails(
        self,
        handler: MailHandler,
        per_page: int = 10,
        folder: str = 'Inbox',
        criteria: Criteria = 'ALL',
    ):
        self.folder.set(folder)

        found_nums = self.numbers(criteria)

        pages = (
            int(len(found_nums) // per_page) + 1
            if len(found_nums) % per_page
            else int(len(found_nums) // per_page)
        )

        for page in range(pages):
            page_limit = slice(page * per_page, page * per_page + per_page)

            messages = self.fetch(
                criteria=criteria,
                bulk=True,
                limit=page_limit,
                reverse=True,
            )
            for msg in messages:
                if not handler(msg):
                    return
