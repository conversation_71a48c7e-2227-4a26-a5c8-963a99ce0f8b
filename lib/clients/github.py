import itertools
import os
from typing import Any, Dict, List, Optional

import github
import httpx
from dotenv import load_dotenv
from github.AuthenticatedUser import AuthenticatedUser
from github.NamedUser import NamedUser
from github.Team import Team

from lib.configs.github import GithubConfig
from log import logger

load_dotenv()


class GithubClient:
    api_endpoint: str = 'https://api.github.com'
    api_version: str = '2022-11-28'
    accept_header: str = 'application/vnd.github+json'

    def __init__(self, token: Optional[str] = None, org_name: str = 'CopilotDemo'):
        self.access_token = token or os.getenv('GH_TOKEN')
        self.org_name = org_name or os.getenv('GH_ORG')

        self.gh = github.Github(self.access_token)
        self.org = self.gh.get_organization(org_name)

        self._teams: Dict[str, Team] = {}

    @classmethod
    def from_config(cls, cfg: GithubConfig):
        return cls(cfg.access_token, cfg.org)

    def get_auth_header(self):
        headers = {
            'Accept': self.accept_header,
            'X-GitHub-Api-Version': self.api_version,
            'Authorization': f'Bearer {self.access_token}',
        }
        return headers

    def list_seats(self, per_page: int = 100, max_page: Optional[int] = None):
        """List all Copilot seat assignments for an organization"""
        url = f'{self.api_endpoint}/orgs/{self.org_name}/copilot/billing/seats'

        headers = self.get_auth_header()

        for page in itertools.count(1):
            response = httpx.get(url, headers=headers, params={'page': page, 'per_page': per_page})
            if not response.is_success:
                break

            data = response.json()
            logger.debug(
                'found {}/{} github copilot seats in total on page {page}',
                len(data['seats']),
                data['total_seats'],
                page=page,
            )
            for item in data['seats']:
                yield item

            if len(data['seats']) < per_page:
                return

            if max_page and page >= max_page:
                return

    def add_seats(self, users: List[NamedUser]):
        url = f'{self.api_endpoint}/orgs/{self.org_name}/copilot/billing/selected_users'

        payload = {'selected_usernames': [user.login for user in users]}
        headers = self.get_auth_header()

        response = httpx.post(url, headers=headers, json=payload)
        if response.is_success:
            return True, response.json()
        return False, response.text

    def cancel_seats(self, usernames: List[str]):
        url = f'{self.api_endpoint}/orgs/{self.org_name}/copilot/billing/selected_users'

        payload = {'selected_usernames': usernames}
        headers = self.get_auth_header()

        response = httpx.request('DELETE', url=url, headers=headers, json=payload)
        return response.is_success

    def list_teams(self):
        if not self._teams:
            teams = self.org.get_teams()
            for team in teams:
                if team.name not in self._teams:
                    self._teams[team.name] = team
        return self._teams.values()

    def find_team_by_name(self, team_name: str):
        for team in self.list_teams():
            if team.name == team_name:
                return team

    def invite_user(
        self,
        user: NamedUser,
        team_name: Optional[str] = None,
        default_team: Optional[str] = None,
        ignore_team_not_exists: bool = False,
    ):
        """invite user to org or team(optional).

        Args:
            user: NamedUser, github user.
            team_name: str, team name.
            default_team: str, default team name.
            ignore_team_not_exists: bool, if true,
                still invite to org event team not exists, otherwise fail.
        """
        kwargs: dict[str, Any] = {'user': user}

        if team_name:
            team = self.find_team_by_name(team_name)
            if team:
                kwargs['teams'] = [team]
            elif default_team:
                team = self.find_team_by_name(default_team)
                if not team and not ignore_team_not_exists:
                    raise ValueError(f'team {team_name} and {default_team} not exists')
                kwargs['teams'] = [team]
            elif not ignore_team_not_exists:
                raise ValueError(f'team {team_name} not exists')

        self.org.invite_user(**kwargs)

    def invite_user_old(self, gh_user_or_email: str, team_name: Optional[str] = None):
        user = self.search_user(gh_user_or_email)

        if not user:
            # TODO: invite exists user, than can be bind to feishu user later.
            if '@' in gh_user_or_email:
                return self.invite_user_by_email(gh_user_or_email, team_name)
            return None

        kwargs: dict[str, Any] = {'user': user}

        if team_name:
            team = self.org.get_team_by_slug(team_name)
            if team:
                kwargs['team'] = team

        self.org.invite_user(**kwargs)

    def invite_user_by_email(self, user_email: str, team_name: Optional[str] = None):
        kwargs: dict[str, Any] = {'email': user_email}

        if team_name:
            team = self.org.get_team_by_slug(team_name)
            if team:
                kwargs['teams'] = [team]

        self.org.invite_user(**kwargs)

    def search_user(self, gh_user: str):
        """search user by login or email."""
        users = self.gh.search_users(gh_user)
        if users.totalCount > 0:
            return users.get_page(0)[0]

    def get_user(self, user_id: int):
        try:
            return self.gh.get_user_by_id(user_id)
        except github.GithubException as e:
            logger.error(f'user id with {user_id}: {e}')
            return None

    def remove_membership(self, user: int | str):
        """remove user from org membership.

        Args:
            user: int|str, user id or login.
        """
        try:
            gh_user: Optional[NamedUser | AuthenticatedUser] = None
            if isinstance(user, int):
                gh_user = self.gh.get_user_by_id(user)
            elif isinstance(user, str):
                gh_user = self.gh.get_user(user)

            assert gh_user, f'user {user} not exists'

            if isinstance(gh_user, NamedUser):
                self.org.remove_from_membership(gh_user)
                return True

            return False
        except github.GithubException:
            return False


if __name__ == '__main__':
    from lib.configs import config

    client = GithubClient(config.github.access_token, config.github.org)

    for seat in client.list_seats(per_page=5, max_page=1):
        print(seat)

    # for team in client.list_teams():
    #     print(team.name, team.slug)

    # t = client.org.get_team_by_slug('研发中台')
    # print(t)

    # invite user
    # client.invite_user_by_email('<EMAIL>', team_name='Plat')
    # client.invite_user_to_team('why2go', 'Plat')

    # user = client.gh.get_user('<EMAIL>')
    # user = client.search_user('<EMAIL>')
    # user = client.search_user('<EMAIL>')
    # user = client.search_user('<EMAIL>')
    # user = client.search_user('<EMAIL>')
    # print(user)
    # client.org.remove_from_membership(user)

    # client.invite_user('<EMAIL>')
    # client.invite_user('<EMAIL>')

    # user = client.search_user('<EMAIL>')
    # user = client.search_user('bunnyxiang')
    # user = client.search_user('unknwon')
    # user = client.get_user(178152469)
    # if user:
    #     debug(user)
    #     debug('user.login:', user.login)
    #     print('user.id', user.id)
    #     print(user.avatar_url)
    #     print(user.email)
    #     print('team count:', user.team_count)

    # is_member = client.org.has_in_members(user)
    # debug(is_member)

    # ship = user.get_organization_membership(client.org)
    # debug(ship)

    # find memeber
    # gh_user_id = 'akayj'
    # gh_user = client.find_user(gh_user_id)
    # print(f'find user: {gh_user.login}, email: {gh_user.email}')
    # user = client.search_user_globally('<EMAIL>')
    # if user:
    #     print(f'find user: {user.login}, email: {user.email}')
    # client.list_org_invitations()
