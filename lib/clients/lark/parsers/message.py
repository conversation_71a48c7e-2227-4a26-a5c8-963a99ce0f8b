import orjson as json
from lark_oapi.api.im.v1.model.message import Message


def extract_message(msg: Message) -> str:
    """Parse interactive message into text.

    current support:
    - text message
    - interactive message
    - richtext message
    """
    msg_type = msg.msg_type
    msg_content = json.loads(msg.body.content)

    if msg_type == 'text':
        return extract_plaintext_message(msg_content)
    elif msg_type == 'interactive':
        return extract_interactive_message_text(msg_content)
    elif msg_type == 'post':
        return extract_richtext(msg_content)
    return msg_content


def extract_plaintext_message(content: dict) -> str:
    """Parse plain text message body into text."""
    return content['text']


def extract_interactive_message_text(content: dict) -> str:
    """Parse interactive message body into text.

    Ref: https://open.feishu.cn/document/server-docs/im-v1/message-content-description/message_content#3ea4c2d5
    """
    print('raw content:', content)
    title = f'[{content["title"]}]'

    lines = [title]

    for elems in content['elements']:
        line_content = []
        for elem in elems:
            tag = elem['tag']
            if tag == 'text':
                line_content.append(elem['text'])
            elif tag == 'a':
                line_content.append(elem['text'])
        if line_content:
            lines.append(' '.join(line_content))

    return '\n'.join(lines)


def extract_richtext(content: dict) -> str:
    """Parse richtext message body into text."""
    lines = []

    title = f'{content["title"]}'
    if title:
        lines.append(f'[{title}]')

    for line in content['content']:
        line_content = ''
        for elem in line:
            tag = elem['tag']
            if tag == 'text':
                line_content += elem['text']
        lines.append(line_content)

    return '\n'.join(lines)
