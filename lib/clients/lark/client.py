import json
from typing import List

import lark_oapi as lark
import redis
from lark_oapi.api.drive.v1 import BatchGetTmpDownloadUrlMediaRequest
from typing_extensions import Self

from lib.configs.lark import LarkAppConfig

from .bitable import Bitable
from .file import FileMixins
from .folder import Folder
from .message import MessageMixins
from .mixin import request_option_builder
from .oauth import OAuthMixin
from .user import UserMixin
from .wiki import WikiMixin


def none_request_option_builder():
    return None


class LarkClient(UserMixin, MessageMixins, FileMixins, OAuthMixin, WikiMixin):
    """Lark Client"""

    def __init__(
        self,
        client: lark.Client,
        *,
        option_builder: request_option_builder = none_request_option_builder,
    ):
        self._client = client
        self._option_builder = option_builder

    @classmethod
    def from_config(cls, cfg: LarkAppConfig) -> Self:
        client = lark.Client().builder().app_id(cfg.app_id).app_secret(cfg.app_secret).build()
        return cls(client)

    @classmethod
    def new(cls, app_id: str, app_secret: str) -> Self:
        client = lark.Client().builder().app_id(app_id).app_secret(app_secret).build()
        return cls(client)

    def bitable_app(self, bitable_app_token: str) -> Bitable:
        return Bitable(self._client, bitable_app_token)

    def folder(self, folder_token: str) -> Folder:
        return Folder(self._client, folder_token)

    def get_tmp_urls(self, file_tokens: List[str]):
        """batch get tmp download urls"""
        req = BatchGetTmpDownloadUrlMediaRequest.builder().file_tokens(file_tokens).build()
        resp = self._client.drive.v1.media.batch_get_tmp_download_url(req)
        if not resp.success():
            print(
                f'batch get tmp download urls failed for {file_tokens}, '
                f'code: {resp.code}, error: {resp.msg}'
            )
            return {}

        urls = resp.data.tmp_download_urls
        r = {x.file_token: x.tmp_download_url for x in urls}
        return r

    def send_image_message(self, image_path: str, user_email: str):
        """发送图片消息给用户通过飞书"""
        # 上传图片并获取 image_key
        image_key = self.upload_image(image_path)

        # 发送图片消息
        return self.send_message(
            user_email,
            content=json.dumps({'image_key': image_key}),
            message_type='image',
            receive_id_type='email',
        )


class UserAuthenClientBuilder:
    @classmethod
    def build_user_client(cls, user_token: str) -> LarkClient:
        """Build a Lark client by user token."""
        client = lark.Client().builder().enable_set_token(True).build()

        # Only build once
        option = lark.RequestOption().builder().user_access_token(user_token).build()

        def option_builder() -> lark.RequestOption:
            return option

        return LarkClient(client, option_builder=option_builder)

    @classmethod
    def build_user_client_from_redis(cls, redis_addr: str) -> LarkClient:
        """Build a Lark client by user token."""
        client = lark.Client().builder().enable_set_token(True).build()

        def option_builder() -> lark.RequestOption:
            redis_client = redis.from_url(redis_addr)

            user_token = redis_client.get('lark_user_token')
            if not user_token:
                raise ValueError('user token not found in redis')

            return lark.RequestOption().builder().user_access_token(user_token).build()

        return LarkClient(client, option_builder=option_builder)
