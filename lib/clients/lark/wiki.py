from lark_oapi.api.wiki.v2 import (
    CopySpaceNodeRequest,
    CopySpaceNodeRequestBody,
    CopySpaceNodeResponse,
    GetNodeSpaceRequest,
    GetNodeSpaceResponse,
    ListSpaceNodeRequest,
    ListSpaceNodeRequestBuilder,
    ListSpaceNodeResponse,
)

from .mixin import ClientMixin


class WikiMixin(ClientMixin):
    def get_wiki_node(self, wiki_token: str):
        # 构造请求对象
        request: GetNodeSpaceRequest = (
            GetNodeSpaceRequest.builder().token(wiki_token).obj_type('wiki').build()
        )

        # 发起请求
        response: GetNodeSpaceResponse = self._client.wiki.v2.space.get_node(
            request, option=self._option_builder()
        )

        if not response.success():
            return False, f'获取wiki失败: {response.code}, {response.msg}'

        return True, response.data.node

    def get_space_child_nodes(self, space_id: str, parent_node_token: str):
        # 构造请求对象
        builder: ListSpaceNodeRequestBuilder = (
            ListSpaceNodeRequest.builder()
            .space_id(space_id)
            .parent_node_token(parent_node_token)
            .page_size(50)
        )

        while True:
            request: ListSpaceNodeRequest = builder.build()

            # 发起请求
            response: ListSpaceNodeResponse = self._client.wiki.v2.space_node.list(
                request, option=self._option_builder()
            )

            if not response.success():
                print(f'list child wiki失败: {response.code}, {response.msg}')
                return

            for node in response.data.items:
                yield node

            if not response.data.has_more:
                break

            page_token = response.data.page_token
            if page_token:
                builder = builder.page_token(page_token)

    def copy_node(
        self,
        src_space_id: str,
        src_node_token: str,
        target_space_id: str,
        target_parent_token: str,
        title: str,
    ):
        # 构造请求对象
        request: CopySpaceNodeRequest = (
            CopySpaceNodeRequest.builder()
            .space_id(src_space_id)
            .node_token(src_node_token)
            .request_body(
                CopySpaceNodeRequestBody.builder()
                .target_parent_token(target_parent_token)
                .target_space_id(target_space_id)
                .title(title)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CopySpaceNodeResponse = self._client.wiki.v2.space_node.copy(
            request, self._option_builder()
        )
        if not response.success():
            return False, f'复制wiki失败: {response.code}, {response.msg}'

        return True, response.data.node
