import os
from pathlib import Path
from typing import IO, Any, List, Literal, Mapping, Optional

import lark_oapi as lark
from lark_oapi.api.bitable.v1 import (
    AppTableCreateHeader,
    AppTableFieldProperty,
    AppTableFieldPropertyOption,
    AppTableRecord,
    AppTableView,
    BatchCreateAppTableRecordRequest,
    BatchCreateAppTableRecordRequestBody,
    BatchUpdateAppTableRecordRequest,
    BatchUpdateAppTableRecordRequestBody,
    BatchUpdateAppTableRecordResponse,
    Condition,
    CopyAppRequest,
    CopyAppRequestBody,
    CreateAppTableRecordRequest,
    CreateAppTableRequest,
    CreateAppTableRequestBody,
    CreateAppTableResponse,
    DeleteAppTableRecordRequest,
    DeleteAppTableRequest,
    FilterInfo,
    GetAppRequest,
    GetAppResponse,
    ListAppTableFieldRequest,
    ListAppTableRequest,
    ListAppTableResponse,
    ListAppTableViewRequest,
    ReqTable,
    SearchAppTableRecordRequest,
    SearchAppTableRecordRequestBody,
    UpdateAppTableRecordRequest,
)
from lark_oapi.api.drive.v1 import (
    UploadAllMediaRequest,
    UploadAllMediaRequestBody,
)

from log import logger

from .parser import FieldType, ParserFunction, parse

Conjunction = Literal['and', 'or']


class Bitable(object):
    def __init__(self, client: lark.Client, app_token: str):
        self.lark = client
        self.app_token = app_token

        self._metadata = self._get_metadata()

        self._tables_cache = None

    def _get_metadata(self):
        # 构造请求对象
        request: GetAppRequest = GetAppRequest.builder().app_token(self.app_token).build()

        # 发起请求
        response: GetAppResponse = self.lark.bitable.v1.app.get(request)
        if not response.success():
            return {}

        meta = response.data.app
        return {
            'name': meta.name,
            'revision': meta.revision,
            'formula_type': meta.formula_type,
            'time_zone': meta.time_zone,
        }

    @property
    def name(self):
        return self._metadata.get('name')

    @property
    def revision(self):
        return self._metadata.get('revision')

    @property
    def formula_type(self):
        return self._metadata.get('formula_type')

    def copy_table(
        self,
        source_table_id: str,
        new_name: str,
        with_data: bool = False,
        with_conds: Optional[List[dict[str, Any]]] = None,
        conjunction: Conjunction = 'and',
    ):
        """copy table with `source_table_id` to `new_name`.

        :param source_table_id: source table id
        :param new_name: new table name
        :param with_data(optional): copy data or not
        :param with_conds(optional): available when `with_data` is True,
            e.g.
                {
                    'field_name': '数据来源',
                    'operator': 'isNotEmpty',
                    'value': []
                }
            https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-record/record-filter-guide
        """
        source_table_fields = self.fields(table_id=source_table_id)

        fields = []
        fields_and_types = {}
        for source_field in source_table_fields:
            # skip formula and button fields
            if source_field.ui_type.lower() in ['formula', 'button']:
                continue

            header = (
                AppTableCreateHeader.builder()
                .field_name(source_field.field_name)
                .type(source_field.type)
                .ui_type(source_field.ui_type)
            )

            # property
            if source_field.property:
                property_builder = AppTableFieldProperty.builder()

                if source_field.property.options:
                    options = [
                        AppTableFieldPropertyOption.builder().color(x.color).name(x.name).build()
                        for x in source_field.property.options
                    ]
                    property_builder.options(options)

                if source_field.ui_type.lower() == 'datetime':
                    property_builder.date_formatter(source_field.property.date_formatter)

                header.property(property_builder.build())

            # append fields' types
            fields_and_types[source_field.field_name] = source_field.ui_type.lower()  # noqa

            # append header field
            fields.append(header.build())

        resp: CreateAppTableResponse = self.create_table(new_name, fields)
        if not resp.success():
            print(f'create table failed: {resp.msg}')
            return None, None

        new_table_id = resp.data.table_id

        if with_data:
            source_views = self.views(source_table_id, 'grid')
            if not source_views:
                raise Exception('source table has no grid view')

            source_view = source_views[0]
            self.copy_table_records(
                source_table_id=source_table_id,
                source_view_id=source_view.view_id,
                to_table_id=new_table_id,
                fields_types=fields_and_types,
                conds=with_conds,
                conjunction=conjunction,
            )
            print('copy table records done')

        target_views = self.views(new_table_id, 'grid')
        if not target_views:
            return new_table_id, None
        return new_table_id, target_views[0].view_id

    def copy_table_records(
        self,
        source_table_id: str,
        source_view_id: str,
        to_table_id: str,
        fields_types: dict[str, Any],
        conds: Optional[List[dict[str, Any]]] = None,
        conjunction: Conjunction = 'and',
    ):
        """copy table records from source_table_id to to_table_id."""
        source_records = self.iter_records(
            table_id=source_table_id,
            view_id=source_view_id,
            conds=conds,
            conjunction=conjunction,
        )

        parsed_records = []
        for records in source_records:
            for record in records:
                fields_to_create = {}
                for k, v in record.fields.items():
                    ftype = fields_types.get(k, '')
                    parsed_field = parse(ftype, v)
                    fields_to_create.update({k: parsed_field})

                parsed_records.append(fields_to_create)

        self.batch_create_records(table_id=to_table_id, records=parsed_records)

    def copy_file(
        self,
        to_folder_token: str,
        name: str,
        without_content: bool = False,
    ) -> bool:
        """copy bitable file"""
        request: CopyAppRequest = (
            CopyAppRequest.builder()
            .app_token(self.app_token)
            .request_body(
                CopyAppRequestBody.builder()
                .name(name)
                .folder_token(to_folder_token)
                .without_content(without_content)
                .time_zone('Asia/Shanghai')
                .build()
            )
            .build()
        )
        response = self.lark.bitable.v1.app.copy(request)
        # return response
        if not response.success():
            return False

        if response.code:
            return False

        return True

    def upload_image(self, img: IO, parent_node: Optional[str] = None):
        if parent_node is None:
            parent_node = self.app_token

        img_name = os.path.basename(img.name)
        img_size = img.seek(0, os.SEEK_END)
        img.seek(0)

        request = (
            UploadAllMediaRequest.builder()
            .request_body(
                UploadAllMediaRequestBody.builder()
                .file_name(img_name)
                .parent_type('bitable_image')
                .parent_node(parent_node)
                .size(img_size)
                .file(img)
                .build()
            )
            .build()
        )
        return self.lark.drive.v1.media.upload_all(request)

    def upload_local_image(self, img_path: str | Path):
        with open(img_path, 'rb') as img:
            response = self.upload_image(img)
            if not response.success():
                lark.logger.error(
                    'client.bitable.v1.media.upload_all failed, '
                    f'code: {response.code}, msg: {response.msg}, '
                    f'log_id: {response.get_log_id()}'
                )
                raise Exception(f'upload image {img.name} failed')

            return response.data.file_token

    def tables(self, force: bool = False):
        """list all tables in the bitable app"""
        if self._tables_cache is not None and not force:
            return self._tables_cache

        # 发起请求
        response: ListAppTableResponse = self.lark.bitable.v1.app_table.list(
            request=ListAppTableRequest.builder().app_token(self.app_token).build(),
        )

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f'client.bitable.v1.app_table.list failed, '
                f'code: {response.code}, msg: {response.msg}, '
                f'log_id: {response.get_log_id()}'
            )
            return None

        self._tables_cache = response.data.items
        return self._tables_cache

    def get_table(self, name: Optional[str] = None):
        """get first table.

        :param name: table name, if not provided, use `LARK_BITABLE_TABLE_NAME`
            env or `巡检条目` as default .
        """
        table_name = name or os.getenv('LARK_BITABLE_TABLE_NAME')

        tables = self.tables()

        if tables:
            for table in tables:
                if table_name and table.name == table_name:
                    return table
            return tables[0]

    def create_table(self, name: str, headers: List[AppTableCreateHeader]):
        req_body = (
            CreateAppTableRequestBody.builder()
            .table(ReqTable.builder().name(name).fields(headers).build())
            .build()
        )
        request: CreateAppTableRequest = (
            CreateAppTableRequest.builder().app_token(self.app_token).request_body(req_body).build()
        )
        response = self.lark.bitable.v1.app_table.create(request)
        return response

    def delete_table(self, table_id: str):
        request: DeleteAppTableRequest = (
            DeleteAppTableRequest.builder().app_token(self.app_token).table_id(table_id).build()
        )
        return self.lark.bitable.v1.app_table.delete(request)

    def create_record(self, table_id: str, record: dict[str, Any]):
        """create record"""
        request = (
            CreateAppTableRecordRequest.builder()
            .app_token(self.app_token)
            .table_id(table_id)
            .request_body(AppTableRecord.builder().fields(record).build())
            .build()
        )
        # AppTableRecord
        response = self.lark.bitable.v1.app_table_record.create(request)
        if not response.success():
            logger.error(
                f'client.bitable.v1.app_table_record.create failed,'
                f'code: {response.code}, '
                f'msg: {response.msg}, '
                f'log_id: {response.get_log_id()}, '
                f'record: {record}',
            )
            return None
        return response.data.record

    def batch_create_records(
        self,
        table_id: str,
        records: List[dict[str, Any]],
    ):
        """batch create records"""
        request = (
            BatchCreateAppTableRecordRequest.builder()
            .app_token(self.app_token)
            .table_id(table_id)
            .request_body(
                BatchCreateAppTableRecordRequestBody.builder()
                .records([AppTableRecord.builder().fields(record).build() for record in records])
                .build()
            )
            .build()
        )

        # AppTableRecord
        response = self.lark.bitable.v1.app_table_record.batch_create(request)
        if not response.success():
            logger.error(
                f'client.bitable.v1.app_table_record.batch_create failed,'
                f'code: {response.code}, '
                f'msg: {response.msg}, '
                f'log_id: {response.get_log_id()}',
            )
            return None
        return response.data.records

    def batch_update_records(
        self,
        table_id: str,
        records: List[dict[str, Any]],
    ):
        record_list = []
        for record in records:
            record_id = record.pop('record_id')
            record_list.append(
                AppTableRecord.builder().fields(record['fields']).record_id(record_id).build()
            )

        request = (
            BatchUpdateAppTableRecordRequest.builder()
            .app_token(self.app_token)
            .table_id(table_id)
            .request_body(
                BatchUpdateAppTableRecordRequestBody.builder().records(record_list).build()
            )
            .build()
        )

        # 发起请求
        response: BatchUpdateAppTableRecordResponse = (
            self.lark.bitable.v1.app_table_record.batch_update(request)
        )

        # 处理失败返回
        return response

    def update_record(
        self,
        table_id: str,
        record_id: str,
        new_fields: dict[str, Any],
    ):
        """update record fields"""
        request = (
            UpdateAppTableRecordRequest.builder()
            .app_token(self.app_token)
            .table_id(table_id)
            .record_id(record_id)
            .request_body(AppTableRecord.builder().fields(new_fields).build())
            .build()
        )
        response = self.lark.bitable.v1.app_table_record.update(request)
        return response

    def remove_record(self, table_id: str, record_id: str):
        """remove record."""
        request = (
            DeleteAppTableRecordRequest.builder()
            .app_token(self.app_token)
            .table_id(table_id)
            .record_id(record_id)
            .build()
        )
        return self.lark.bitable.v1.app_table_record.delete(request)

    def records(
        self,
        table_id: str,
        view_id: str,
        page_size: int = 20,
        conds: Optional[List[dict[str, Any]]] = None,
        conjunction: Conjunction = 'and',
    ):
        """return a generator of records."""
        records_gen = self.iter_records(
            table_id=table_id,
            view_id=view_id,
            page_size=page_size,
            conds=conds,
            conjunction=conjunction,
        )
        for records in records_gen:
            for record in records:
                yield record

    def iter_records(
        self,
        table_id: str,
        view_id: str,
        page_size: int = 20,
        conds: Optional[List[dict[str, Any]]] = None,
        conjunction: Conjunction = 'and',
    ):
        """iterate records.

        Please use :self:`records` instead.
        """
        conditions = []
        if conds:
            conditions.extend([Condition(d) for d in conds])

        page_token = ''
        while 1:
            filters = FilterInfo.builder().conjunction(conjunction).conditions(conditions).build()
            request: SearchAppTableRecordRequest = (
                SearchAppTableRecordRequest.builder()
                .app_token(self.app_token)
                .table_id(table_id)
                .page_size(page_size)
                .page_token(page_token)
                .request_body(
                    SearchAppTableRecordRequestBody.builder()
                    .view_id(view_id)
                    .filter(filters)
                    .build()
                )
                .build()
            )

            response = self.lark.bitable.v1.app_table_record.search(request)
            if not response.success():
                lark.logger.error(
                    f'v1.search failed, code: {response.code}, '
                    f' page_token: {page_token}, '
                    f'msg: {response.msg}, log_id: {response.get_log_id()},'
                    f'view_id: {view_id}'
                )
                return

            records = response.data.items
            if not records:
                break

            yield records

            page_token = response.data.page_token
            if not page_token:
                break

    def get_record(
        self,
        table_id: str,
        view_id: str,
        conds: List[dict[str, Any]],
        conjunction: Conjunction = 'and',
    ):
        records_gen = self.iter_records(table_id, view_id, conds=conds, conjunction=conjunction)
        records_list = list(records_gen)
        for records in records_list:
            for record in records:
                return record

    def views(self, table_id: str, view_type: Optional[str] = None) -> Optional[List[AppTableView]]:
        # 构造请求对象
        request: ListAppTableViewRequest = (
            ListAppTableViewRequest.builder().app_token(self.app_token).table_id(table_id).build()
        )

        # 发起请求
        response = self.lark.bitable.v1.app_table_view.list(request)
        if not response.success():
            return None

        if view_type is None:
            return response.data.items

        items = filter(lambda i: i.view_type == view_type, response.data.items)
        return list(items)

    def fields(self, table_id: str):
        request = (
            ListAppTableFieldRequest.builder().app_token(self.app_token).table_id(table_id).build()
        )
        response = self.lark.bitable.v1.app_table_field.list(request)
        if not response.success():
            lark.logger.error(
                f'client.bitable.v1.app_table_field.list failed, '
                f'code: {response.code}, '
                f'msg: {response.msg}, '
                f'log_id: {response.get_log_id()}'
            )
            return None
        return response.data.items

    def get_view_column(
        self,
        table_id: str,
        view_id: str,
        column_name: str,
        func: Optional[ParserFunction | FieldType] = None,
        conds=None,
        conjunction='and',
    ):
        """Get column from view, only support user type.

        Args:
            func (Optional[ParserFunction|str]): parser function or name.
        """
        source_records = self.iter_records(
            table_id=table_id,
            view_id=view_id,
            conds=conds,
            conjunction=conjunction,
        )

        values = set()
        for records in source_records:
            for record in records:
                for col, v in record.fields.items():
                    if col == column_name:
                        if func:
                            if isinstance(func, str):
                                values.update(parse(func, v))
                            else:
                                values.update(func(v))

        return values

    def get_view_columns(
        self,
        table_id: str,
        view_id: str,
        columns: List[str],
        func_map: Optional[Mapping[str, ParserFunction | FieldType]] = None,
        conds=None,
        conjunction='and',
    ):
        """Get columns from view.

        Args:
            columns (List[str]):
                columns to get.

            func_map (Optional[Mapping[str, ParserFunction|str]]):
                map of column and parser function.
        """
        source_records = self.iter_records(
            table_id=table_id,
            view_id=view_id,
            conds=conds,
            conjunction=conjunction,
        )

        rows = []
        for records in source_records:
            for record in records:
                row = {}
                for col, v in record.fields.items():
                    if col in columns:
                        func = func_map.get(col) if func_map else None
                        if func:
                            if isinstance(func, str):
                                row.update({col: parse(func, v)})
                            else:
                                row.update({col: func(v)})
                        else:
                            row.update({col: v})
                rows.append(row)

        return rows
