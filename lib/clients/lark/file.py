import lark_oapi
from lark_oapi.api.im.v1 import (
    CreateImageRequest,
    CreateImageRequestBody,
    CreateImageResponse,
)

from .mixin import ClientMixin


class UploadError(Exception):
    """Upload Error"""

    pass


class Image(ClientMixin):
    def upload_image(self, img_path: str):
        """Upload image, raise `UploadError` if operation failed."""
        # 构造请求对象
        with open(img_path, 'rb') as img:
            request: CreateImageRequest = (
                CreateImageRequest.builder()
                .request_body(
                    CreateImageRequestBody.builder().image_type('message').image(img).build()
                )
                .build()
            )

            # 发起请求
            response: CreateImageResponse = self._client.im.v1.image.create(request)
            if not response.success():
                lark_oapi.logger.error(
                    f'client.im.v1.message.get failed, code: {response.code}, '
                    f'msg: {response.msg}, log_id: {response.get_log_id()},'
                )
                # return False, response.error
                raise UploadError(response.error)

            if response.code != 0:
                raise UploadError(response.msg)

            if not response.data or not response.data.image_key:
                raise UploadError('response data is empty')

            return response.data.image_key


class FileMixins(
    Image,
):
    """File related Mixins."""
