from enum import Enum
from operator import attrgetter
from typing import Dict, List, Literal

import lark_oapi
from lark_oapi.api.contact.v3 import (
    BatchGetIdUserRequest,
    BatchGetIdUserRequestBody,
    BatchGetIdUserResponse,
    GetUserRequest,
    GetUserResponse,
    UserContactInfo,
)

from .mixin import ClientMixin


class UserIdType(Enum):
    OPEN_ID = 'open_id'
    USER_ID = 'user_id'
    UNION_ID = 'union_id'


class UserMixin(ClientMixin):
    """Batch user operations Mixin."""

    def get_user(
        self,
        user_id: str,
        user_id_type: UserIdType = UserIdType.USER_ID,
        department_id_type: str = 'open_department_id',
    ):
        # 构造请求对象
        request: GetUserRequest = (
            GetUserRequest.builder()
            .user_id(user_id)
            .user_id_type(user_id_type.value)
            .department_id_type(department_id_type)
            .build()
        )

        # 发起请求
        response: GetUserResponse = self._client.contact.v3.user.get(request)

        # 处理失败返回
        if not response.success():
            lark_oapi.logger.error(
                f'client.contact.v3.user.get failed, '
                f'code: {response.code}, '
                f'msg: {response.msg}, log_id: {response.get_log_id()}'
            )
            return None

        return response.data.user

    def get_user_id(
        self,
        user_email_or_mobile: str,
        users_type: Literal['email', 'mobile'] = 'email',
        user_id_type: UserIdType = UserIdType.OPEN_ID,
    ) -> str | None:
        """获取单个用户的 ID.

        Args:
            user_email_or_mobile (str): 用户邮箱或手机号.
            users_type (Literal['email', 'mobile']): 用户类型.
            user_id_type (UserIdType): 返回的用户 ID 类型.

        Returns:
            str | None: 用户 ID，如果未找到则返回 None.

        Raises:
            ValueError: 当 users_type 参数不支持时抛出.
            RuntimeError: 当 API 调用失败时抛出.
        """
        user_contacts = self.batch_get_users_contact(
            [user_email_or_mobile], users_type, user_id_type
        )

        user_contact = user_contacts.get(user_email_or_mobile)
        if user_contact:
            return user_contact.user_id
        return None

    def get_user_ids(
        self,
        users: List[str],
        users_type: Literal['email', 'mobile'] = 'email',
        user_id_type: UserIdType = UserIdType.OPEN_ID,
    ) -> List[str]:
        """获取批量用户 ID.

        Args:
            users (List[str]): 用户邮箱或手机号列表.
            users_type (Literal['email', 'mobile']): 用户类型.
            user_id_type (UserIdType): 返回的用户 ID 类型.

        Returns:
            List[str]: 用户 ID 列表.
        """
        user_contacts = self.batch_get_users_contact(users, users_type, user_id_type)

        open_id_getter = attrgetter('user_id')
        user_ids = []
        for user_contact in user_contacts.values():
            if open_id_getter(user_contact):
                user_ids.append(user_contact.user_id)
        return user_ids

    def batch_get_users_contact(
        self,
        emals_or_mobiles: List[str],
        users_type: Literal['email', 'mobile'] = 'email',
        user_id_type: UserIdType = UserIdType.OPEN_ID,
    ) -> Dict[str, UserContactInfo]:
        """批量获取用户联系信息.

        Args:
            emals_or_mobiles (List[str]): 邮箱或手机号列表.
            users_type (Literal['email', 'mobile']): 用户类型，邮箱或手机号.
            user_id_type (UserIdType): 返回的用户 ID 类型.

        Returns:
            Dict[str, UserContactInfo]: 用户联系信息字典，键为邮箱或手机号.

        Raises:
            ValueError: 当 users_type 参数不支持时抛出.
            RuntimeError: 当 API 调用失败时抛出.
        """
        body_builder = BatchGetIdUserRequestBody.builder()
        if users_type == 'email':
            body_builder = body_builder.emails(emals_or_mobiles)
        elif users_type == 'mobile':
            body_builder = body_builder.mobiles(emals_or_mobiles)
        else:
            error_msg = f'unsupported users_type: {users_type}'
            lark_oapi.logger.error(error_msg)
            raise ValueError(error_msg)

        request: BatchGetIdUserRequest = (
            BatchGetIdUserRequest.builder()
            .user_id_type(user_id_type.value)
            .request_body(body_builder.build())
            .build()
        )

        # 发起请求
        response: BatchGetIdUserResponse = self._client.contact.v3.user.batch_get_id(request)

        # 处理失败返回
        if not response.success():
            error_msg = (
                f'client.contact.v3.user.batch_get_id failed, code: {response.code}, '
                f'msg: {response.msg}, log_id: {response.get_log_id()}'
            )
            lark_oapi.logger.error(error_msg)
            raise RuntimeError(f'{response.code}: {response.msg}')

        response_users = response.data.user_list
        if not response_users:
            # 返回空字典而不是抛出异常，因为没有找到用户是正常情况
            return {}

        results: Dict[str, UserContactInfo] = {}
        get_attr = attrgetter(users_type)

        for user in response_users:
            results[get_attr(user)] = user
        return results
