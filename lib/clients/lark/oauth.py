import json
from typing import List, Optional

import lark_oapi

from .mixin import ClientMixin


class OAuthError(Exception):
    """OAuth 操作异常基类."""

    def __init__(self, message: str, code: Optional[str] = None):
        """
        初始化 OAuth 异常.

        Args:
            message (str): 错误消息
            code (Optional[str]): 错误代码
        """
        super().__init__(message)
        self.message = message
        self.code = code


class TokenRefreshError(OAuthError):
    """Token 刷新异常."""

    pass


class AuthorizationError(OAuthError):
    """授权异常."""

    pass


class OAuthMixin(ClientMixin):
    """OAuth Mixin for Lark API."""

    TOKEN_URI = '/open-apis/authen/v2/oauth/token'

    @classmethod
    def refresh_user_access_token(
        cls, client_id: str, client_secret: str, refresh_token: str
    ) -> dict:
        """
        刷新用户访问令牌.

        Args:
            client_id (str): 客户端ID
            client_secret (str): 客户端密钥
            refresh_token (str): 刷新令牌

        Returns:
            dict: 包含新访问令牌的响应数据

        Raises:
            TokenRefreshError: 当令牌刷新失败时抛出
        """
        payload = {
            'grant_type': 'refresh_token',
            'client_id': client_id,
            'client_secret': client_secret,
            'refresh_token': refresh_token,
        }

        request: lark_oapi.BaseRequest = (
            lark_oapi.BaseRequest.builder()
            .http_method(lark_oapi.HttpMethod.POST)
            .headers({'Content-Type': 'application/json; charset=UTF-8'})
            .uri(cls.TOKEN_URI)
            .body(payload)
            .build()
        )

        client = lark_oapi.Client().builder().build()
        response: lark_oapi.BaseResponse = client.request(request)

        response_body: dict = json.loads(response.raw.content)
        if response_body['code'] != 0:
            error_msg = f'error: {response_body["error"]}, {response_body["error_description"]}'
            raise TokenRefreshError(error_msg, response_body.get('error'))

        return response_body

    @classmethod
    def get_user_access_token(
        cls,
        code: str,
        app_id: str,
        app_secret: str,
        *,
        scopes: Optional[List[str]] = None,
        redirect_uri: str = 'https://eagle.lilithgame.com/api/oauth',
    ) -> dict:
        """
        使用授权码获取用户访问令牌.

        Args:
            code (str): 授权码
            app_id (str): 应用ID
            app_secret (str): 应用密钥
            scopes (Optional[List[str]], optional): 访问令牌的权限范围.
                默认为 None.
            redirect_uri (str): 重定向URI

        Returns:
            dict: 包含访问令牌和其他信息的字典

        Raises:
            AuthorizationError: 当授权失败时抛出
        """
        # 构造请求对象
        request_body = {
            'grant_type': 'authorization_code',
            'code': code,
            'client_id': app_id,
            'client_secret': app_secret,
            'redirect_uri': redirect_uri,
        }
        if scopes:
            request_body['scope'] = ' '.join(scopes)

        request: lark_oapi.BaseRequest = (
            lark_oapi.BaseRequest.builder()
            .http_method(lark_oapi.HttpMethod.POST)
            .headers({'Content-Type': 'application/json; charset=UTF-8'})
            .uri(cls.TOKEN_URI)
            .body(request_body)
            .build()
        )

        # 发起请求
        client = lark_oapi.Client().builder().build()
        response: lark_oapi.BaseResponse = client.request(request)

        # 处理失败返回
        if not response.success():
            error_msg = (
                f'client.oauth.v1.access_token.get failed, '
                f'code: {response.code}, '
                f'msg: {response.msg}, error: {response.error}, log_id: {response.get_log_id()}'
            )
            lark_oapi.logger.error(error_msg)
            raise AuthorizationError(response.msg or 'Unknown error', str(response.code))

        # 处理成功返回
        lark_oapi.logger.info(str(response.raw.content, lark_oapi.UTF_8))

        response_body: dict = json.loads(response.raw.content)
        if response_body['code'] != 0:
            error_msg = f'error: {response_body["error"]}, {response_body["error_description"]}'
            raise AuthorizationError(error_msg, response_body.get('error'))

        return response_body
