import json
from enum import Enum
from typing import List, Literal, Optional

import lark_oapi
from lark_oapi.api.im.v1 import (
    CreateMessageRequest,
    CreateMessageRequestBody,
    CreateMessageResponse,
    GetChatRequest,
    GetChatResponse,
    GetMessageRequest,
    GetMessageResponse,
    ListChatRequest,
    ListChatResponse,
    ListMessageRequest,
    ListMessageResponse,
    PatchMessageRequest,
    PatchMessageRequestBody,
    PatchMessageResponse,
    ReplyMessageRequest,
    ReplyMessageRequestBody,
    ReplyMessageResponse,
    SearchChatRequest,
    SearchChatResponse,
)

from .mixin import ClientMixin


class BatchMessageReceivedIdType(Enum):
    OPEN_ID = 'open_ids'
    USER_ID = 'user_ids'
    DEPARTMENT_ID = 'department_ids'
    UNION_ID = 'union_ids'


class BatchMessageMixin(ClientMixin):
    """Batch Message Mixin."""

    def batch_send_message(
        self,
        receivers: List[str],
        content: str,
        title: str = '',
        subtitle: str = '',
        header_color: str = 'blue',
        tags: List[str] | None = None,
        receive_id_type=BatchMessageReceivedIdType.OPEN_ID,
    ):
        """批量发送消息.

        每次接受者上限 200,
        参考: https://open.feishu.cn/document/server-docs/im-v1/batch_message/send-messages-in-batches
        """
        if len(receivers) > 200 or len(receivers) == 0:
            raise ValueError('receivers must provided, and count less than 200')

        card_message = {
            'elements': [
                {'tag': 'div', 'text': {'content': content, 'tag': 'lark_md'}},
            ],
            'header': {
                'template': header_color,
                'title': {'content': title, 'tag': 'plain_text'},
            },
        }

        # subtitle
        if subtitle:
            card_message['header']['subtitle'] = {  # type: ignore
                'content': subtitle,
                'tag': 'plain_text',
            }

        if tags and len(tags) > 0:
            text_tag_list = []
            for tag in tags:
                tag_pair = tag.split(':')
                if len(tag_pair) > 0:
                    tag_content = tag_pair[0]
                    if len(tag_pair) > 1:
                        tag_color = tag_pair[1]
                    else:
                        tag_color = 'neutral'

                    text_tag_list.append(
                        {
                            'tag': 'text_tag',
                            'text': {'tag': 'plain_text', 'content': tag_content},
                            'color': tag_color,
                        }
                    )

            if text_tag_list:
                card_message['header'].update(text_tag_list=text_tag_list)  # type: ignore

        request_body = {
            'msg_type': 'interactive',
            receive_id_type.value: receivers,
            'card': card_message,
        }

        request: lark_oapi.BaseRequest = (
            lark_oapi.BaseRequest.builder()
            .http_method(lark_oapi.HttpMethod.POST)
            .uri('/open-apis/message/v4/batch_send/')
            .token_types({lark_oapi.AccessTokenType.TENANT})
            .body(request_body)
            .build()
        )

        response: lark_oapi.BaseResponse = self._client.request(request)

        # 处理失败返回
        if not response.success():
            # lark_oapi.logger.error(
            #     f'client.message.v4.batch_send failed, '
            #     f'code: {response.code}, msg: {response.msg}, '
            #     f'log_id: {response.get_log_id()}'
            # )
            # return False, response.msg
            raise RuntimeError(response.msg)

        # 处理业务结果
        # lark_oapi.logger.info(str(response.raw.content, lark_oapi.UTF_8))

        response_body = json.loads(response.raw.content)
        if response_body['code'] != 0:
            raise RuntimeError(response_body['msg'])
        return response_body['data']['message_id']


class MessageSendMixin(ClientMixin):
    """Message Send Mixin."""

    def send_text_message(self, receive_id: str, content: str, receive_id_type: str = 'user_id'):
        """Send text message to receiver.

        Args:
            receive_id (str): The ID of the receiver.
            content (str): The text content of the message.
            receive_id_type (str): The type of receiver ID.

        Returns:
            bytes: The raw response content when message is sent successfully.

        Raises:
            RuntimeError: When the message sending fails.
        """
        return self.send_message(
            receive_id=receive_id,
            content=json.dumps({'text': content}),
            message_type='text',
            receive_id_type=receive_id_type,
        )

    def send_message(
        self,
        receive_id: str,
        content: str,
        *,
        message_type: str = 'text',
        receive_id_type: str = 'user_id',
    ):
        """Send message to chat group.

        Args:
            receive_id (str): The ID of the receiver.
            content (str): The content of the message.
            message_type (str): The type of the message.
            receive_id_type (str): The type of receiver ID.

        Returns:
            bytes: The raw response content when message is sent successfully.

        Raises:
            RuntimeError: When the message sending fails.
        """
        # 构造请求对象
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type(receive_id_type)
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(receive_id)
                .msg_type(message_type)
                .content(content)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = self._client.im.v1.message.create(request)

        # 处理失败返回
        if not response.success():
            error_msg = f'client.message.v4.send failed, code: {response.code}, msg: {response.msg}'
            lark_oapi.logger.error(error_msg)
            raise RuntimeError(error_msg)

        # 处理业务结果
        return response


class MessageReplyMixin(ClientMixin):
    """Message Reply Mixin."""

    def reply(
        self,
        message_id: str,
        content: str,
        message_type: str = 'text',
        reply_in_thread: bool = False,
    ) -> ReplyMessageResponse:
        """回复消息

        Args:
            message_id (str): 消息ID
            content (str): 回复内容
            message_type (str): 消息类型，默认为text
            reply_in_thread (bool): 是否在话题中回复

        Returns:
            ReplyMessageResponse: 回复消息响应

        Raises:
            RuntimeError: 当回复消息失败时抛出异常
        """
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .message_id(message_id)
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type(message_type)
                .reply_in_thread(reply_in_thread)
                .build()
            )
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self._client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            error_msg = (
                f'client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}'
            )
            raise RuntimeError(error_msg)

        return response

    def patch(
        self,
        message_id: str,
        content: str,
    ):
        request: PatchMessageRequest = (
            PatchMessageRequest.builder()
            .message_id(message_id)
            .request_body(PatchMessageRequestBody.builder().content(content).build())
            .build()
        )

        # 发起请求
        response: PatchMessageResponse = self._client.im.v1.message.patch(request)

        # 处理失败返回
        if not response.success():
            lark_oapi.logger.error(
                f'client.im.v1.message.patch failed,code: {response.code}, msg: {response.msg}'
            )
            return False, response

        # 处理业务结果
        return True, response


class MessageQueryMixin(ClientMixin):
    """Message query Mixin."""

    def query(
        self,
        message_id: str,
    ):
        # 构造请求对象
        request: GetMessageRequest = GetMessageRequest.builder().message_id(message_id).build()

        # 发起请求
        response: GetMessageResponse = self._client.im.v1.message.get(request)

        # 处理失败返回
        if not response.success():
            error_msg = (
                f'client.im.v1.message.get failed, code: {response.code}, msg: {response.msg}'
            )
            raise RuntimeError(error_msg)

        return response


class ChatGroupMixin(ClientMixin):
    """Chat Group Mixin."""

    def get_group_by_id(self, chat_id: str):
        # 构造请求对象
        request: GetChatRequest = (
            GetChatRequest.builder().chat_id(chat_id).user_id_type('user_id').build()
        )

        # 发起请求
        response: GetChatResponse = self._client.im.v1.chat.get(request)
        if response.success():
            return response.data
        return None

    def get_group(self, group_name: str):
        try:
            items = self.search_groups(group_name)
            if not items:
                return None
            return items[0]
        except Exception:
            return None

    def search_groups(self, query: str):
        """Search for chat groups.

        Args:
            query (str): The search keywords.
        """
        request: SearchChatRequest = (
            SearchChatRequest.builder().user_id_type('user_id').query(query).build()
        )

        # 发起请求
        response: SearchChatResponse = self._client.im.v1.chat.search(request)

        # 处理失败返回
        if not response.success():
            error_msg = (
                f'client.im.v1.chat.search failed, code: {response.code}, msg: {response.msg}'
            )
            raise RuntimeError(error_msg)

        return response.data.items

    def list_groups(self):
        """List all participant chat groups."""
        request: ListChatRequest = (
            ListChatRequest.builder().user_id_type('user_id').sort_type('ByCreateTimeAsc').build()
        )

        # 发起请求
        response: ListChatResponse = self._client.im.v1.chat.list(request)

        # 处理失败返回
        if not response.success():
            error_msg = f'client.im.v1.chat.list failed, code: {response.code}, msg: {response.msg}'
            raise RuntimeError(error_msg)

        return response.data.items or []

    def list_group_messages(
        self,
        chat_id: str,
        *,
        start_time: int,
        end_time: int,
        sender_types: Optional[List[Literal['app', 'user']]] = None,
        include_system_message: bool = False,
    ):
        """List messages in a chat group.

        Args:
            chat_id (str): The ID of the chat group.
            start_time (int): The start time of the messages to be listed.
            end_time (int): The end time of the messages to be listed.
            sender_types (Optional[List[Literal['app', 'user']]]): The type of sender to filter
                messages. If None, all messages will be returned.
            include_system_message (bool): Whether to include system messages. Default is False.

        Returns:
            generator: A generator that yields messages in the chat group.
        """
        # 构造请求对象
        builder = (
            ListMessageRequest.builder()
            .container_id_type('chat')
            .container_id(chat_id)
            .start_time(str(start_time))
            .end_time(str(end_time))
            .sort_type('ByCreateTimeAsc')
            .page_size(20)
        )

        # 发起请求
        request: ListMessageRequest = builder.build()

        while True:
            response: ListMessageResponse = self._client.im.v1.message.list(request)

            if not response.success():
                lark_oapi.logger.error(
                    f'list chat messages failed, code: {response.code}, msg: {response.msg}'
                )
                break

            for item in response.data.items:
                # 过滤: 系统消息,已删除的消息,app发送的消息
                if not item.deleted:
                    if not include_system_message and item.msg_type == 'system':
                        continue

                    if sender_types:
                        if item.sender.sender_type in sender_types:
                            yield item
                    else:
                        yield item

            if response.data.page_token:
                request = builder.page_token(response.data.page_token).build()

            if not response.data.has_more:
                return


class MessageMixins(
    BatchMessageMixin,
    MessageReplyMixin,
    MessageQueryMixin,
    MessageSendMixin,
    ChatGroupMixin,
):
    """Message Mixin."""
