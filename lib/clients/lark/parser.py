"""Lark bitable field parser for create record"""

from typing import Any, Dict, Literal

from typing_extensions import Callable

ParserFunction = Callable[[Any], Any]

FieldType = Literal[
    'text',
    'link_text',
    'user',
    'user_id',
    'user_email',
    'singleselect',
    'datetime',
]


def parse_text(field_value: list) -> str:
    txt = [x['text'] for x in field_value]
    return ''.join(txt)


parsers: Dict[FieldType, ParserFunction] = {
    'text': parse_text,
    'link_text': lambda x: x['text'],
    'user': lambda x: [{'id': i['id']} for i in x],
    'user_id': lambda x: [i['id'] for i in x],
    'user_email': lambda x: [i['email'] for i in x],
}


def parse(field_type: FieldType, field_value: Any) -> Any:
    parser = parsers.get(field_type)
    if not parser:
        return field_value
    return parser(field_value)
