from typing import Any, Callable, List, Optional

import lark_oapi as lark
from lark_oapi.api.drive.v1 import File, ListFileRequest


class Folder:
    def __init__(self, client: lark.Client, folder_token: str):
        self.lark = client
        self.folder_token = folder_token

    def list_files(
        self,
        filter_opt: Optional[Callable[[Any], bool]] = None,
        page_size: int = 200,
    ) -> Optional[List[File]]:
        request = (
            ListFileRequest.builder()
            .folder_token(self.folder_token)
            .page_size(page_size)
            .build()
        )
        response = self.lark.drive.v1.file.list(request)
        if not response.success():
            return None

        if filter_opt is None:
            return response.data.files

        return list(filter(lambda f: filter_opt(f), response.data.files))
