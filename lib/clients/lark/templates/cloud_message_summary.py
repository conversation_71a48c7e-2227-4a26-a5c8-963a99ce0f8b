from typing import List, Optional


def generate_section(
    title: str, content: str, color: str = 'orange', icon: str = 'hot_outlined'
) -> List[dict]:
    section_header = {
        'tag': 'column_set',
        'flex_mode': 'none',
        'horizontal_spacing': 'default',
        'background_style': 'default',
        'columns': [
            {
                'tag': 'column',
                'elements': [
                    {
                        'tag': 'div',
                        'text': {
                            'tag': 'plain_text',
                            'content': title,
                            'text_size': 'heading',
                            # 'text_size': 'normal',
                            'text_align': 'left',
                            'text_color': color,
                        },
                        'icon': {
                            'tag': 'standard_icon',
                            'token': icon,
                            'color': color,
                        },
                    }
                ],
                'width': 'weighted',
                'weight': 1,
            }
        ],
    }

    section_content = {
        'tag': 'markdown',
        'content': content,
        'text_align': 'left',
        'text_size': 'normal',
        # 'text_size': 'notation',
    }

    section = [section_header, section_content]
    return section


def get_cloud_message_summary_card_template(header: dict, sections: List[List[dict]]) -> dict:
    all_sections = []
    for sec in sections:
        all_sections.extend(sec)

    return {
        'config': {'update_multi': True},
        'card_link': {'url': ''},
        'i18n_header': {'zh_cn': header},
        'i18n_elements': {'zh_cn': all_sections},
        # 'i18n_elements': {
        # 'zh_cn': [
        # {
        #     'tag': 'column_set',
        #     'flex_mode': 'none',
        #     'horizontal_spacing': 'default',
        #     'background_style': 'default',
        #     'columns': [
        #         {
        #             'tag': 'column',
        #             'elements': [
        #                 {
        #                     'tag': 'div',
        #                     'text': {
        #                         'tag': 'plain_text',
        #                         'content': '重要紧急',
        #                         'text_size': 'heading',
        #                         'text_align': 'left',
        #                         'text_color': 'red',
        #                     },
        #                     'icon': {
        #                         'tag': 'standard_icon',
        #                         'token': 'reply-cn_outlined',
        #                         'color': 'red',
        #                     },
        #                 }
        #             ],
        #             'width': 'weighted',
        #             'weight': 1,
        #         }
        #     ],
        # },
        # {
        #     'tag': 'markdown',
        #     'content': '${import_urgy}',
        #     'text_align': 'left',
        #     'text_size': 'normal',
        # },
        # {
        #     'tag': 'column_set',
        #     'flex_mode': 'none',
        #     'horizontal_spacing': 'default',
        #     'background_style': 'default',
        #     'columns': [
        #         {
        #             'tag': 'column',
        #             'elements': [
        #                 {
        #                     'tag': 'div',
        #                     'text': {
        #                         'tag': 'plain_text',
        #                         'content': '重要不紧急',
        #                         'text_size': 'heading',
        #                         'text_align': 'left',
        #                         'text_color': 'orange',
        #                     },
        #                     'icon': {
        #                         'tag': 'standard_icon',
        #                         'token': 'hot_outlined',
        #                         'color': 'orange',
        #                     },
        #                 }
        #             ],
        #             'width': 'weighted',
        #             'weight': 1,
        #         }
        #     ],
        # },
        # {
        #     'tag': 'markdown',
        #     'content': '- Redis实例(r-0xi3db3025657c74等6个) 计划于2025-05-11进行运维, 可能闪断',
        #     'text_align': 'left',
        #     'text_size': 'normal',
        # },
        # # {
        # #     'tag': 'note',
        # #     'elements': [
        # #         {'tag': 'standard_icon', 'token': 'robot-add_outlined'},
        # #         {'tag': 'plain_text', 'content': '如有疑问，可咨询值班号'},
        # #     ],
        # # },
        # ]
        # },
    }


def generate_header(
    title: str,
    subtitle: Optional[str] = None,
    tags: Optional[List[str]] = None,
    tag_color: str = 'yellow',
    header_color: str = 'blue',
) -> dict:
    header = {
        'tag': 'header',
        'template': header_color,
        'title': {'tag': 'plain_text', 'content': title},
    }

    if subtitle:
        header['subtitle'] = {
            'tag': 'plain_text',
            'content': subtitle,
        }

    if tags:
        header['text_tag_list'] = []
        for tag in tags:
            header['text_tag_list'].append(
                {
                    'tag': 'text_tag',
                    'text': {
                        'tag': 'plain_text',
                        'content': tag,
                    },
                    'color': tag_color,
                }
            )

    return header
