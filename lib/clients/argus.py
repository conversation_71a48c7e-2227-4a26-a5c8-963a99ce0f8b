from pathlib import Path
from typing import Optional

import httpx

from lib.configs.rootcause import RootCauseConfig

from .auth.bear import BearAuth


class ArgusClient:
    """Argus Client for interacting with the Argus rootcause service."""

    def __init__(self, endpoint: str, token: Optional[str] = None, timeout: Optional[int] = None):
        self.endpoint = endpoint
        self.token = token

        self.client = httpx.Client(
            base_url=self.endpoint,
            timeout=timeout,
            auth=BearAuth(token) if token else None,
        )

    @classmethod
    def from_config(cls, config: RootCauseConfig) -> 'ArgusClient':
        return cls(
            endpoint=str(config.url),
            token=config.token,
            timeout=config.timeout,
        )

    def get(self, subpath: str, *, params: Optional[dict] = None):
        p = Path(self.endpoint) / Path(subpath)
        response = self.client.get(f'{p}', params=params)

        if response.status_code != 200:
            raise Exception(f'Error: {response.status_code} - {response.content.decode("utf-8")}')

        return response.text
