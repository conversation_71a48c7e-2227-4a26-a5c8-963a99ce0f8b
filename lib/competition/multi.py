from abc import abstractmethod
from typing import Dict

from langchain.memory import ConversationBufferMemory
from langchain_core.language_models import BaseLanguageModel
from langchain_core.messages import SystemMessage

from lib.llm.models import get_llm
from lib.utils.printer import debug


class Agent:
    def __init__(
        self, name: str, llm: BaseLanguageModel, memory_key: str = 'chat_history'
    ):
        self.name = name
        self.llm = llm
        self.memory = ConversationBufferMemory(
            memory_key=memory_key,
            return_messages=True,
        )

    @abstractmethod
    def invoke(self, query: str):
        raise NotImplementedError


class ReserchAgent(Agent):
    def invoke(self, topic: str) -> str:
        prompt = f'作为一个研究员, 请对{topic}进行深入研究并提供关键发现。'
        message = [SystemMessage(content=prompt)]
        response = self.llm.invoke(message)
        return response.content


class WriterAgent(Agent):
    def invoke(self, research_findings: str) -> str:
        prompt = f'基于以下研究发现,  创作一篇文章:\n{research_findings}的文章。'
        message = [SystemMessage(content=prompt)]
        response = self.llm.invoke(message)
        return response.content


class Coordinator:
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self.message_queue = []

    def add_agent(self, agent: Agent):
        self.agents[agent.name] = agent

    def send_message(self, from_agent: str, to_agent: str, message: str):
        self.message_queue.append(
            {
                'from': from_agent,
                'to': to_agent,
                'message': message,
            }
        )

    def process_messages(self):
        while self.message_queue:
            msg = self.message_queue.pop(0)
            if msg['to'] in self.agents:
                receiver = self.agents[msg['to']]
                response = receiver.invoke(msg['message'])

                debug(f'From {msg["from"]} to {msg["to"]}', color='yellow')
                return response


def main(topic: str):
    researcher = ReserchAgent('researcher', get_llm('gpt4o'))
    writer = WriterAgent('writer', get_llm('qwen-plus'))

    coordinator = Coordinator()

    coordinator.add_agent(researcher)
    coordinator.add_agent(writer)

    research_findings = researcher.invoke(topic)
    debug('研究发现:', research_findings)

    coordinator.send_message(researcher.name, writer.name, research_findings)

    final_article = coordinator.process_messages()
    debug('\n最终文件:', final_article, color='green')


if __name__ == '__main__':
    topic = '如何用pytorch开始学习机器学习NLP'
    main(topic)
