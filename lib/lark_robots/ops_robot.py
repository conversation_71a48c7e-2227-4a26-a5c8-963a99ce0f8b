import lark_oapi as lark
import orjson as json

import app  # noqa
from lib.clients.lark.parsers.message import extract_richtext
from lib.configs import config
from lib.utils.mention import clean_content
from tasks.chat import group_chat, p2p_chat_by_dify


def handle_p2p_message(event: lark.im.v1.P2ImMessageReceiveV1Data) -> None:
    content_dict = json.loads(event.message.content)

    if event.message.message_type == 'post':
        content = extract_richtext(content_dict)
    else:
        content = content_dict[event.message.message_type]

    if event.message.mentions:
        mentions = [mention.key for mention in event.message.mentions]
        content = clean_content(content, mentions)

    p2p_chat_by_dify.delay(
        content,
        event.message.message_id,
        event.message.thread_id,
        event.sender.sender_id.user_id,
        event.message.create_time,
    )


def handle_group_message(event: lark.im.v1.P2ImMessageReceiveV1Data) -> None:
    content_dict = json.loads(event.message.content)
    content = content_dict[event.message.message_type]

    if event.message.mentions:
        mentions = [mention.key for mention in event.message.mentions]
        content = clean_content(content, mentions)

    group_chat.delay(
        content,
        event.message.message_id,
    )


# P2ImMessageReceiveV1 为接收消息 v2.0；CustomizedEvent 内的 message 为接收消息 v1.0。
def do_p2_im_message_receive_v1(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
    chat_type = data.event.message.chat_type
    if chat_type == 'p2p':
        handle_p2p_message(data.event)
    elif chat_type == 'group':
        handle_group_message(data.event)
    else:
        print(f'unknow chat_type: {chat_type}')


def main():
    lark_config = config.lark.apps['aiops']

    event_handler = (
        lark.EventDispatcherHandler.builder('', lark_config.verification_token)
        .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)
        .build()
    )

    cli = lark.ws.Client(
        lark_config.app_id,
        lark_config.app_secret,
        event_handler=event_handler,
        log_level=lark.LogLevel.DEBUG,
    )
    cli.start()


if __name__ == '__main__':
    main()
