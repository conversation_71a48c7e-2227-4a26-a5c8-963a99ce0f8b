import lark_oapi as lark
import orjson as json

import app  # noqa
from lib.configs import config
from lib.utils.mention import clean_content
from tasks.argus import group_chat


def handle_group_message(event: lark.im.v1.P2ImMessageReceiveV1Data) -> None:
    content_dict = json.loads(event.message.content)
    content = content_dict[event.message.message_type]

    if not event.message.mentions:
        return

    mention_names = [mention.name.lower() for mention in event.message.mentions]
    if 'argus' not in mention_names:
        return

    mentions = [mention.key for mention in event.message.mentions]
    content = clean_content(content, mentions)

    group_chat.apply_async(
        kwargs={
            'content': content,
            'message_id': event.message.message_id,
            'parent_id': event.message.parent_id,
            'at_list': [event.sender.sender_id.open_id],
        },
    )


## P2ImMessageReceiveV1 为接收消息 v2.0；CustomizedEvent 内的 message 为接收消息 v1.0。
def do_p2_im_message_receive_v1(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
    print(f'[ do_p2_im_message_receive_v1 access ], data: {lark.JSON.marshal(data, indent=2)}')
    if data.event.message.chat_type == 'group':
        handle_group_message(data.event)


def main():
    lark_config = config.lark.apps['argus']

    event_handler = (
        lark.EventDispatcherHandler.builder('', lark_config.verification_token)
        .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)
        .build()
    )

    cli = lark.ws.Client(
        lark_config.app_id,
        lark_config.app_secret,
        event_handler=event_handler,
        log_level=lark.LogLevel.DEBUG,
    )
    cli.start()


if __name__ == '__main__':
    main()
