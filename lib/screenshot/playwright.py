import time
from typing import Callable, Optional, Tuple

from playwright.sync_api import sync_playwright

from lib.constants import STORE_ROOT


class PlaywrightScreenShot:
    """Playwright screenshot class for Grafana.
    Playwright driven to take screenshots of Grafana dashboards.
    """

    _DEFAULT_WINDOWS_SIZE = (1920, 1080)

    def __init__(self, window_size: Optional[Tuple[int, int]] = None):
        self._window_size = window_size or self._DEFAULT_WINDOWS_SIZE

    @property
    def windows_size(self):
        return {'width': self._window_size[0], 'height': self._window_size[1]}

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def take(
        self,
        url: str,
        save_path: str = 'screenshot.png',
        *,
        parser: Optional[Callable[[str], dict]] = None,
        goto_util: str = 'domcontentloaded',
        wait_selector: Optional[str] = None,
    ) -> dict:
        """
        Takes a screenshot of the given URL using Playwright.

        Args:
            url (str): The URL to take a screenshot of.
            save_path (str): The path to save the screenshot.
            parser (Optional[Callable[[str], Any]]): A function to parse the HTML content.

        Returns:
            dict: A dictionary containing the screenshot path and any parsed data.
        """
        # Initialize Playwright and launch the browser
        with sync_playwright() as p:
            # with p.firefox.launch(headless=True) as browser:
            with p.chromium.launch(headless=True) as browser:
                with browser.new_context(
                    viewport=self.windows_size,
                    device_scale_factor=1.2,  # 设置设备缩放比例以获得更高分辨率的截图
                ) as context:
                    page = context.new_page()
                    page.add_init_script(
                        """
                        // Fold the sidebar and hide the search bar
                        window.localStorage.setItem('grafana.navigation.docked', 'false');
                        window.localStorage.setItem('SearchBar_Hidden', 'true');
                        """
                    )

                    page.goto(url, wait_until=goto_util)
                    if wait_selector:
                        # page.wait_for_selector('#pageContent .scrollbar-view table')
                        page.wait_for_selector(wait_selector)
                    page.screenshot(path=save_path, full_page=True)

                    results = {'image': save_path}
                    if parser:
                        parsed_data = parser(page.inner_html('body'))
                        results.update(parsed_data)
                    return results


class FeishuLoginPlaywrightScreenShot:
    """Playwright screenshot class for Feishu login.
    Playwright driven to take screenshots of Feishu login page.
    """

    _DEFAULT_WINDOWS_SIZE = (1920, 1080)

    def __init__(self, window_size: Optional[Tuple[int, int]] = None, headless: bool = True):
        self._window_size = window_size or self._DEFAULT_WINDOWS_SIZE
        self.headless = headless

    @property
    def windows_size(self):
        return {'width': self._window_size[0], 'height': self._window_size[1]}

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def take(self, url: str, save_path: str = 'screenshot.png'):
        """
        Takes a screenshot of the given URL using Playwright.

        Args:
            url (str): The URL to take a screenshot of.
            save_path (str): The path to save the screenshot.

        Returns:
            dict: A dictionary containing the screenshot path.
        """
        # Initialize Playwright and launch the browser
        with sync_playwright() as p:
            with p.chromium.launch(headless=self.headless) as browser:
                with browser.new_context(
                    viewport=self.windows_size,
                    device_scale_factor=1.2,  # 设置设备缩放比例以获得更高分辨率的截图
                    locale='zh-CN',
                ) as context:
                    page = context.new_page()

                    page.add_style_tag(
                        content="""
                        // Override the default font-family to use system fonts
                        * {
                            font-family: Arial, sans-serif !important;
                        }
                        """
                    )

                    try:
                        # page.goto(url, wait_until='domcontentloaded')
                        # page.goto(url, wait_until='commit')
                        page.goto(url)

                        # 1. 发起获取授权Code请求
                        # qrcode_selector = '#root div.web-main-content'
                        qrcode_selector = '#root div.new-scan-login-box'
                        page.wait_for_selector(qrcode_selector)
                    except Exception as e:
                        print('wait qrcode error:', e)
                        page.screenshot(path=save_path)
                        raise e

                    # 2. 截图二维码
                    page.query_selector(qrcode_selector, strict=True).screenshot(path=save_path)
                    yield {'image': save_path}

                    # 3. 等待用户扫码登录
                    clicked = False
                    for _ in range(20):
                        # Check if the authorization footer is present
                        auth_div = page.query_selector('div.authorization-footer')
                        if auth_div:
                            # click the 2nd button
                            auth_div.query_selector('button:nth-child(2)').click()
                            clicked = True
                            break
                        time.sleep(3)

                    if not clicked:
                        raise RuntimeError('Failed to click the authorization button')

                    # 4. 等待redirect_url返回
                    page.wait_for_url('**/eagle.lilithgame.com/api/oauth*')

                    # 5.为了人类验证，，保留最终截图
                    final_image_path = (
                        STORE_ROOT / f'final_feishu-{time.time_ns()}.png'
                    ).as_posix()
                    page.screenshot(path=final_image_path)
                    yield True
