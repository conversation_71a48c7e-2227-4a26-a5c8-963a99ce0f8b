from typing import Optional

from bs4 import BeautifulSoup


def parse_table_values(html: str, limit: Optional[int] = None):
    """
    Parses an HTML table and returns its content as a CSV string.

    Args:
        html (str): The HTML content containing the table.
        limit (Optional[int]): The maximum number of rows to parse. Defaults to 50.

    Returns:
        str: A CSV string representation of the table's content.
    """
    if limit is None:
        limit = 50

    soup = BeautifulSoup(html, 'html.parser')
    all_tables = soup.find_all('table')
    if not all_tables:
        raise ValueError('Table not found in the HTML content.')

    table = all_tables.pop()

    rows = []

    list_headers = []
    for items in table.find_all('th'):
        list_headers.append(items.get_text().strip())

    if not list_headers[0]:
        list_headers[0] = 'name'
    rows.append(','.join(list_headers))

    html = table.find_all('tr')[1:]
    for element in html:
        sub_list = [td.get_text().strip() for td in element.find_all('td')]

        if '' in sub_list or '∞' in sub_list:
            continue

        # drop values exceed limit
        if len(rows) == limit:
            break

        rows.append(','.join(sub_list))

    return '\n'.join(rows)


def table_parser(html: str) -> dict:
    """Parse the HTML content and extract relevant information."""
    soup = BeautifulSoup(html, 'html.parser')

    # Extract the title
    panel_name = soup.select_one('#pageContent h6').text

    # Extract the body content
    table_data = parse_table_values(html)

    return {'panel_name': panel_name, 'value': table_data}
