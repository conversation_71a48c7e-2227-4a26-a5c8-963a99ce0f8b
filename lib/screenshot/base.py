import os
import time
from abc import ABC, abstractmethod
from typing import Any, Callable, List, Optional, Tuple

from selenium.webdriver.remote.webdriver import WebDriver as RemoteWebDriver

from .paths import update_path, urlparam_to_dict

ScreenShotHandler = Callable[[RemoteWebDriver], None]


class ScreenShot(ABC):
    """ScreenShot take screenshot from url"""

    def __init__(self, driver: 'RemoteWebDriver'):
        self.driver = driver
        self._handlers: List[ScreenShotHandler] = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, exc_tb):
        self.driver.quit()

    def add_handlers(self, handlers: List[ScreenShotHandler]):
        """before take screenshot.
        handler some actions before take screenshot
        """
        self._handlers.extend(handlers)

    @abstractmethod
    def screenshot(
        self,
        url: str,
        filename: str,
        timeout: int = 2,
    ) -> Tuple[bool, Any]:
        raise NotImplementedError

    def take(
        self,
        url: str,
        base_dir: str,
        timeout: int = 3,
        qs: Optional[dict] = None,
        domain_replacer: Optional[Callable[[str], str]] = None,
        token: Optional[str] = None,
    ):
        """take screenshot from url, and save to base_dir"""

        # with GrafanaScreenShot(driver) as shot:
        save_to = os.path.join(base_dir, f'{self.driver.session_id}')
        os.makedirs(save_to, exist_ok=True)

        qs = qs or {}
        download_url = update_path(url, qs, domain_replacer=domain_replacer)
        img_name = f'{time.time_ns()}.png'

        img_save_to = os.path.join(save_to, img_name)

        success, metainfo = self.screenshot(
            download_url,
            img_save_to,
            timeout=timeout,
        )
        if success:
            if isinstance(metainfo, dict):
                params = urlparam_to_dict(download_url)
                metainfo.update(params)
            return img_save_to, metainfo
        return '', metainfo
