from typing import Callable, Optional
from urllib.parse import parse_qs, urlencode, urlparse, urlunparse


def urlparam_to_dict(url: str) -> dict:
    """convert url query string to dict"""
    qs = url_qs(url)

    params = {}
    for k, v in qs.items():
        if len(v) == 1:
            params[k] = v[0]
        else:
            params[k] = v
    return params


def url_qs(url: str) -> dict:
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    return query_params


def modify_path(
    path: str,
    new_qs: dict,
    domain_replacer: Optional[Callable[[str], str]] = None,
) -> str:
    """update path with new query string.

    :param:
        - path: url path

        - new_qs: new query string

        - domain_replacer: a function to replace domain
    """
    parsed_url = urlparse(path)

    query_params = parse_qs(parsed_url.query)
    # print(f"查询参数: {query_params}")

    for k, v in new_qs.items():
        query_params[k] = v
    new_qs = urlencode(query_params, doseq=True)

    #  为了跳过登录，可以改变域名
    netloc = parsed_url.netloc
    if domain_replacer:
        netloc = domain_replacer(netloc)

    return urlunparse(
        (
            parsed_url.scheme,
            netloc,
            parsed_url.path,
            parsed_url.params,
            new_qs,
            parsed_url.fragment,
        )
    )


def oma_monitor_replacer(domain: str) -> str:
    if domain == 'monitor.lilithgame.com':
        return 'oma-' + domain
    return domain


def update_path(*args, **kwargs):
    kwargs.setdefault('domain_replacer', oma_monitor_replacer)
    return modify_path(*args, **kwargs)
