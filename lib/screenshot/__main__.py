from typing import Optional

from lib.configs import config

from .driver import DriverType, local_driver, remote_driver
from .grafana import (
    GrafanaScreenShot,
    GrafanaTableScreenShot,
)
from .paths import oma_monitor_replacer


def main(url: str, driver_type: DriverType = 'remote', qs: Optional[dict] = None):
    driver = remote_driver() if driver_type == 'remote' else local_driver()

    with GrafanaScreenShot(driver) as shot:
        saved_to, meta = shot.take(url, './store', timeout=30, qs=qs)
        print(f'saved_to: {saved_to}')
        print(f'metainfo: {meta}')


def main_tableless(url: str, driver_type: DriverType = 'remote', qs: Optional[dict] = None):
    driver = remote_driver() if driver_type == 'remote' else local_driver()

    # from lib.screenshot.paths import oma_monitor_replacer
    # with GrafanaTablelessScreenShot(driver) as shot:
    with GrafanaTableScreenShot(driver) as shot:
        saved_to, meta = shot.take(
            url, './store', timeout=30, qs=qs, domain_replacer=oma_monitor_replacer
        )
        # saved_to, meta = shot.take(url, './store', timeout=30, qs=qs)
        print(f'saved_to: {saved_to}')
        print(f'metainfo: {meta}')


if __name__ == '__main__':
    import sys

    driver_type = sys.argv[1] if len(sys.argv) > 1 else 'remote'

    if driver_type not in ['remote', 'local']:
        print(f'Invalid driver type {driver_type}', file=sys.stderr)
        print('Usage: python -m lib.screenshot [remote|local(default)]')
        sys.exit(1)

    dashboard = config.grafanas['k8s']

    dashboard_id = dashboard.dashboard_id
    views = dashboard.views

    # ad
    origin_url = 'https://monitor.lilithgame.com/d/eba6a3bb-ba23-4779-a7e0-48f908e35885/dapper-dashboard-vm?orgId=1&viewPanel=14'
    # ops
    # origin_url = 'https://monitor.lilithgame.com/d/adrioz46wvwu8e/kmae5b9b3-e58fb0-pode79b91-e68ea7-ai?orgId=1&viewPanel=7'

    origin_url = 'https://oma-monitor.lilithgame.com/d/eba6a3bb-ba23-4779-a7e0-48f908e35885/dapper-dashboard-vm?orgId=1&viewPanel=14&from=1725292800000&to=1725379199000'

    origin_url = f'https://oma-monitor.lilithgame.com/d/{dashboard_id}/{dashboard.name}?orgId=1&var-datasource=f70420bb-c623-4c49-b58b-7bed3803830e&var-cluster_name=tva-igame-prod-global-k8s&var-Node=All&var-NameSpace=All&var-Deployment=All&var-Container=prod-global-btcheck&var-Pod=prod-global-btcheck-22c8z&viewPanel=58'
    # origin_url += 'from=1631539200000&to=1631625599000'
    # origin_url += '&from=1631539200000&to=1631625599000'
    origin_url = 'https://oma-monitor.lilithgame.com/d/PwMJtdvnz/k8s-dashboard?orgId=1&var-datasource=bbf8f4e7-dc47-4888-a126-3c31472fa5ad&var-cluster_name=mona-cn-prod-k8s-tsh-foodprod-0002&var-Node=All&var-NameSpace=foodprod&var-Deployment=All&var-Container=All&var-Pod=All&viewPanel=24'
    origin_url += '&from=now-24h&to=now'

    print('page url: ', origin_url)

    main_tableless(origin_url, driver_type)  # type: ignore

    # strategy = invoke('按天环比，指标变化幅度上下 10%')

    # left_range = [timestamp_ms(t) for t in strategy.period_previous]
    # right_range = [timestamp_ms(t) for t in strategy.period_current]
    # # threshold = strategy.threshold

    # print('left_range:', left_range)
    # print('right_range:', right_range)
    # print('threshold:', threshold)

    # for t_range in [left_range, right_range]:
    #     qs = {
    #         'from': t_range[0],
    #         'to': t_range[1],
    #     }
    #     main_tableless(origin_url, driver_type, qs=qs)
