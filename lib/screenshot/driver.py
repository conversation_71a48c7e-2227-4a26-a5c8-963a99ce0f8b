from typing import Literal, Optional, Tuple

from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.remote.webdriver import WebDriver

DriverType = Literal['remote', 'local']
Resolution = Tuple[int, int]


def remote_driver(window_size: Resolution = (1920, 1080)) -> 'WebDriver':
    options = ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    # options.add_argument('--disable-dev-shm-usage')
    # 禁用CSS动画，以确保稳定的截图
    options.add_argument('--disable-animations')

    driver = webdriver.Remote(options=options, command_executor='http://127.0.0.1:4444')
    driver.set_window_size(*window_size)
    return driver


def local_driver(
    window_size: Resolution = (1920, 1080), driver_path: Optional[str] = None
) -> 'WebDriver':
    options = ChromeOptions()
    options.add_argument('--headless')

    if driver_path:
        driver = webdriver.Chrome(
            options=options,
            service=webdriver.ChromeService(executable_path=driver_path),
        )
    else:
        driver = webdriver.Chrome(options=options)
    driver.set_window_size(*window_size)
    return driver
