import time
import traceback
from typing import Tu<PERSON>

from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from .base import ScreenShot
from .parse import table_parser


class GrafanaScreenShot(ScreenShot):
    def __init__(self, driver: 'WebDriver'):
        super().__init__(driver)
        self.add_handlers(self.default_handlers())

    @classmethod
    def default_handlers(self):
        return [self.fold_sidebar, self.until_loaded]

    @classmethod
    def fold_sidebar(cls, driver: 'WebDriver'):
        driver.execute_script(
            (
                'window.localStorage.setItem("grafana.navigation.docked", "false");'
                'window.localStorage.setItem("SearchBar_Hidden", "true");'
            )
        )
        driver.refresh()

    @classmethod
    def until_loaded(cls, driver: 'WebDriver'):
        wait = WebDriverWait(driver, 20)
        # wait.until(EC.presence_of_element_located((By.ID, 'pageContent')))
        wait.until(EC.visibility_of_all_elements_located((By.CLASS_NAME, 'scrollbar-view')))
        # ensure the page is loaded
        time.sleep(10)

    def visit(self, url: str):
        self.driver.get(url)

        # handle some actions before take screenshot
        for handler in self._handlers:
            handler(self.driver)

    def screenshot(self, url: str, filename: str, timeout: int = 2) -> Tuple[bool, dict | str]:
        print(f'save screenshot to {filename}\nurl: {url}')

        try:
            self.visit(url)

            saved = self.driver.save_screenshot(filename)

            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')

            # navs = [link.text for link in soup.nav.find_all('a')[2:]]
            panel_name = soup.select_one('#pageContent h6').text

            # title = f'{navs[-1]}/{panel_title}'

            return saved, {'panel_name': panel_name}
        except Exception as ex:
            traceback.print_exc(limit=5)
            print(f'exception raised, {ex}')
            return False, str(ex)


class GrafanaTableScreenShot(GrafanaScreenShot):
    def screenshot(
        self,
        url: str,
        filename: str,
        timeout: int = 2,
    ) -> Tuple[bool, dict | str]:
        try:
            self.visit(url)
            saved = self.driver.save_screenshot(filename)

            html_content = self.driver.page_source
            results = table_parser(html_content)

            return saved, results
        except Exception as ex:
            print(f'exception raised, {ex}')
            return False, str(ex)


class GrafanaTablelessScreenShot(GrafanaScreenShot):
    @classmethod
    def until_loaded(cls, driver: 'WebDriver'):
        wait = WebDriverWait(driver, 20)
        # wait.until(EC.presence_of_element_located((By.ID, 'pageContent')))
        # wait.until(
        #     EC.visibility_of_all_elements_located((By.CLASS_NAME, 'scrollbar-view'))
        # )
        wait.until(EC.visibility_of_all_elements_located((By.CSS_SELECTOR, '#pageContent h6')))

        # ensure the page is loaded
        time.sleep(10)

    def screenshot(self, url: str, filename: str, timeout: int = 2) -> Tuple[bool, dict | str]:
        print(f'saving screenshot from {url}')

        try:
            self.visit(url)

            saved = self.driver.save_screenshot(filename)

            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')

            # navs = [link.text for link in soup.nav.find_all('a')[2:]]
            panel_name = soup.select_one('#pageContent h6').text

            # title = f'{navs[-1]}/{panel_name}'

            # metric value
            metric_value = soup.select_one('#pageContent').find('div', {'title': True}).text
            return saved, {'panel_name': panel_name, 'value': metric_value}
        except Exception as ex:
            traceback.print_exc(limit=5)
            print(f'exception raised, {ex}')
            return False, str(ex)
