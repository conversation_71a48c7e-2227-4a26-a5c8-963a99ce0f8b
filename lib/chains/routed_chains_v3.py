from operator import itemgetter
from typing import Literal, TypedDict

from langchain.memory import ConversationBufferMemory
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import Runnable, RunnableLambda, RunnablePassthrough

from lib.llm.models import get_llm
from lib.utils.printer import debug

llm = get_llm('gpt4o')
memory = ConversationBufferMemory(return_messages=True)

# 第一个提示适合回答物理问题
physics_template = """你是一个非常聪明的物理专家。 \
你擅长用一种简洁并且易于理解的方式去回答问题。\
当你不知道问题的答案时，你承认\
你不知道.

<< 对话历史 >>
{history}

这是一个问题:
{input}"""

# 第二个提示适合回答数学问题
math_template = """你是一个非常优秀的数学家。 \
你擅长回答数学问题。 \
你之所以如此优秀，\
是因为你能够将棘手的问题分解为组成部分，\
回答组成部分，然后将它们组合在一起，回答更广泛的问题。

<< 对话历史 >>
{history}

这是一个问题：
{input}"""

# 第三个适合回答历史问题
history_template = """你是以为非常优秀的历史学家。 \
你对一系列历史时期的人物、事件和背景有着极好的学识和理解\
你有能力思考、反思、辩证、讨论和评估过去。\
你尊重历史证据，并有能力利用它来支持你的解释和判断。

<< 对话历史 >>
{history}

这是一个问题:
{input}"""

# 第四个适合回答计算机问题
computerscience_template = """ 你是一个成功的计算机科学专家。\
你有创造力、协作精神、\
前瞻性思维、自信、解决问题的能力、\
对理论和算法的理解以及出色的沟通技巧。\
你非常擅长回答编程问题。\
你之所以如此优秀，是因为你知道 \
如何通过以机器可以轻松解释的命令式步骤描述解决方案来解决问题，\
并且你知道如何选择在时间复杂性和空间复杂性之间取得良好平衡的解决方案。

<< 对话历史 >>
{history}

这还是一个输入：
{input}"""

prompt_infos = [
    {
        '名字': '物理学',
        '描述': '擅长回答关于物理学的问题',
        '提示模板': physics_template,
    },
    {
        '名字': '数学',
        '描述': '擅长回答数学问题',
        '提示模板': math_template,
    },
    {
        '名字': '历史',
        '描述': '擅长回答历史问题',
        '提示模板': history_template,
    },
    {
        '名字': '计算机科学',
        '描述': '擅长回答计算机科学问题',
        '提示模板': computerscience_template,
    },
]

# 目标链是由路由链调用的链, 每个目标链都是一个LLMChain实例
destination_chains = {}
for p_info in prompt_infos:
    name = p_info['名字']
    prompt_template = p_info['提示模板']
    promt = ChatPromptTemplate.from_template(prompt_template)
    expert_chain = promt | llm | StrOutputParser()
    destination_chains[name] = expert_chain

destinations = [f'{p["名字"]}: {p["描述"]}' for p in prompt_infos]
destinations_str = '\n'.join(destinations)
ExpertName = Literal['物理学', '数学', '历史', '计算机科学', 'DEFAULT']


# 默认目标链
default_prompt = ChatPromptTemplate.from_template('{input}')
default_chain = default_prompt | llm | StrOutputParser()

# 动态agent注册表
agent_registry = {}


def register_agent(name: str, description: str, template: str):
    """注册新的agent"""
    agent_registry[name] = {'名字': name, '描述': description, '提示模板': template}
    # 更新目标链
    prompt = ChatPromptTemplate.from_template(template)
    expert_chain = prompt | llm | StrOutputParser()
    destination_chains[name] = expert_chain


# 多提示路由模板
MULTI_PROMPT_ROUTER_TEMPLATE = """\
给语言模型一个原始文本输入和对话历史，让其选择最适合输入的模型提示。\
系统将为您提供可用提示的名称以及最适合改提示的描述。\
你可以根据以下规则选择目标agent：
1. 如果输入包含特定领域关键词，选择对应agent
2. 如果输入需要多领域知识，选择DEFAULT
3. 如果输入需要修改以更好匹配agent，可以修改输入

<< 对话历史 >>
{{history}}

<< 格式 >>
返回一个带有JSON对象的markdown代码片段，该JSON对象的格式如下：
```json
{{{{
"destination": 字符串, 使用的提示名字或者使用 "DEFAULT"
"next_inputs": 字符串, 原始输入的改进版本,
"reason": 字符串, 选择该agent的原因
}}}}
```

<< 候选提示 >>
{destinations}
<< 输入 >>
{{input}}
<< 输出>>

样例:
<< 输入 >>
"什么是黑体辐射?"
<< 输出 >>
```json
{{{{
"destination": "物理学",
"next_inputs": "请解释黑体辐射的概念及其物理意义",
"reason": "输入包含物理学术语'黑体辐射'"
}}}}
```
"""

# 路由链
router_template = MULTI_PROMPT_ROUTER_TEMPLATE.format(
    destinations=destinations_str,
)
print(f'router_template:\n{router_template}')
router_prompt = ChatPromptTemplate.from_template(router_template)


class RouteQuery(TypedDict):
    """Route query to destination."""

    destination: ExpertName


router_chain = (
    router_prompt | llm.with_structured_output(RouteQuery) | itemgetter('destination')
)


def choose_chain(x):
    """根据路由结果选择目标链"""
    destination = x['destination']
    if destination in destination_chains:
        # 记录选择原因
        debug(f'选择 {destination} agent，原因：{x.get("reason", "无")}')
        return destination_chains[destination]
    else:
        # 使用默认链并记录原因
        debug(f'使用 DEFAULT agent，原因：{x.get("reason", "无")}')
        return default_chain


overall_chain: Runnable = {
    'destination': router_chain,
    'input': RunnablePassthrough(),
    'history': RunnablePassthrough(),
} | RunnableLambda(choose_chain)


def invoke(question: str, chat_history=None):
    """调用路由链处理问题"""
    # 加载对话历史
    if chat_history:
        memory.chat_memory.add_user_message(chat_history['user'])
        memory.chat_memory.add_ai_message(chat_history['ai'])

    # 准备输入
    inputs = {'input': question, 'history': memory.load_memory_variables({})['history']}

    # 调用链
    debug(f'处理问题: {question}')
    response = overall_chain.invoke(inputs)

    # 保存上下文
    memory.save_context({'input': question}, {'output': response})

    # 返回响应内容
    return response


if __name__ == '__main__':
    # 测试多轮对话
    questions = ['什么是黑体辐射?', '能详细解释一下吗?', '它和量子力学有什么关系?']

    chat_history = None
    for q in questions:
        debug('Question:', q, color='blue')
        response = invoke(q, chat_history)
        debug('Answer:', response, color='green')
        chat_history = {'user': q, 'ai': response}
