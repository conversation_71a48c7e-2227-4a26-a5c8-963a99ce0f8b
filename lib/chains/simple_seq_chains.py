from langchain.chains.llm import LL<PERSON>hain
from langchain.chains.sequential import SimpleSequentialChain
from langchain.prompts import ChatPromptTemplate

from lib.llm.models import get_llm
from lib.utils.printer import debug

llm = get_llm('gpt4o')


def run(question: str):
    # 提示模板1: 接受产品并返回最佳公司名称来描述公司
    first_prompt = ChatPromptTemplate.from_template(
        '描述制造 **{product}** 的一个公司的最好的名称是什么?'
    )
    chain_one = LLMChain(llm=llm, prompt=first_prompt)

    # 提示模板2: 接受公司名称, 然后输出该公司的描述
    second_prompt = ChatPromptTemplate.from_template(
        '写一个 20 字的描述对于下面这个公司: {company_name}'
    )
    chain_two = LLMChain(llm=llm, prompt=second_prompt)

    overall_simple_chain = SimpleSequentialChain(
        chains=[chain_one, chain_two],
        verbose=True,
    )

    response = overall_simple_chain.run(question)
    return response


def invoke(question: str):
    # 提示模板1: 接受产品并返回最佳公司名称来描述公司
    first_prompt = ChatPromptTemplate.from_template(
        '描述制造 **{product}** 的一个公司的最好的名称是什么?'
    )
    chain_one = first_prompt | llm

    # 提示模板2: 接受公司名称, 然后输出该公司的描述
    second_prompt = ChatPromptTemplate.from_template(
        '写一个 20 字的描述对于下面这个公司: {company_name}'
    )
    chain_two = second_prompt | llm

    overall_simple_chain = chain_one | chain_two

    return overall_simple_chain.invoke(question)


if __name__ == '__main__':
    final_anwser = invoke('年轻的第一辆跑车')
    debug('final answer:', final_anwser.content, color='red')
