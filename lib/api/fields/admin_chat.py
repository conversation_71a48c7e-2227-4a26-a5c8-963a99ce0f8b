import pendulum
from flask_restful import fields


class UnixTimestampField(fields.Raw):
    def format(self, value: int):
        return pendulum.from_timestamp(value / 1000, tz='Asia/Shanghai').format(
            'YYYY-MM-DD HH:mm:ss'
        )


chat_fields = {
    # 'id': fields.Integer,
    'conversation_id': fields.String,
    'topic': fields.String,
    'universal_id': fields.String,
    'user': fields.String,
    'create_time': UnixTimestampField,
}

chat_message_fields = {
    # 'id': fields.Integer,
    'message_id': fields.String,
    'content': fields.String,
}
