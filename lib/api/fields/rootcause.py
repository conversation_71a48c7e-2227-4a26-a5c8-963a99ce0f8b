import json
from dataclasses import dataclass
from typing import Dict, List


@dataclass
class QuickRootCauseRequest:
    """快速根因分析请求参数.

    简化后的请求参数，只包含消息ID、父消息ID和@列表
    """

    message_id: str
    parent_message_id: str
    at_list: List[str]

    @classmethod
    def from_request_args(cls, args: Dict[str, str]) -> 'QuickRootCauseRequest':
        """从 Flask request.args 解析创建请求对象.

        Args:
            args: Flask request.args 字典

        Returns:
            QuickRootCauseRequest: 解析后的请求对象

        Raises:
            ValueError: 当参数解析失败时抛出异常
        """
        # 解析消息相关参数
        message_id = args.get('message_id', '')
        parent_message_id = args.get('parent_message_id', '')

        # 解析 at_list 参数（如果是JSON字符串）
        at_list_str = args.get('at_list', '[]')
        at_list: List[str] = []

        if at_list_str:
            try:
                at_list = json.loads(at_list_str)
                if not isinstance(at_list, list):
                    at_list = []
            except (json.JSONDecodeError, ValueError):
                # 如果解析失败，使用空列表
                at_list = []

        return cls(
            message_id=message_id,
            parent_message_id=parent_message_id,
            at_list=at_list,
        )

    def validate(self) -> None:
        """验证请求参数的有效性.

        Raises:
            ValueError: 当必要参数缺失时抛出异常
        """
        if not self.message_id:
            raise ValueError('缺少必要参数: message_id')

        # parent_message_id 可以与 message_id 相同，所以不验证是否为空
        # at_list 可以为空列表，所以不需要验证
