import zipfile
from io import BytesIO

from flask import Blueprint, request, send_file
from werkzeug.utils import secure_filename

from lib.configs import config
from lib.constants import STORE_ROOT
from lib.utils.size_utils import parse_size
from scripts.extract_pdf import extract_text_from_pdf, save_to_csv

bp = Blueprint('pdf', __name__)

LIMIT_PDF_MAX_SIZE = parse_size(config.limit_pdf_max_size)


@bp.route('/extract', methods=['POST'])
def extract_pdf():
    """Convert pdf file into csv."""
    request.max_content_length = LIMIT_PDF_MAX_SIZE

    # Check content length
    content_length = request.content_length
    if content_length is not None and content_length > request.max_content_length:
        return {
            'error': (
                f'Payload too large: {content_length / (1 << 20):.2f} MB '
                f'(limit: {config.limit_pdf_max_size})'
            )
        }, 413

    if 'files' not in request.files:
        return {'error': 'No files uploaded'}, 400

    files = request.files.getlist('files')
    if not files:
        return {'error': 'No selected files'}, 400

    # Validate files
    for file in files:
        if file.filename == '':
            return {'error': 'One or more files have no filename'}, 400
        if not file.filename.lower().endswith('.pdf'):
            return {'error': 'All files must be PDFs'}, 400

    # Save uploaded files and process
    STORE_ROOT.mkdir(exist_ok=True)
    csv_files = []

    try:
        # Create in-memory zip file
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file in files:
                # Save and process each PDF
                pdf_path = STORE_ROOT / secure_filename(file.filename)
                file.save(pdf_path)

                try:
                    # Extract text and save as CSV
                    lines = extract_text_from_pdf(pdf_path)
                    csv_filename = f'{pdf_path.stem}.csv'
                    csv_path = STORE_ROOT / csv_filename
                    csv_content = '\n'.join(lines)
                    save_to_csv(csv_content, csv_path)

                    # Add CSV to zip
                    zip_file.write(csv_path, csv_path.name)
                    csv_files.append(csv_path)
                finally:
                    # Clean up PDF file
                    if pdf_path.exists():
                        pdf_path.unlink()

        # Prepare zip file for download
        zip_buffer.seek(0)
        return send_file(
            zip_buffer,
            mimetype='application/zip',
            as_attachment=True,
            download_name='extracted_csv_files.zip',
        )
    except Exception as e:
        print(f'exception: {str(e)}')
        return {'error': str(e)}, 500
    finally:
        # Clean up CSV files
        for csv_file in csv_files:
            if csv_file.exists():
                csv_file.unlink()
