import httpx

from log import logger

# const
# 开放接口 URI
TENANT_ACCESS_TOKEN_URI = '/open-apis/auth/v3/tenant_access_token/internal'
JSAPI_TICKET_URI = '/open-apis/jssdk/ticket/get'


class Auth:
    """飞书应用认证类.

    用于获取飞书应用的租户访问令牌和JSAPI票据，
    支持前端SDK的鉴权配置。
    """

    def __init__(self, feishu_host: str, app_id: str, app_secret: str) -> None:
        """初始化飞书认证实例.

        Args:
            feishu_host: 飞书开放平台域名
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
        """
        self.feishu_host = feishu_host
        self.app_id = app_id
        self.app_secret = app_secret
        self.tenant_access_token = ''

    def get_ticket(self) -> str:
        """获取飞书JSAPI ticket用于前端SDK鉴权.

        Returns:
            str: 飞书JSAPI ticket字符串
        """
        # 获取jsapi_ticket，具体参考文档：https://open.feishu.cn/document/ukTMukTMukTM/uYTM5UjL2ETO14iNxkTN/h5_js_sdk/authorization
        self.authorize_tenant_access_token()
        url = '{}{}'.format(self.feishu_host, JSAPI_TICKET_URI)
        headers = {
            'Authorization': 'Bearer ' + self.tenant_access_token,
            'Content-Type': 'application/json',
        }

        with httpx.Client() as client:
            resp = client.post(url=url, headers=headers)
            Auth._check_error_response(resp)
            return resp.json().get('data').get('ticket', '')

    def authorize_tenant_access_token(self) -> None:
        """获取飞书租户访问令牌.

        获取tenant_access_token，基于开放平台能力实现，
        具体参考文档：https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/tenant_access_token_internal
        """
        url = '{}{}'.format(self.feishu_host, TENANT_ACCESS_TOKEN_URI)
        req_body = {'app_id': self.app_id, 'app_secret': self.app_secret}

        with httpx.Client() as client:
            response = client.post(url, json=req_body)
            Auth._check_error_response(response)
            self.tenant_access_token = response.json().get('tenant_access_token')

    @staticmethod
    def _check_error_response(resp: httpx.Response) -> None:
        """检查HTTP响应是否包含错误信息.

        Args:
            resp: httpx响应对象

        Raises:
            httpx.HTTPStatusError: HTTP状态码错误时抛出
            FeishuException: 飞书API返回错误码时抛出
        """
        # 检查HTTP状态码
        if resp.status_code != 200:
            resp.raise_for_status()

        # 检查飞书API返回的业务错误码
        response_dict = resp.json()
        code = response_dict.get('code', -1)
        if code != 0:
            logger.error(response_dict)
            raise FeishuException(code=code, msg=response_dict.get('msg'))


class FeishuException(Exception):
    """飞书API异常类.

    用于处理并展示飞书API返回的错误码和错误信息。
    """

    def __init__(self, code: int = 0, msg: str = None) -> None:
        """初始化飞书异常.

        Args:
            code: 飞书API错误码
            msg: 飞书API错误信息
        """
        self.code = code
        self.msg = msg
        super().__init__(f'{code}:{msg}')

    def __str__(self) -> str:
        """返回异常的字符串表示."""
        return f'{self.code}:{self.msg}'

    __repr__ = __str__
