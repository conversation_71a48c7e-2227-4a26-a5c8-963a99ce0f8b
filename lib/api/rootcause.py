import hashlib
import os
import time

from flask import Blueprint, Response, jsonify, request

from lib.configs import config
from tasks.argus import group_chat

from .auth import Auth
from .fields.rootcause import QuickRootCauseRequest

# 常量定义
# 随机字符串，用于签名生成加密使用
NONCE_STR = 'KdxsVdCSgf3GlzDPmCQ5SkcWvh9o6riq'


# 获取环境变量
APP_CONFIG = config.lark.apps['argus']
APP_ID = APP_CONFIG.app_id
APP_SECRET = APP_CONFIG.app_secret
FEISHU_HOST = os.getenv('FEISHU_HOST', 'https://open.feishu.cn')  # 默认值为飞书开放平台地址

# 用获取的环境变量初始化Auth类，由APP ID和APP SECRET获取access token，进而获取jsapi_ticket
auth = Auth(FEISHU_HOST, APP_ID, APP_SECRET)

bp = Blueprint('argus', __name__)


@bp.route('', methods=['GET'])
def quick_rootcause() -> Response:
    """Quick root cause analysis endpoint.

    解析根因分析所需的关键参数，包括：
    - message_id: 消息ID
    - parent_message_id: 父消息ID
    - at_list: @用户列表

    Returns:
        Response: 处理结果响应
    """
    try:
        # 使用 dataclass 解析请求参数
        req = QuickRootCauseRequest.from_request_args(request.args)

        # 验证请求参数
        req.validate()

        group_chat.apply_async(
            kwargs={
                'content': '',
                'message_id': req.message_id,
                'parent_id': req.parent_message_id,
                'at_list': req.at_list,
            },
        )

        return Response('已发起诊断', 200)

    except ValueError as e:
        # 参数解析或验证错误
        return Response(str(e), 400)
    except Exception as e:
        # 其他异常
        return Response(f'参数解析异常: {e}', 500)


@bp.route('/lark_auth_config', methods=['GET'])
def get_lark_auth_config() -> Response:
    """获取飞书（Lark）前端SDK鉴权配置参数接口.

    为前端飞书SDK提供必要的鉴权配置参数，包括：
    - appid: 飞书应用ID
    - signature: 基于jsapi_ticket生成的签名
    - noncestr: 随机字符串
    - timestamp: 当前时间戳

    Query Parameters:
        url (str): 需要鉴权的网页URL

    Returns:
        Response: 包含飞书鉴权参数的JSON响应
    """
    try:
        # 接入方前端传来的需要鉴权的网页URL
        url = request.args.get('url')
        if not url:
            return Response('缺少必需的url参数', 400)

        # 获取飞书jsapi_ticket用于生成签名
        ticket = auth.get_ticket()
        # 当前时间戳，毫秒级
        timestamp = int(time.time()) * 1000
        # 按照飞书要求拼接签名字符串
        verify_str = 'jsapi_ticket={}&noncestr={}&timestamp={}&url={}'.format(
            ticket, NONCE_STR, timestamp, url
        )
        # 对字符串做SHA1加密，生成飞书要求的签名
        signature = hashlib.sha1(verify_str.encode('utf-8')).hexdigest()

        # 返回飞书前端SDK所需的鉴权配置参数
        lark_auth_config = {
            'appid': APP_ID,
            'signature': signature,
            'noncestr': NONCE_STR,
            'timestamp': timestamp,
        }

        return jsonify(lark_auth_config)

    except Exception as e:
        # 异常处理
        return Response(f'获取飞书鉴权配置异常: {e}', 500)
