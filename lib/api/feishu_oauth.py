from flask import Blueprint, Response, request

from lib.configs import config
from log import logger
from tasks.oauth import authorized

bp = Blueprint('oauth', __name__)


@bp.route('', methods=['GET'])
def activate_feishu_oauth():
    """Activate Feishu OAuth."""
    callback_code = request.args.get('code')
    error_message = request.args.get('error')
    state = request.args.get('state', '')

    if not callback_code:
        return Response(f'missing code, {error_message}', 400)

    logger.debug(f'state is {state}, code is {callback_code}')

    # NOTE: 开启异步任务，开始user_access_token的生成和定期维护
    app_name = config.lark.service_account.app_name
    app = config.lark.apps[app_name]
    app_id = app.app_id
    app_secret = app.app_secret

    # 用 signature immutable(si) 而不是 apply_async(立即实例化会导致无法JSON化)
    # 这样 refresh_user_access_token 会在 authorized 任务成功完成后自动执行
    authorized.apply_async(
        args=[callback_code, app_id, app_secret],
        # link=refresh_user_access_token.si().set(countdown=10),
    )

    return Response('授权已发起', 200)
