from functools import wraps
from typing import Any, Optional

from flask import request, url_for
from flask_restful import marshal


def marshal_response(fields: Optional[Any] = None, show_endpoint: bool = False):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            result = f(*args, **kwargs)

            if isinstance(result, str):
                response = {'status': 'ok', 'data': result}
            else:
                data = marshal(result, fields)
                response = {
                    'status': 'ok',
                    'data': data,
                }

            if show_endpoint:
                endpoint = request.endpoint
                qs = request.view_args
                response['url'] = url_for(endpoint, **qs)
            return response

        return wrapper

    return decorator
