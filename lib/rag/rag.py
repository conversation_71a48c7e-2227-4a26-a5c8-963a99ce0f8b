"""Document loader for RAG.

This module provides a document loader for RAG. It is responsible for loading,
more details can be found at: https://python.langchain.com/docs/tutorials/rag/

workflow: Load -> Split -> Store -> Retrieve -> Generate
"""

from langchain_chroma import Chroma
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters.markdown import (
    MarkdownHeaderTextSplitter,
)

from lib.constants import KNOWLEDGE_SHARE_ROOT
from lib.llm.models import get_embedding, get_llm
from lib.llm.prompt import get_content, get_prompt
from lib.utils.printer import debug

llm = get_llm('gpt4o')
embedding = get_embedding('text_embedding_3_large')

# Load, chunk and index the contents of the blog.
# loader = WebBaseLoader(
#     web_paths=('https://lilianweng.github.io/posts/2023-06-23-agent/',),
#     bs_kwargs=dict(
#         parse_only=bs4.SoupStrainer(
#             class_=('post-content', 'post-title', 'post-header')
#         )
#     ),
# )
# docs = loader.load()

# loader2 = TextLoader(KS_ROOT / 'game.txt')
# loader2 = TextLoader(
#     KS_ROOT / '故障报告-20240518-平台消费kafka异常.txt', encoding='utf-8'
# )
# docs = loader2.load()

markdown_document = get_content(
    '故障报告-20240518-平台消费kafka异常', KNOWLEDGE_SHARE_ROOT
)

# text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
# text_splitter = MarkdownTextSplitter()
text_splitter = MarkdownHeaderTextSplitter(
    headers_to_split_on=[
        ('#', 'Header 1'),
        ('##', 'Header 2'),
    ],
    strip_headers=False,
)
splits = text_splitter.split_text(markdown_document)
# splits = text_splitter.split_documents(docs)

vectorstore = Chroma.from_documents(documents=splits, embedding=embedding)

# Retrieve and generate using the relevant snippets of the blog.
retriever = vectorstore.as_retriever(search_kwargs={'k': 2})
prompt = get_prompt('rag-prompt')


def format_docs(docs):
    for doc in docs:
        debug('doc.page_content:\n', color='green')
        debug(doc.page_content)
    return '\n\n'.join(doc.page_content for doc in docs)


rag_chain = (
    {'context': retriever | format_docs, 'question': RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)

# response = rag_chain.invoke('What is Task Decomposition?')
# response = rag_chain.invoke('什么是任务分解？')
# response = rag_chain.invoke('什么是反思？')
# print(response)

for chunk in rag_chain.stream(
    '今天是 2024-10-29, kafka 最近一次故障是什么,  根因是什么?'
):
    print(chunk, end='', flush=True)

vectorstore.delete_collection()
