"""Document loader for RAG.

This module provides a document loader for RAG. It is responsible for loading,
more details can be found at: https://python.langchain.com/docs/tutorials/rag/

workflow: Load -> Split -> Store --> Retrieve --> Generate
"""

from langchain_chroma import Chroma
from langchain_community.document_loaders import TextLoader
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters.character import RecursiveCharacterTextSplitter

from lib.constants import DATA_ROOT
from lib.llm.models import get_embedding, get_llm
from lib.llm.prompt import get_prompt
from lib.utils.printer import debug

llm = get_llm('x')
embedding = get_embedding('text_embedding_3_large')

# Load, chunk and index the contents of the blog.
# loader = WebBaseLoader(
#     web_paths=('https://lilianweng.github.io/posts/2023-06-23-agent/',),
#     bs_kwargs=dict(
#         parse_only=bs4.SoupStrainer(
#             class_=('post-content', 'post-title', 'post-header')
#         )
#     ),
# )
# docs = loader.load()

docs = TextLoader(DATA_ROOT / 'reflection.txt', encoding='utf-8').load()

text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
splits = text_splitter.split_documents(docs)

vectorstore = Chroma.from_documents(documents=splits, embedding=embedding)

# Retrieve and generate using the relevant snippets of the blog.
retriever = vectorstore.as_retriever(search_kwargs={'k': 2})
prompt = get_prompt('rag-prompt')


def format_docs(docs):
    for doc in docs:
        debug('doc.page_content:\n', color='green')
        debug(doc.page_content)
    return '\n\n'.join(doc.page_content for doc in docs if '反思' in doc.page_content)


rag_chain = (
    {'context': retriever | format_docs, 'question': RunnablePassthrough()}
    | prompt
    | llm
    # | StrOutputParser()
)

# response = rag_chain.invoke('What is Task Decomposition?')
# response = rag_chain.invoke('什么是任务分解？')
# response = rag_chain.invoke('什么是反思？')
# print(response)

# for chunk in rag_chain.stream('9.11 和 9.9 哪个大?'):
#     print(chunk, end='', flush=True)

response = rag_chain.invoke('9.11 和 9.9 哪个大?')
print(response)

vectorstore.delete_collection()
