import json
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent
CONFIG_ROOT = PROJECT_ROOT / 'configs'
STORE_ROOT = PROJECT_ROOT / 'store'
PROMPT_ROOT = PROJECT_ROOT / 'prompts'
DATA_ROOT = PROJECT_ROOT / 'data'
KNOWLEDGE_SHARE_ROOT = PROJECT_ROOT / 'knowledge'

GITHUB_TEAMS = [
    'Hgame',
    'iGame',
    'Mona',
    'Dgame',
    'Partopia',
    'Party IP',
    'QA',
    'Sgame',
    'Farlight84',
    'Wgame',
    'Xgame',
    'Zgame',
    'Pgame',
    '研发中台',
    '发行技术与数据中台',
    '内控',
    '发行中心',
    'Dislyte',
    '质量管理中心',
    'IP中台',
]


def format_message_card(template_id: str, as_str: bool = True, **template_vars):
    body = {
        'type': 'template',
        'data': {
            'template_id': template_id,
            'template_variable': template_vars,
        },
    }
    if as_str:
        return json.dumps(body)
    return body


def get_lark_post_message(contents: list, title: str = '', as_str: bool = False):
    template = {
        'zh_cn': {
            'title': title,
            'content': contents,
        }
    }
    if as_str:
        return json.dumps(template)
    return template


def get_lark_card_template(body: str, footer: str = '', as_str: bool = False):
    template = {
        'config': {'update_multi': True},
        'i18n_elements': {
            'zh_cn': [
                {
                    'tag': 'markdown',
                    'content': body,
                    'text_align': 'left',
                    'text_size': 'normal',
                },
                # {
                #     'tag': 'column_set',
                #     'flex_mode': 'none',
                #     'horizontal_spacing': 'default',
                #     'background_style': 'default',
                #     'columns': [
                #         {
                #             'tag': 'column',
                #             'elements': [
                #                 {
                #                     'tag': 'div',
                #                     'text': {
                #                         # 'tag': 'plain_text',
                #                         'tag': 'lark_md',
                #                         'content': body,
                #                         'text_size': 'normal',
                #                         'text_align': 'left',
                #                         'text_color': 'default',
                #                     },
                #                 }
                #             ],
                #             'width': 'weighted',
                #             'weight': 1,
                #         }
                #     ],
                # },
                {'tag': 'hr'},
                {
                    'tag': 'column_set',
                    'flex_mode': 'none',
                    'horizontal_spacing': 'default',
                    'background_style': 'default',
                    'columns': [
                        {
                            'tag': 'column',
                            'elements': [
                                {
                                    'tag': 'div',
                                    'text': {
                                        'tag': 'plain_text',
                                        'content': footer,
                                        'text_size': 'notation',
                                        'text_align': 'left',
                                        'text_color': 'grey',
                                    },
                                    'icon': {
                                        'tag': 'standard_icon',
                                        'token': 'robot_outlined',
                                        'color': 'grey',
                                    },
                                }
                            ],
                            'width': 'weighted',
                            'weight': 1,
                        }
                    ],
                },
            ]
        },
        'i18n_header': {},
    }
    if as_str:
        return json.dumps(template)
    return template


def get_error_lark_template(msg: str, detail: str, as_str: bool = False):
    body = {
        'zh_cn': {
            'content': [
                # 第一行
                [
                    {
                        'tag': 'emotion',
                        'emoji_type': 'SHOCKED',
                    },
                    {
                        'tag': 'text',
                        'text': msg,
                        'style': ['bold'],
                    },
                ],
                # 第二行
                [{'tag': 'hr'}],
                # 第三行
                [
                    {
                        'tag': 'code_block',
                        'language': 'python',
                        'text': detail,
                    }
                ],
            ],
        }
    }
    if as_str:
        return json.dumps(body)
    return body
