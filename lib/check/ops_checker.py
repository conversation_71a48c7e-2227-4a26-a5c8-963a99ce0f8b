import sys
import traceback
from concurrent import futures

from lark_oapi.api.bitable.v1 import AppTableRecord

from lib.clients.dify import FileObject, WorkflowClient
from lib.clients.lark import Bitable
from lib.constants import STORE_ROOT
from lib.screenshot import <PERSON>anaScreenShot, remote_driver
from lib.screenshot.paths import oma_monitor_replacer
from lib.utils import timerange
from lib.utils.template import render_template

from .base import BaseChecker


class OpsChecker(BaseChecker):
    """<PERSON>s<PERSON><PERSON><PERSON> is drop-in replacement for <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>"""

    def process_record(self, app: Bitable, record: AppTableRecord):
        last_result = None
        for new_record_data in self.iter_process_records(app, record):
            if last_result is None:
                last_result = new_record_data
            else:
                last_result.update(new_record_data)
        return last_result

    def iter_process_records(self, app: Bitable, record: AppTableRecord):
        # TODO: 分组并发处理, 临时文件链接最多一次性处理 5 个
        fields = record.fields

        content = ''.join(t['text'] for t in fields.get('巡检内容'))
        standard = ''.join(t['text'] for t in fields.get('巡检标准'))
        source = fields.get('数据来源').get('link')
        source_text = fields.get('数据来源').get('text')
        # timeranged_string = fields.get('时间范围').get('text')

        print(f'content: {content}\nstandard: {standard}')
        print(f'source: {source}')

        file_token = None
        try:
            # 1. 截图到本地
            with GrafanaScreenShot(remote_driver()) as shot:
                img_path, meta = shot.take(
                    source,
                    STORE_ROOT,
                    timeout=10,
                    domain_replacer=oma_monitor_replacer,
                )
                if not img_path:
                    raise Exception(f'take shot of {source} failed, {meta}')

            ## 2. 上传图片到飞书
            file_token = app.upload_local_image(img_path)

            # 2.1 更新截图到飞书记录
            record_data = {
                '截图': [{'file_token': file_token}],
                '更新日期': timerange.now_ns(),
            }
            if meta:
                record_data['数据来源'] = {
                    'text': render_template(source_text, meta),
                    'link': source,
                }

            yield record_data

            ## 3. 获取图片的飞书临时链接(不能开启高级权限, 或者需要 extra 参数)
            img_tmp_url = self._client.get_tmp_urls([file_token]).get(file_token, '')
            assert img_tmp_url, f'get tmp url for {file_token} failed'

            ## 4. 分析图片中的数据
            dify_host = self._config.get('DIFY_HOST', '')
            dify_app_token = self._config.get('DIFY_DETECT_IMAGE_TOKEN')
            dify_user = self._config.get('DIFY_USER', 'ops-checker')

            wf_client = WorkflowClient(dify_app_token, dify_host)
            image_response = wf_client.run(
                inputs={},
                response_mode='blocking',
                user=dify_user,
                files=[
                    # {
                    #     'type': 'image',
                    #     'transfer_method': 'remote_url',
                    #     'url': img_tmp_url,
                    # }
                    FileObject(
                        file_type='image',
                        transform_method='remote_url',
                        url=img_tmp_url,
                    ),
                ],
            )
            assert image_response.ok, f'run detect_image failed: {image_response.text}'

            image_result = image_response.json()
            print(f'image_result is : {image_result}')
            data_from_image = image_result['data']['outputs']['result']

            yield {
                '分析信息': data_from_image,
                '更新日期': timerange.now_ns(),
            }

            ## 5. 分析数据
            dify_analyze_app_token = self._config.get('DIFY_ANALYZE_TOKEN')
            wf_analyze_client = WorkflowClient(dify_analyze_app_token, dify_host)
            analyze_response = wf_analyze_client.run(
                inputs={
                    'standard': standard,
                    'data': data_from_image,
                    # 'query': content,
                },
                response_mode='blocking',
                user=dify_user,
            )
            assert analyze_response.ok, (
                f'run analyze failed, '
                f'token {dify_analyze_app_token},'
                f'host: {dify_host}: {analyze_response.text}'
            )
            analyze_outputs = analyze_response.json()['data'].get('outputs', {})
            print(f'analyze_result is : {analyze_outputs}')

            ## 6. 更新记录
            record_data = {
                '巡检结果': analyze_outputs.get('result', '巡检失败').strip(),
                '结果说明': analyze_outputs.get('reason', '没有输出').strip(),
                '更新日期': timerange.now_ns(),
            }

            yield record_data
        except Exception as e:
            traceback.print_exc(limit=5, file=sys.stderr)

            record_data = {
                '巡检结果': '巡检失败',
                '结果说明': str(e),
                '更新日期': timerange.now_ns(),
            }
            yield record_data

    def check_record(self, app: Bitable, table_id: str, record: AppTableRecord):
        # update every step
        for new_record_data in self.iter_process_records(app, record):
            app.update_record(
                table_id=table_id,
                record_id=record.record_id,
                new_fields=new_record_data,
            )

    def check(self, app: 'Bitable', table_id: str):
        # get table's first view
        views = app.views(table_id, 'grid')
        if not views:
            print(f'no view found in table: {table_id}')
            return

        first_view = views[0]
        print(f'check from view: {first_view.view_id} {first_view.view_name}')

        records_it = app.iter_records(table_id=table_id, view_id=first_view.view_id)
        for records in records_it:
            # 分组并发处理, 临时文件链接最多一次性处理 5 个
            with futures.ThreadPoolExecutor(max_workers=5) as executor:
                tasks = []
                for record in records:
                    tasks.append(
                        executor.submit(self.check_record, app, table_id, record)
                    )

                for f in futures.as_completed(tasks):
                    try:
                        f.result()
                    except Exception as e:
                        print(f'check record failed: {e}')
