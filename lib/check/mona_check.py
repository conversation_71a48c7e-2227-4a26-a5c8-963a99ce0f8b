import sys
import traceback
from concurrent import futures

from lark_oapi.api.bitable.v1 import AppTableRecord

from lib.agents import comparison, complicant
from lib.clients.lark import Bitable
from lib.constants import STORE_ROOT
from lib.llm.models import get_llm
from lib.screenshot import remote_driver
from lib.screenshot.grafana import GrafanaTableScreenShot
from lib.screenshot.paths import oma_monitor_replacer
from lib.utils import timerange
from lib.utils.template import render_template
from log import logger

from .base import BaseChecker

model_name = 'deepseek-v3'
anaysis_model = 'deepseek-r1-0528'


class MonaChecker(BaseChecker):
    def process_record(self, app: Bitable, record: AppTableRecord):
        last_result = None
        for new_record_data in self.iter_process_records(app, record):
            if last_result is None:
                last_result = new_record_data
            else:
                last_result.update(new_record_data)
        return last_result

    def iter_process_records(self, app: Bitable, record: AppTableRecord):
        fields = record.fields

        inspection_description: str = ''.join(t['text'] for t in fields.get('巡检方法'))
        source: str = fields.get('数据源').get('link')
        source_text: str = fields.get('数据源').get('text')

        print(f'inspection: {inspection_description}')
        print(f'source: {source}')

        try:
            # 1. 解析巡检策略
            strategy = comparison.invoke(inspection_description, model_name=model_name)

            left_range = [timerange.timestamp_ms(t) for t in strategy.period_previous]
            right_range = [timerange.timestamp_ms(t) for t in strategy.period_current]

            # 2. 更新数据源title、保存截图
            images_token = []
            metric_values = []

            for t_range in [left_range, right_range]:
                # with PlaywrightScreenShot() as shoter:
                #     remote_url = update_path(
                #         source,
                #         new_qs={'from': t_range[0], 'to': t_range[1]},
                #     )
                #     img_saved_path = STORE_ROOT / f'{time.time_ns()}.png'
                #     img_saved_path.parent.mkdir(parents=True, exist_ok=True)
                # results = shoter.take(
                #     remote_url,
                #     img_saved_path.as_posix(),
                #     parser=table_parser,
                #     wait_selector='#pageContent .scrollbar-view table',
                # )

                #     # 更新数据源link、截图
                #     images_token.append(dict(file_token=app.upload_local_image(img_saved_path)))

                #     url_context = urlparam_to_dict(remote_url)
                #     url_context.update(results)
                #     yield {
                #         '截图': images_token,
                #         '数据源': {
                #             'text': render_template(source_text, url_context),
                #             'link': source,
                #         },
                #     }

                #     # 提取 metric 数据
                #     metric_values.append(results.pop('value', ''))

                with GrafanaTableScreenShot(remote_driver()) as shot:
                    img_path, meta = shot.take(
                        source,
                        STORE_ROOT,
                        qs={'from': t_range[0], 'to': t_range[1]},
                        domain_replacer=oma_monitor_replacer,
                    )
                    if not img_path:
                        raise Exception(f'take shot of {source} failed, {meta}')
                    images_token.append(dict(file_token=app.upload_local_image(img_path)))
                    yield {'截图': images_token}

                    metric_values.append(meta.pop('value', ''))

            # 更新数据源link
            if meta:
                yield {
                    '数据源': {
                        'text': render_template(source_text, meta),
                        'link': source,
                    }
                }

            # 3. 保存巡检数据
            analysis_data = (
                f'[{"~".join(strategy.period_previous)}]\n'
                f'{metric_values[0]}\n\n'
                f'[{"~".join(strategy.period_current)}]\n'
                f'{metric_values[1]}\n\n'
            )
            yield {'分析数据': analysis_data}

            ## 4. 数据分析
            complicant_result = complicant.invoke_by_reasoning(
                f'{inspection_description}\n\n{analysis_data}',
                get_llm(anaysis_model),
                stream=True,
            )

            ## 5. 保存最终巡检结果
            record_data = {
                '巡检结果': '正常' if complicant_result.complicant else '不正常',
                '巡检说明': f'{complicant_result.analysis_details}',
            }

            yield record_data
        except Exception as e:
            es = traceback.format_exc(limit=5, file=sys.stderr)
            logger.error('巡检失败', 'message', es)

            record_data = {
                '巡检结果': '巡检失败',
                '巡检说明': str(e),
            }
            yield record_data

    def check_record(self, app: Bitable, table_id: str, record: AppTableRecord):
        result_cache = {}

        # update every step
        for new_record_data in self.iter_process_records(app, record):
            new_record_data.setdefault('更新日期', timerange.now_ns())

            #  更新记录 cache
            result_cache.update(new_record_data)

            response = app.update_record(
                table_id=table_id,
                record_id=record.record_id,
                new_fields=new_record_data,
            )
            print(
                f'update record, code: {response.code}, msg: {response.msg}, '
                f'records: {new_record_data}'
            )
        return result_cache

    def check(self, app: 'Bitable', table_id: str):
        # get table's first view
        views = app.views(table_id, 'grid')
        if not views:
            print(f'no view found in table: {table_id}')
            return

        first_view = views[0]
        print(f'check from view: {first_view.view_id} {first_view.view_name}')

        record_gen = app.iter_records(table_id=table_id, view_id=first_view.view_id)
        with futures.ThreadPoolExecutor(max_workers=10) as executor:
            tasks = []

            for records in record_gen:
                for record in records:
                    tasks.append(executor.submit(self.check_record, app, table_id, record))

            for f in futures.as_completed(tasks):
                try:
                    f.result()
                except Exception as e:
                    print(f'check record failed: {e}')
