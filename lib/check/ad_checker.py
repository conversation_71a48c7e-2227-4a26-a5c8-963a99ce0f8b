import sys
import traceback
from concurrent import futures

from lark_oapi.api.bitable.v1 import AppTableRecord

from lib.agents import comparison
from lib.clients.lark import Bitable
from lib.constants import STORE_ROOT
from lib.screenshot import GrafanaTablelessScreenShot, remote_driver
from lib.utils import timerange
from lib.utils.template import render_template

from .base import BaseChecker

model_name = 'deepseek-v3'


class AdChecker(BaseChecker):
    def iter_process_records(self, app: Bitable, record: AppTableRecord):
        fields = record.fields

        inspection_content = ''.join(t['text'] for t in fields.get('巡检方法'))
        source = fields.get('数据源').get('link')
        source_text = fields.get('数据源').get('text')

        print(f'inspection: {inspection_content}')
        print(f'source: {source}')

        try:
            # 1. 巡检方法识别
            strategy = comparison.invoke(inspection_content, model_name=model_name)

            left_range = [timerange.timestamp_ms(t) for t in strategy.period_previous]
            right_range = [timerange.timestamp_ms(t) for t in strategy.period_current]

            # changed_type = strategy.metric_changed_type
            # changed_value = strategy.changed_value

            # 2. 截图到本地
            images = []
            metric_values = []

            for t_range in [left_range, right_range]:
                with GrafanaTablelessScreenShot(remote_driver()) as shot:
                    img_path, meta = shot.take(
                        source,
                        STORE_ROOT,
                        qs={'from': t_range[0], 'to': t_range[1]},
                    )
                    if not img_path:
                        raise Exception(f'take shot of {source} failed, {meta}')
                    images.append(img_path)
                    metric_values.append(meta.get('value'))

            ## 3. 上传图片到飞书
            file_tokens = [dict(file_token=app.upload_local_image(img)) for img in images]

            # 3.1 更新截图到飞书记录
            record_data = {
                '截图': file_tokens,
                '更新日期': timerange.now_ns(),
                '分析数据': (
                    f'巡检策略:\n {strategy}\n'
                    f'{"~".join(strategy.period_previous)}: {metric_values[0]}\n'
                    f'{"~".join(strategy.period_current)}: {metric_values[1]}\n'
                ),
            }
            if meta:
                record_data['数据源'] = {
                    'text': render_template(source_text, meta),
                    'link': source,
                }

            yield record_data

            ## 6. 更新记录
            record_data = {
                '巡检结果': '正常',
                '更新日期': timerange.now_ns(),
            }

            yield record_data
        except Exception as e:
            traceback.print_exc(limit=5, file=sys.stderr)

            record_data = {
                '巡检结果': '巡检失败',
                '巡检说明': str(e),
                '更新日期': timerange.now_ns(),
            }
            yield record_data

    def check_record(self, app: Bitable, table_id: str, record: AppTableRecord):
        # update every step
        for new_record_data in self.iter_process_records(app, record):
            response = app.update_record(
                table_id=table_id,
                record_id=record.record_id,
                new_fields=new_record_data,
            )
            print(
                f'update record, code: {response.code}, msg: {response.msg}, '
                f'records: {new_record_data}'
            )

    def check(self, app: 'Bitable', table_id: str):
        # get table's first view
        views = app.views(table_id, 'grid')
        if not views:
            return

        first_view = views[0]
        print(f'check from view: {first_view.view_id} {first_view.view_name}')

        records_it = app.iter_records(table_id=table_id, view_id=first_view.view_id)
        for records in records_it:
            # 分组并发处理, 临时文件链接最多一次性处理 5 个
            with futures.ThreadPoolExecutor(max_workers=5) as executor:
                tasks = []
                for record in records:
                    tasks.append(executor.submit(self.check_record, app, table_id, record))

                for f in futures.as_completed(tasks):
                    try:
                        f.result()
                    except Exception as e:
                        print(f'check record failed: {e}')
