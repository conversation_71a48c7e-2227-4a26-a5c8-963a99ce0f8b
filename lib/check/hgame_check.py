import sys
import traceback
from concurrent import futures

from lark_oapi.api.bitable.v1 import AppTableRecord

from lib.agents import complicant_task
from lib.clients.lark import Bitable
from lib.constants import STORE_ROOT
from lib.screenshot import GrafanaTableScreenShot, remote_driver
from lib.screenshot.paths import oma_monitor_replacer
from lib.utils import timerange
from lib.utils.template import render_template

from .base import BaseChecker


class HgameChecker(BaseChecker):
    def process_record(self, app: Bitable, record: AppTableRecord):
        last_result = None
        for new_record_data in self.iter_process_records(app, record):
            if last_result is None:
                last_result = new_record_data
            else:
                last_result.update(new_record_data)
        return last_result

    def iter_process_records(self, app: Bitable, record: AppTableRecord):
        fields = record.fields

        task_description = ''.join(t['text'] for t in fields.get('任务'))
        source = fields.get('数据源').get('link')
        source_text = fields.get('数据源').get('text')

        print(f'inspection: {task_description}')
        print(f'source: {source}')

        try:
            # 1. 识别任务
            strategy = complicant_task.invoke(task_description)

            right_range = [
                timerange.timestamp_ms(strategy.start_time),
                timerange.timestamp_ms(strategy.end_time),
            ]

            # 2. 分析数据、保存截图
            images_token = []
            metric_values = []

            for t_range in [right_range]:
                with GrafanaTableScreenShot(remote_driver()) as shot:
                    # with GrafanaTableScreenShot(local_driver()) as shot:
                    img_path, meta = shot.take(
                        source,
                        STORE_ROOT,
                        qs={'from': t_range[0], 'to': t_range[1]},
                        domain_replacer=oma_monitor_replacer,
                    )
                    if not img_path:
                        raise Exception(f'take shot of {source} failed, {meta}')
                    images_token.append(
                        dict(file_token=app.upload_local_image(img_path))
                    )
                    yield {'截图': images_token}

                    metric_values.append(meta.pop('value', ''))

            # 3. 更新数据
            anaysis_data = (
                f'[{strategy.start_time}~{strategy.end_time}]\n{metric_values[0]}\n\n'
            )
            record_data: dict = {'数据': anaysis_data}

            if meta:
                record_data['数据源'] = {
                    'text': render_template(source_text, meta),
                    'link': source,
                }

            yield record_data

            ## 4. 数据分析
            # complicant_result = complicant_plan.invoke(
            #     f'{inspection_description}\n\n{anaysis_data}'
            # )

            ## 5. 更新记录
            record_data = {
                # '结果': '正常' if complicant_result.complicant else '不正常',
                # '说明': f'{complicant_result.analysis_details}',
                '结果': '正常',
            }

            yield record_data
        except Exception as e:
            traceback.print_exc(limit=5, file=sys.stderr)

            record_data = {
                '结果': '失败',
                '说明': str(e),
            }
            yield record_data

    def check_record(self, app: Bitable, table_id: str, record: AppTableRecord):
        result_cache = {}

        # update every step
        for new_record_data in self.iter_process_records(app, record):
            new_record_data.setdefault('更新日期', timerange.now_ns())

            #  更新记录 cache
            result_cache.update(new_record_data)

            response = app.update_record(
                table_id=table_id,
                record_id=record.record_id,
                new_fields=new_record_data,
            )
            print(
                f'update record, code: {response.code}, msg: {response.msg}, '
                f'records: {new_record_data}'
            )
        print('result_cache:', result_cache)
        return result_cache

    def check(self, app: 'Bitable', table_id: str):
        # get table's first view
        views = app.views(table_id, 'grid')
        if not views:
            print(f'no view found in table: {table_id}')
            return '', f'views not found in table: {table_id}'

        first_view = views[0]
        print(f'check from view: {first_view.view_id} {first_view.view_name}')

        record_gen = app.iter_records(table_id=table_id, view_id=first_view.view_id)
        results = []
        err = None
        with futures.ThreadPoolExecutor(max_workers=10) as executor:
            tasks = []

            for records in record_gen:
                for record in records:
                    tasks.append(
                        executor.submit(self.check_record, app, table_id, record)
                    )

            for f in futures.as_completed(tasks):
                try:
                    results.append(f.result())
                except Exception as e:
                    print(f'check record failed: {e}')
                    err = str(e)
        return results, err
