from abc import ABC, abstractmethod
from typing import Optional

from lib.clients.lark import LarkClient


class AbstractChecker(ABC):
    @abstractmethod
    def check(self, app, table_id):
        raise NotImplementedError


class BaseChecker(AbstractChecker):
    def __init__(self, client: 'LarkClient', config: Optional[dict] = None):
        self._client = client
        self._config = config or {}
