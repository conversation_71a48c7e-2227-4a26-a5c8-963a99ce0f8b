from typing import List, Optional

import pendulum
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.prompts.chat import BaseMessagePromptTemplate
from langchain_core.tools import tool
from typing_extensions import Annotated

from lib.constants import DATA_ROOT
from lib.llm.models import get_llm
from lib.utils.printer import debug


@tool
def current_time(
    tz: Annotated[
        str, 'Must be a valid timezone from pytz.all_timezones'
    ] = 'Asia/Shanghai',
):
    """Return the current time and weekday."""
    now = pendulum.now(tz=tz)
    weekday = now.weekday() + 1
    return f'It is {now.to_datetime_string()} now, and weekday is {weekday}.'


@tool
def date_of(
    days: int,
    tz: Annotated[
        str, 'Must be a valid timezone from pytz.all_timezones'
    ] = 'Asia/Shanghai',
):
    """Return the datetime after days.

    Args:
        days: number of days to add/subtract, when negative, means after abs(days)
        tz: timezone, default is 'Asia/Shanghai'
    """
    d = pendulum.now(tz=tz).add(days=days)
    weekday = d.weekday() + 1
    return f'After {days} days, it is {d.to_datetime_string()}, weekday is {weekday}.'


@tool
def date_in_week(
    weeks: int,
    weekday: int,
    tz: Annotated[
        str, 'Must be a valid timezone from pytz.all_timezones'
    ] = 'Asia/Shanghai',
):
    """Return the datetime in specify week.

    Args:
        weeks: number of weeks to add/subtract, negative means week before abs(week)
        weekday: number of weekday, 0 is Monday and so on till 6 is Sunday
        tz: timezone, default is 'Asia/Shanghai'
    """
    d = pendulum.now(tz=tz).add(weeks=weeks).start_of('week').add(days=weekday)
    return f'After {weeks} weeks, it is {d.to_datetime_string()}, weekday is {weekday}.'


tools = [
    current_time,
    date_of,
    date_in_week,
]

tools_map = {tool.name: tool for tool in tools}

llm = get_llm('gpt4o').bind_tools(tools)


def read_feedback(filename: str):
    """
    读取反馈数据。

    :param filename: 反馈文件名
    :return: 反馈数据列表
    """
    with open(DATA_ROOT / filename, 'r') as f:
        feedbacks = f.readlines()
        return [feedback.strip() for feedback in feedbacks]


feedback_data = read_feedback('reflection_v3.txt')


def create_context(feedbacks):
    """
    将反馈整合为上下文。

    :param feedbacks: 反馈数据列表
    :return: 整合后的上下文字符串
    """
    return '\n'.join(feedbacks)


# SYSTEM_TEMPLATE = """You are a helpful ai robot \
# that can self-reflection from chat history and answer questions.
# chat history and self-reflections:
# ```
# {chat_history}
# ```
# """

SYSTEM_TEMPLATE = (
    'You are a helpful ai robot,'
    'that can self-reflection from chat history and answer questions.'
    'self-reflections and chat_history are as follows:'
    # 'You are an asistant for question-answering tasks. '
    # 'Use the following pieces of retrived context to answer the question.'
    # "If you don't known, say that you don't known. Use three sentences maximum and "
    # 'keep the answer concise.'
    '\n\n'
    'reflections:\n'
    '```{context}```'
)


def invoke(question: str, chat_history: Optional[List[BaseMessage]] = None):
    messages: List[BaseMessage | BaseMessagePromptTemplate] = [
        SystemMessage(SYSTEM_TEMPLATE.format(context=create_context(feedback_data))),
        MessagesPlaceholder('chat_history', optional=True),
        HumanMessage(question),
    ]

    for i in range(5):
        # ai_msg = llm.invoke(messages)

        prompt = ChatPromptTemplate.from_messages(messages)
        chain = prompt | llm
        ai_msg = chain.invoke({'chat_history': chat_history})

        messages.append(ai_msg)

        # tool calls in a round
        if ai_msg.tool_calls:
            for tool_call in ai_msg.tool_calls:
                selected_tool = tools_map.get(tool_call['name'].lower())
                if not selected_tool:
                    continue

                tool_msg = selected_tool.invoke(tool_call)
                messages.append(tool_msg)
        else:
            break

    for msg in messages:
        debug(msg, color='blue')
        print('-' * 20)

    return messages[-1]


# 3. 使用上下文生成新的回答
if __name__ == '__main__':
    while 1:
        question = input('请输入问题(或者输入 exit/quit/q 退出): ')
        if question.strip() in ['exit', 'q', 'quit'] or not question:
            break

        response = invoke(question)
        debug('Answer:', response, color='green')

        print('-' * 20)
        debug(
            f'total token: {response.response_metadata["token_usage"]}', color='green'
        )
