from langchain.chat_models.base import BaseChatModel
from langchain.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

from lib.constants import DATA_ROOT
from lib.llm.models import get_llm


class ReflectionGenerator(object):
    """
    反思生成器类，用于生成对话的反思内容。
    """

    def __init__(self, llm: BaseChatModel):
        """
        初始化反思生成器。

        :param llm: 语言模型实例
        """
        self.llm = llm
        self.prompt_template = PromptTemplate(
            input_variables=['dialogue'],
            template=(
                '请对以下对话内容进行反思, 并给出改进意见:\n'
                ' 对话内容: {dialogue}'
                ' 反思内容:\n1.\n2.\n3.\n'
            ),
        )

    def generate(self, dialogue):
        """
        生成反思内容。

        :param dialogue: 对话内容
        :return: 反思内容
        """
        chain = {'dialogue': RunnablePassthrough()} | self.prompt_template | self.llm
        return chain.invoke(dialogue)


if __name__ == '__main__':
    llm = get_llm('gpt4o')
    reflection_generator = ReflectionGenerator(llm)

    with open(DATA_ROOT / 'reflection.txt', 'r+') as f:
        dialogue = f.read()

        reflection = reflection_generator.generate(dialogue)
        print(reflection.content)

        f.write(f'\n\n{reflection.content}\n\n')
