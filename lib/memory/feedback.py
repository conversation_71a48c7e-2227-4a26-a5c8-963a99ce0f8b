from langchain.chains.llm import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate

from lib.constants import DATA_ROOT
from lib.llm.models import get_llm

template = """请回答一下问题: {question}"""

prompt = PromptTemplate(
    template=template,
    input_variables=['question'],
)

chain = LLMChain(llm=get_llm('gpt4o'), prompt=prompt, verbose=True)


def get_user_feedback(question, answer):
    print(f'Question: {question}')
    print(f'Answer: {answer}')
    feedback = input('请给这个回答打分(0-5): ')
    return int(feedback)


while 1:
    question = input('请输入问题(或者输入 exit 退出): ')
    if question == 'exit':
        break

    answer = chain.run(question)

    feedback = get_user_feedback(question, answer)

    with open(DATA_ROOT / 'feedback.txt', 'a') as f:
        f.write(f'Q: {question}\nA: {answer}\nScore: {feedback}\n\n')
    print('感谢您的反馈!')
