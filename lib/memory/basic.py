from langchain.chains.llm import <PERSON><PERSON><PERSON>n
from langchain.memory import Conversation<PERSON>ufferMemory
from langchain.prompts import PromptTemplate

from lib.constants import DATA_ROOT
from lib.llm.models import get_llm

template = """You are a chatbot having a conversation with a human.
{chat_history}

Human: {human_input}
Chatbot:"""

prompt = PromptTemplate(
    template=template,
    input_variables=['chat_history', 'human_input'],
)
memory = ConversationBufferMemory(memory_key='chat_history')

chain = LLMChain(
    llm=get_llm('gpt4o'),
    prompt=prompt,
    verbose=True,
    memory=memory,
)


reflection_template = """
作为 AI 助手, 我需要你对以下之前的回答进行深入的反思，并提出改进意见, 如果足够好了那么改进意见就是空.

请在反思时使用以下格式:
反思:
[{user_input}]

改进意见:
[你的改进意见, 犯错的地方和改正的方法]
"""


def reflection(user_input):
    reflection_chain = <PERSON><PERSON>hain(
        llm=get_llm('gpt4o'),
        prompt=PromptTemplate.from_template(reflection_template),
        verbose=True,
    )
    response = reflection_chain.run(user_input=user_input)
    return response


for i in range(10):
    human_input = input('> ')
    if human_input == 'exit':
        history = memory.buffer_as_str

        response = reflection(history)
        with open(DATA_ROOT / 'reflection.txt', 'a') as f:
            f.write(f'{history}\n{response}\n\n')
        break

    response = chain.predict(human_input=human_input)
    print(response)
