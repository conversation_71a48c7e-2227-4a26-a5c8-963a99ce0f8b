import re

from langchain.output_parsers import PydanticOutputParser


class MyJSONMarkdownParser(PydanticOutputParser):
    _json_markdown_re = re.compile(
        r'```(json)?\n(?P<json_data>\{.*\})\n?```',
        re.DOTALL,
    )

    def parse(self, text):
        try:
            return super().parse(text)
        except Exception as e:
            match = self._json_markdown_re.search(text)
            if match:
                data = match.group('json_data')
                print(f'valid json text is {data}')
                return super().parse(data)
            raise e
