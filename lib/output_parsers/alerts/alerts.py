from typing import List, Literal

from pydantic import BaseModel, Field

from lib.output_parsers.mixins import OutputParserMixin

AlertType = Literal[
    'cpu',
    'memory',
    'disk',
    'aliyun_alert',
    'aliyun_eip_alert',
    'other',
]

AlertList: List[AlertType] = [
    'cpu',
    'memory',
    'disk',
    'aliyun_alert',
    'aliyun_eip_alert',
    'other',
]


class BaseAlert(OutputParserMixin, BaseModel):
    alert_type: str = Field(
        alias='alert_type',
        description=(
            f'告警的类型, 只能在[{", ".join(AlertList)}]中的一个,'
            ' 如果不是告警, 请填写 other'
        ),
    )
