from ipaddress import IPv4Address
from typing import List, Self

from pydantic import Field, model_validator

from .alerts import BaseAlert


class DiskAlert(BaseAlert):
    cluster_name: str = Field(
        default='',
        alias='cluster_name',
        description='k8s集群实例名称, 只有在 alert_type 是 cpu, memory, disk 时才有值',
    )

    cluster_id: str = Field(
        default='',
        alias='cluster_id',
        description='k8s集群告警实例, 只有在 alert_type 是 cpu, memory, disk 时才有值',
    )

    nodes: List[IPv4Address] = Field(
        default=[],
        alias='node_ip_list',
        description='k8s集群中的node的IP列表, 只有在 alert_type 是 disk 时才有值',
    )

    def __str__(self):
        content_list = [
            f' 这是一个{self.alert_type}告警',
            f'节点列表: {", ".join(str(n) for n in self.nodes)}',
        ]

        return '\n'.join(content_list)

    @model_validator(mode='after')
    def validate_request(self) -> Self:
        assert self.cluster_name, 'missing cluster_name'
        assert self.cluster_id, 'missing cluster_id'
        assert self.nodes, 'missing nodes'

        return self
