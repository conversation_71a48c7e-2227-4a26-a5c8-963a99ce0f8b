from pydantic import BaseModel, Field

from .mixins import OutputParserMixin


class Complicant(OutputParserMixin, BaseModel):
    complicant: bool = Field(
        description=('是否合规, 是或否, 必须是 true 或 false 中的一个'),
    )

    function_type: str = Field(
        description='计算方法: 用哪一种值和阈值进行比较: 变化差值, 变化率',
    )

    reason: str = Field(
        description='如果判定为不合规, 描述原因, 不要超过150个字, 如果没有就填写""',
    )

    analysis_details: str = Field(
        description=(
            '列举所有项目的合规检查，格式按照: X(变化多少, 合规/不合规)\n'
            '例如:"- X(+1.2%, 合规)\n- X(-0.3%, 合规)",'
            '其中 X 是具体的项目名称,'
            '最终限制条数不大于10条。'
        ),
    )
