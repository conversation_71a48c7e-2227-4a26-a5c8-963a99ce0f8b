from typing import Optional

from pydantic import BaseModel, Field, model_validator
from typing_extensions import Self

from lib.constants import GITHUB_TEAMS

from .mixins import OutputParserMixin


class CopilotEmail(OutputParserMixin, BaseModel):
    apply_time: str = Field(description='申请时间, 格式: 2021-01-01')

    request_user: str = Field(description='申请人姓名')

    email: str = Field(description='申请人邮箱')

    department: str = Field(description='申请人部门全路径')

    github_team: str = Field(
        description=(f'根据department字段选择一个团队, 团队选项如下:[{", ".join(GITHUB_TEAMS)}]。')
    )

    github_user: str = Field(
        description=(
            '用户的 github 账号, 如果提供的是 https://github.com/XXX,'
            '那么 XXX 就是 github user; 如果是邮箱地址则保持不变'
        )
    )

    reason: str = Field(description='申请Copilot的用途')

    request_id: Optional[int] = Field(description='OA工单流程编号, 内容是数字')

    @model_validator(mode='after')
    def validate_request(self) -> Self:
        assert self.request_user, 'missing request_user'
        assert self.github_user, 'missing github_user'
        return self
