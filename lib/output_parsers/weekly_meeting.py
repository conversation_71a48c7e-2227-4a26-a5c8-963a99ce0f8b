from typing import Literal

from pydantic import BaseModel, Field

from .mixins import OutputParserMixin


class WeeklyMeetingOutputParser(BaseModel, OutputParserMixin):
    """周会Wiki"""

    title: str = Field(description='节点标题, 例如: MM/DD周会')
    link: str = Field(description='节点链接, 只有在创建成功或者已经存在时才有, 否则为空')
    wiki_path: str = Field(
        description='节点可读性路径, 例如: YYYY汇总/Q1汇总/T周会>, 其中 T 是 M/D周会的前缀, YYYY 是当前年份, Q1是当前季度'
    )
    success: Literal[True, False] = Field(description='是否成功创建, 例如: true 或者 false')
    message: str = Field(
        description="创建成功/失败的提示信息, 例如:  'new' 表示新建; 'exist' 表示无需创建，已经存在; 'failed with XXX' 表示因为某种原因失败"
    )
