from functools import lru_cache

from .parser import MyJSONMarkdownParser


class OutputParserMixin:
    """
    A mixin class to provide a method for getting a Pydantic output parser,
    must be a Pydantic model.
    """

    @classmethod
    @lru_cache()
    def get_parser(cls) -> MyJSONMarkdownParser:
        """
        Get a Pydantic output parser for the class.

        Returns:
            PydanticOutputParser: An instance of PydanticOutputParser initialized with
            the class.
        """
        return MyJSONMarkdownParser(pydantic_object=cls)

    @classmethod
    def parse(cls, text: str):
        return cls.get_parser().parse(text)

    @classmethod
    @lru_cache()
    def get_format_instructions(cls) -> str:
        """
        Get the format instructions for the output parser.

        Returns:
            str: The format instructions for the output parser.
        """
        return cls.get_parser().get_format_instructions()
