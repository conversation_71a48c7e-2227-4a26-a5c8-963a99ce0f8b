from typing import List, Literal

from pydantic import BaseModel, Field, IPvAnyAddress, model_validator
from typing_extensions import Self

from .mixins import OutputParserMixin

AlertType = Literal[
    'cpu',
    'memory',
    'disk',
    'aliyun_alert',
    'aliyun_eip_alert',
    'other',
]

AlertList: List[AlertType] = [
    'cpu',
    'memory',
    'disk',
    'aliyun_alert',
    'aliyun_eip_alert',
    'other',
]


class Alert(OutputParserMixin, BaseModel):
    alert_type: str = Field(
        alias='alert_type',
        description=f'告警的类型, 只能在[{", ".join(AlertList)}]中的一个, 如果不是告警, 请填写 other',
    )

    cluster_name: str = Field(
        default='',
        alias='cluster_name',
        description='k8s集群实例名称, 只有在 alert_type 是 cpu, memory, disk 时才有值',
    )

    cluster_id: str = Field(
        default='',
        alias='cluster_id',
        description='k8s集群告警实例, 只有在 alert_type 是 cpu, memory, disk 时才有值',
    )

    namespace: str = Field(
        default='',
        alias='namespace',
        description='k8s集群命名空间, 只有在 alert_type 是 cpu, memory 时才有值',
    )

    pod: str = Field(
        default='',
        alias='pod_name',
        description='k8s集群中的 pod 名字, 只有在 alert_type 是 cpu, memory 时才有值',
    )

    container: str = Field(
        default='',
        alias='container_name',
        description='k8s集群中的容器的名字, 只有在 alert_type 是 cpu, memory 时才有值',
    )

    # nodes: List[str] = Field(
    nodes: List[IPvAnyAddress] = Field(
        default=[],
        alias='node_ip_list',
        description='k8s集群中的node的IP列表, 只有在 alert_type 是 disk 时才有值',
    )

    ddos_ip: str = Field(
        default='',
        alias='ddos_ip',
        description='ddos攻击的IP, 只有在 alert_type 是 aliyun_alert 时才有值',
    )

    eip: str = Field(
        default='',
        alias='aliyun_eip',
        description='aliyun eip 告警中的实例IP, 只有在 alert_type 是 aliyun_eip_alert 时才有值',
    )

    eip_name: str = Field(
        default='',
        alias='aliyun_eip_name',
        description='aliyun eip 告警中的实例名字, 只有在 alert_type 是 aliyun_eip_alert 时才有值',
    )

    def __str__(self):
        content_list = [
            f' 这是一个{self.alert_type}告警',
        ]
        if self.alert_type in ['cpu', 'memory', 'disk']:
            content_list.extend(
                [
                    f'集群是: {self.cluster_name} (id: {self.cluster_id})',
                ]
            )

            if self.alert_type in ['cpu', 'memory']:
                content_list.extend(
                    [
                        f'命名空间: {self.namespace}',
                        f'pod 名字: {self.pod}',
                    ]
                )
            elif self.alert_type == 'disk':
                content_list.append(f'节点列表: {", ".join(str(n) for n in self.nodes)}')

        return '\n'.join(content_list)

    @model_validator(mode='after')
    def validate_request(self) -> Self:
        if self.alert_type in ['cpu', 'memory', 'disk']:
            assert self.cluster_name, 'missing cluster_name'
            assert self.cluster_id, 'missing cluster_id'

            if self.alert_type == 'disk':
                assert self.nodes, 'missing nodes'
            else:
                assert self.namespace, 'missing namespace'
                assert self.pod, 'missing pod'

        elif self.alert_type == 'aliyun_alert':
            assert self.ddos_ip, 'missing ddos_ip'

        elif self.alert_type == 'aliyun_eip_alert':
            assert self.eip, 'missing eip'

        return self
