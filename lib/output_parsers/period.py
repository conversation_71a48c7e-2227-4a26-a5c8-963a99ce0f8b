from typing import Literal, <PERSON><PERSON>

from pydantic import BaseModel, Field

from .mixins import OutputParserMixin

ChangedType = Literal['diff', 'ratio']


class Period(OutputParserMixin, BaseModel):
    period_previous: Tuple[str, str] = Field(
        # alias='上期',
        description=('上期的时间范围,时间格式: YYYY-mm-dd HH:MM:SS'),
    )

    period_current: Tuple[str, str] = Field(
        # alias='本期',
        description=('本期的时间范围,时间格式: YYYY-mm-dd HH:MM:SS'),
    )

    metric_changed_type: ChangedType = Field(
        # alias='计算方法',
        description=('计算变化的方法, 有2种: 变化差值(difference)、变化比率(ratio)'),
    )

    changed_value: str = Field(
        # alias='阈值',
        description=(
            '变化的具体数值, '
            '如"变化比率10%", 则阈值就是10%;'
            '如"变化差值100ms", 则是100ms 就是阈值;'
            '如果没有就是空字符串'
        ),
    )


class TimeRange(BaseModel, OutputParserMixin):
    start_time: str = Field(
        # alias='开始时间',
        description='开始时间, 时间格式: YYYY-mm-dd HH:MM:SS',
    )
    end_time: str = Field(
        # alias='结束时间',
        description='开始时间, 时间格式: YYYY-mm-dd HH:MM:SS',
    )


class Strategy(OutputParserMixin, BaseModel):
    period_previous: Tuple[str, str] = Field(
        # alias='上期',
        description=('上期的时间范围,时间格式: YYYY-mm-dd HH:MM:SS'),
    )

    period_current: Tuple[str, str] = Field(
        # alias='本期',
        description=('本期的时间范围,时间格式: YYYY-mm-dd HH:MM:SS'),
    )

    compare_info: str = Field(
        # alias='巡检策略',
        description=(
            '巡检的策略, '
            '包含数据的周期描述、 巡检的列、比较的方法和阈值信息, '
            '例如: 日环比,  最大值列的差值在10%以内'
        ),
    )
