from typing import Tuple

from pydantic import BaseModel, Field

from .mixins import OutputParserMixin


class CloudNotification(BaseModel, OutputParserMixin):
    """公有云通知"""

    timerange: str = Field(description='消息发生时间')
    content: str = Field(description='消息原文')
    summary: str = Field(description='消息摘要, 主要是提取出重要的内容, 不超过20个字')
    priority: Tuple[str, str] = Field(
        description='将消息按重要性和紧急性2个维度划分成四个象限: 重要/不重要, 紧急/不紧急'
    )
    priority_reason: str = Field(
        description='基于事实, 给出消息重要性和紧急性的分类理由, 而不是建议'
    )
    chat_group: str = Field(description='消息来源的群组名字')


class GroupedNotification(BaseModel, OutputParserMixin):
    """根据重要性和紧急性分组的总结性公有云通知


    例如:
    ```json
    {
        "important_urgent": [
            "[group_kind] ECS实例(实例1, 实例2) 将于 XX 日后到期, 请及时续费",
            "[group_kind] Kafka集群(集群1) 在 XXX 维护，请关注 XXX."
        ],
        "important_non_urgent": [
            "[group_kind] Redis实例(实例1, 实例2) 将于XXX维护，请关注 XXX."
        ],
        "urgent_non_important": [
            "[group_kind] ECS实例(实例3) 将于 XX 日后到期, 请及时续费"
        ],
        "non_urgent_non_important": []
    }
    ```
    """

    # priority: str = Field(
    #     description='重要性和紧急性分组, 重要紧急/重要不紧急/不重要紧急/不重要不紧急'
    # )
    # messages: List[str] = Field(description='消息列表')
    important_urgent: list[str] = Field(default_factory=list, description='重要紧急消息列表')
    important_non_urgent: list[str] = Field(default_factory=list, description='重要非紧急消息列表')
    urgent_non_important: list[str] = Field(default_factory=list, description='紧急不重要消息列表')
    non_urgent_non_important: list[str] = Field(
        default_factory=list, description='不紧急不重要消息列表'
    )
