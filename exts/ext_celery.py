from celery import Celery, Task
from flask import Flask


def init_app(app: Flask) -> Celery:
    """初始化 Celery 应用

    Args:
        app (Flask): Flask 应用实例

    Returns:
        Celery: 配置好的 Celery 应用实例
    """

    class FlaskTask(Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery_app = Celery(app.import_name, task_cls=FlaskTask)

    # 加载 celeryconfig.py 中的详细配置
    celery_app.config_from_object('celeryconfig')

    celery_app.set_default()
    app.extensions['celery'] = celery_app

    return celery_app
