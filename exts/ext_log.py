import logging
from operator import attrgetter

from flask import Flask

from lib.configs import config

COLOR_ENABLED = False
try:
    import colorlog

    COLOR_ENABLED = True
except ImportError:
    pass


def init_app(app: Flask):
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)

    if COLOR_ENABLED:
        console_handler.setFormatter(
            colorlog.ColoredFormatter(
                '%(log_color)s%(asctime)s - <%(name)s> - %(levelname)s - %(message)s',
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                },
            )
        )
    else:
        console_handler.setFormatter(
            logging.Formatter('%(asctime)s - <%(name)s> - %(levelname)s - %(message)s')
        )

    logging.basicConfig(
        level=logging.WARNING,
        handlers=[console_handler],
        force=True,
    )

    level = attrgetter(
        app.config.get('LOG_LEVEL', 'DEBUG' if config.debug else 'INFO')
    )(logging)

    logging.getLogger('api').setLevel(level)
    logging.getLogger('lib').setLevel(level)
    logging.getLogger('tasks').setLevel(level)
