openapi: 3.1.0
info:
  title: Get resource info from oma
  description: query oma resource.
  version: v1.0.0
servers:
  - url: https://oma-api.lilithgame.com/v2
paths:
  /api/cmdb/machine-document/:
    get:
      description: search machine info
      operationId: FuzzSearchMachine
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1
      responses:
        "200":
          description: successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SpecificResourceApiResponse"
  /api/cmdb/user-document/:
    get:
      description: search user info
      operationId: FuzzSearchUser
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/mysql-document/:
    get:
      description: search mysql database info
      operationId: FuzzSearchMySQL
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/domainrecord-document/:
    get:
      description: search domain record info
      operationId: FuzzSearchDomainRecord
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/lb-document/:
    get:
      description: search loadblance info
      operationId: FuzzSearchLoadBlance
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/redis-document/:
    get:
      description: search redis instances
      operationId: FuzzSearchRedis
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/hbase-document/:
    get:
      description: search hbase instances
      operationId: FuzzSearchHBase
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/logstore-document/:
    get:
      description: search logstore or sls instances
      operationId: FuzzSearchLogstore
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/polardb-document/:
    get:
      description: search polardb instances
      operationId: FuzzSearchPolardb
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/k8snode-document/:
    get:
      description: search kuberneates(k8s) nodes
      operationId: FuzzSearchK8sNode
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/k8scluster-document/:
    get:
      description: search kuberneates(k8s) clusters
      operationId: FuzzSearchK8sCluster
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1
        - name: offset
          in: query
          description: The offset to return next item
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/security-document/:
    get:
      description: search security groups
      operationId: FuzzSearchSecurityGroups
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

  /api/cmdb/exportip-document/:
    get:
      description: search export ip
      operationId: FuzzSearchExportIP
      parameters:
        - name: search
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
            default: 1

components:
  schemas:
    SpecificResourceApiResponse:
      type: object
      properties:
        count:
          type: integer
          format: int64
          example: 10
        results:
          type: array

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: Token
