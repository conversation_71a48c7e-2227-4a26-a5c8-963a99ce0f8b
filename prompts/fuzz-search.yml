openapi: 3.1.0
info:
  title: Get resource data from oma
  description: query oma resource.
  version: v1.0.0
servers:
  - url: https://oma-api.lilithgame.com
paths:
  /api/cmdb/multiple-search/:
    get:
      description: Fuzz search any resource
      operationId: FuzzSearchResource
      parameters:
        - name: query
          in: query
          description: The keyword to search
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: The total count to return
          required: false
          schema:
            type: integer
        - name: resource_type
          in: query
          description: The type of resource to search for
          required: false
          schema:
            type: string
            default: machine
            enum:
              - machine
              - redis
              - hbase
              - user
              - polardb
              - logstore
              - k8snode
              - k8scluster
              - domainrecord
              - lb
              - mysql
              - security

  components:
    securitySchemes:
      bearerAuth:
        type: http
        scheme: bearer
        bearerFormat: Token
