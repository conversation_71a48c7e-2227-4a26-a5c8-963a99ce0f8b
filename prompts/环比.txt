# 背景知识
{{context}}

# 任务描述
你的任务是分析 2 张 Grafana 监控页面截图, 并遵循"任务规则" 进行总结，总字数不多于200个.

# 任务规则
- 图片分成两张，第一张是{{left_time_range}}的数据，第二张是{{right_time_range}}的数据
- 每张图的指标(从上到下)分别是：{{metrics}}
- 按同时间段、同指标对比.
- 如果 A 指标峰值、低谷、线条形状和分布，发生在接近时间，且绝对值差距较小，就认为是"变化不大"
- 如果变化很大就按照"输出格式"请描述下重点，其中最好包括发生时间和变化幅度。
- 如果所有指标"变化不大"，就说: "所有指标变化不大"
- 如果有多个指标的结果，那么请分段，否则就一个段落。
- 发生时间请具体到日期和分钟
- 对于不确定的，也不要瞎猜， 瞎说, 闭嘴就好了。
- 确保你的分析公正客观，不依赖于刻板印象
- 请使用清晰且专业的语言。

# 输出格式
- 指标名称 **加粗**
- 变化幅度大的，请将标志名称 <font color='red'>红色文字</font> 标红

# 举例
原文: "节点内存使用在两个时间段内均保持稳定，无明显变化。"
希望: ""

原文: "小幅变化"
希望: ""

原文: "变化不大"
希望: ""

原文: "xxx指标变化很大，超过 80%"
希望: "<font color='red'>xxx 指标变化很大</font>，超过 80%"

原文: "网络带宽显著增加，峰值接近6MB/s"
希望: "<font color='red'>网络带宽显著增加</font>，峰值接近6MB/s"

原文: "A 指标变化不大，B 指标在某某时间到某某时间，变化很大，超过 90%"
希望: "<font color='red'>B 指标在**某某时间到某某时间**，变化很大</font>，超过 90%"

# 开始任务
请对图片分析，并输出分析结果。